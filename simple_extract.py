#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单解压工具：将 /data/phasetwo 目录下的压缩包解压到 /data/phaseone_data 目录
"""

import os
import zipfile
import tarfile
import shutil
from pathlib import Path


def extract_archives():
    """解压 /data/phasetwo 目录下的所有压缩包到 /data/phaseone_data"""
    
    source_dir = Path("/data/phasetwo")
    target_dir = Path("/data/phaseone_data")
    
    # 检查源目录是否存在
    if not source_dir.exists():
        print(f"错误: 源目录不存在 {source_dir}")
        return False
    
    # 创建目标目录
    target_dir.mkdir(parents=True, exist_ok=True)
    print(f"目标目录: {target_dir}")
    
    # 统计变量
    total_files = 0
    success_count = 0
    failed_files = []
    
    # 遍历源目录下的所有文件
    for file_path in source_dir.rglob('*'):
        if not file_path.is_file():
            continue
            
        file_name = file_path.name.lower()
        
        # 检查是否为压缩文件
        is_archive = False
        extract_func = None
        
        if file_name.endswith('.zip'):
            is_archive = True
            extract_func = extract_zip
        elif file_name.endswith(('.tar', '.tar.gz', '.tgz', '.tar.bz2', '.tbz2', '.tar.xz', '.txz')):
            is_archive = True
            extract_func = extract_tar
        
        if not is_archive:
            continue
            
        total_files += 1
        print(f"\n正在处理: {file_path}")
        
        # 创建解压子目录（基于文件名）
        base_name = file_path.stem
        if file_name.endswith('.tar.gz') or file_name.endswith('.tar.bz2') or file_name.endswith('.tar.xz'):
            base_name = Path(base_name).stem  # 移除 .tar 部分
        
        extract_path = target_dir / base_name
        extract_path.mkdir(parents=True, exist_ok=True)
        
        # 执行解压
        try:
            if extract_func(file_path, extract_path):
                print(f"✓ 解压成功: {file_path.name}")
                success_count += 1
            else:
                print(f"✗ 解压失败: {file_path.name}")
                failed_files.append(str(file_path))
                # 清理失败的目录
                if extract_path.exists() and not any(extract_path.iterdir()):
                    extract_path.rmdir()
        except Exception as e:
            print(f"✗ 解压异常: {file_path.name} - {e}")
            failed_files.append(str(file_path))
    
    # 输出统计结果
    print(f"\n{'='*50}")
    print(f"解压完成!")
    print(f"总文件数: {total_files}")
    print(f"成功解压: {success_count}")
    print(f"失败文件: {len(failed_files)}")
    
    if failed_files:
        print(f"\n失败的文件列表:")
        for failed_file in failed_files:
            print(f"  - {failed_file}")
    
    return len(failed_files) == 0


def extract_zip(zip_path, extract_path):
    """解压 ZIP 文件"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
        return True
    except Exception as e:
        print(f"  ZIP 解压错误: {e}")
        return False


def extract_tar(tar_path, extract_path):
    """解压 TAR 相关文件"""
    try:
        with tarfile.open(tar_path, 'r:*') as tar_ref:
            tar_ref.extractall(extract_path)
        return True
    except Exception as e:
        print(f"  TAR 解压错误: {e}")
        return False


if __name__ == "__main__":
    print("开始解压 /data/phasetwo 目录下的压缩包...")
    success = extract_archives()
    
    if success:
        print("\n🎉 所有文件解压成功!")
        exit(0)
    else:
        print("\n⚠️  部分文件解压失败，请检查日志")
        exit(1)
