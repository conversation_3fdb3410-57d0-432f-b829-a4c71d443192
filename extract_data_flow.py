#!/usr/bin/env python3
"""
从批量测试日志中提取数据流信息
"""
import re
import subprocess
import sys

def extract_data_acquisition_logs():
    """提取数据获取日志"""
    
    print("🔍 **数据获取情况分析**")
    print("=" * 60)
    
    # 查找最近的批量测试日志
    try:
        # 查找包含批量测试信息的日志
        result = subprocess.run([
            'grep', '-l', 'A2A AIOps系统批量诊断工具', '*.log'
        ], capture_output=True, text=True, cwd='/home/<USER>/aiopsagent/autogen-0.4.4')
        
        if result.returncode == 0:
            log_files = result.stdout.strip().split('\n')
            print(f"找到批量测试日志文件: {log_files}")
        else:
            print("未找到批量测试日志文件，使用终端输出分析")
            return analyze_terminal_output()
            
    except Exception as e:
        print(f"查找日志文件失败: {e}")
        return analyze_terminal_output()

def analyze_terminal_output():
    """分析终端输出中的数据获取信息"""
    
    print("\n📊 **从终端输出分析数据获取模式**")
    
    # 基于之前看到的输出模式进行分析
    data_patterns = {
        'metrics_acquired': r'✅ 从DataLoader获取到 (\d+) 条metrics记录',
        'traces_acquired': r'✅ 从DataLoader获取到 (\d+) 条traces记录', 
        'logs_acquired': r'✅ 从DataLoader获取到 (\d+) 条logs记录',
        'metrics_analyzed': r'✅ metricsAgent LLM预处理成功: (\d+) 个可疑实体',
        'traces_analyzed': r'✅ tracesAgent LLM预处理成功: (\d+) 个可疑实体',
        'logs_analyzed': r'✅ logsAgent LLM预处理成功: (\d+) 个可疑实体'
    }
    
    print("根据之前观察到的模式:")
    print("✅ 数据获取阶段:")
    print("   - Metrics: 通常获取到2000-3000条记录")
    print("   - Traces: 通常获取到50000+条记录") 
    print("   - Logs: 通常获取到50000+条记录")
    print()
    print("❌ LLM分析阶段:")
    print("   - MetricsAgent: 通常输出0个可疑实体")
    print("   - TracesAgent: 通常输出2-4个可疑实体")
    print("   - LogsAgent: 通常输出0-2个可疑实体")
    print()
    print("🚨 **关键问题**: MetricsAgent获取数据成功但LLM分析失败")
    
    return True

def analyze_specific_case_data_flow():
    """分析特定案例的数据流"""
    
    print("\n🔍 **特定案例数据流分析**")
    print("=" * 60)
    
    # 分析一个Frontend案例的数据流
    case_uuid = "345fbe93-80"
    print(f"分析案例: {case_uuid}")
    print("预期数据流:")
    print("1. DataLoader → 获取APM/Trace/Log数据")
    print("2. MetricAgent → LLM分析APM数据 → 输出可疑实体")
    print("3. TraceAgent → LLM分析Trace数据 → 输出可疑实体") 
    print("4. LogAgent → LLM分析Log数据 → 输出可疑实体")
    print("5. ReasoningAgent → 融合多维证据 → 输出根因")
    print()
    print("实际观察到的问题:")
    print("❌ 步骤2: MetricAgent LLM分析输出0个可疑实体")
    print("❌ 步骤5: ReasoningAgent主要使用Trace数据，缺少Metric数据")
    
    return True

def identify_data_transmission_breakpoints():
    """识别数据传递断点"""
    
    print("\n🚨 **数据传递断点分析**")
    print("=" * 60)
    
    breakpoints = [
        {
            "位置": "MetricAgent LLM分析",
            "问题": "获取到数据但LLM输出0个异常",
            "可能原因": [
                "LLM提示中的阈值设置问题",
                "数据预处理过滤过于严格", 
                "LLM响应解析失败",
                "时间窗口匹配问题"
            ]
        },
        {
            "位置": "ReasoningAgent证据聚合",
            "问题": "没有使用具体的APM数值",
            "可能原因": [
                "证据摘要构建逻辑过于简化",
                "数据格式转换丢失细节",
                "LLM提示没有要求具体数值"
            ]
        },
        {
            "位置": "推理逻辑",
            "问题": "错误判断Frontend为根因",
            "可能原因": [
                "缺少分布式系统架构知识",
                "没有正确实现调用链因果分析",
                "证据权重计算错误"
            ]
        }
    ]
    
    for i, bp in enumerate(breakpoints, 1):
        print(f"{i}. **{bp['位置']}**")
        print(f"   问题: {bp['问题']}")
        print(f"   可能原因:")
        for reason in bp['可能原因']:
            print(f"     - {reason}")
        print()
    
    return breakpoints

if __name__ == "__main__":
    extract_data_acquisition_logs()
    analyze_specific_case_data_flow()
    breakpoints = identify_data_transmission_breakpoints()
    
    print("🎯 **下一步行动建议**:")
    print("1. 深入调试MetricAgent的LLM分析逻辑")
    print("2. 检查ReasoningAgent的证据聚合机制")
    print("3. 修复Frontend根因判断的逻辑错误")
    print("4. 验证修复后的多维数据融合效果")
