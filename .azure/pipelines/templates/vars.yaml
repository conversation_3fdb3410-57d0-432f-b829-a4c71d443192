# It seems that variables must be defined in their own file when using templates

variables:
  build_flags: ' /m /v:m'
  solution: 'AutoGen.sln'
  codesign_runtime: '2.1.x'
  GDN_SUPPRESS_FORKED_BUILD_WARNING: true # Avoid warning "Guardian is not supported for builds from forked GitHub repositories"
  MicroBuildOutputFolderOverride: '$(Agent.TempDirectory)'
  # Auto-injection is not necessary because the tasks are explicitly included where they're enabled.
  Codeql.SkipTaskAutoInjection: true
  ${{ if eq(variables['System.TeamProject'], 'GitHub - PR Builds') }}:
    pool_name: '1es-agpublish-pool'
    pool_image: 'agpublish-agent-image'
    official_build: false
  ${{ else }}:
    ${{ if eq(variables['System.TeamProject'], 'internal') }}:
      pool_name: '1es-agpublish-pool'
      pool_image: 'agpublish-agent-image'
    ${{ else }}:
      pool_name: '1es-agpublish-pool'
      pool_image: 'agpublish-agent-image'
    official_build: true
    # Do not let CodeQL3000 Extension gate scan frequency.
    Codeql.Cadence: 0
    # Enable CodeQL3000 unconditionally so it may be run on any branch.
    Codeql.Enabled: true
    # Ignore test and infrastructure code.
    Codeql.SourceRoot: src
    # CodeQL3000 needs this plumbed along as a variable to enable TSA. Don't use TSA in manual builds.
    Codeql.TSAEnabled: ${{ eq(variables['Build.Reason'], 'Schedule') }}
    # Default expects tsaoptions.json under SourceRoot.
    Codeql.TSAOptionsPath: '$(Build.SourcesDirectory)/.config/tsaoptions.json'
    # Do not slow builds down w/ the CodeQL3000 tasks unless this is a nightly build or it's requested.
    runCodeQL3000: ${{ or(eq(variables['Build.Reason'], 'Schedule'), and(eq(variables['Build.Reason'], 'Manual'), eq(parameters.runCodeQL3000, 'true'))) }}