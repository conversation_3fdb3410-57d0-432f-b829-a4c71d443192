t - INFO - 请分析以下系统异常证据，确定故障根因：

**案例信息**:
- 案例ID: 345fbe93-80
- 异常时间: 2025-06-05T16:10:02Z 到 2025-06-05T16:31:02Z

**证据分析**:
=== 🔍 系统异常证据分析 ===

📊 **调用链异常分析**:
  异常1: frontend服务
    TraceID: 714615d20256e0bbc7eda48b198396fd
    SpanID: b9a5023201eabb21
    操作名称: hipstershop.Frontend/Recv
    响应延迟: 554.4ms (严重程度: low)
    开始时间: 2025-06-05 17:10:04
    Span类型: server
    状态码: HTTP:200
    父Span: 根Span
    调用关系: ROOT
    主机名: unknown
    异常类型: high_latency
    置信度: 0.800

  异常2: productcatalogservice服务
    TraceID: 55c5acc5d4b5fa11592aa70af798625c
    SpanID: 0c4e5f3bc4bfa17e
    操作名称: hipstershop.ProductCatalogService/GetProduct
    响应延迟: 11.3ms (严重程度: low)
    开始时间: 2025-06-05 17:10:04
    Span类型: client
    状态码: gRPC:OK
    父Span: 262d3c481085d4b1
    调用关系: CHILD_OF
    主机名: unknown
    异常类型: service_error
    置信度: 0.800

METRIC ANOMALY ANALYSIS:
APPLICATION LAYER ANOMALIES:
  Service: redis-cart (Criticality: 普通服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 292 (threshold: 50, deviation: 5.8x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 42 (threshold: 25, deviation: 1.7x)  [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 292 (threshold: 50, deviation: 5.8x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 42 (threshold: 25, deviation: 1.7x)  [peak: 16:16:00, duration: 22.0min]

    Pod: redis-cart-0
      CRITICAL: 请求数量 = 292 (threshold: 50, deviation: 5.8x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.1%(1次事件), 超时6次 (严重程度:high, 评分:4)
      CRITICAL: 响应数量 = 292 (threshold: 50, deviation: 5.8x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.1%(1次事件), 超时6次 (严重程度:high, 评分:4)
      MEDIUM: 请求数量 = 42 (threshold: 25, deviation: 1.7x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.1%(1次事件), 超时6次 (严重程度:high, 评分:4)
      MEDIUM: 响应数量 = 42 (threshold: 25, deviation: 1.7x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.1%(1次事件), 超时6次 (严重程度:high, 评分:4)


  Service: productcatalogservice (Criticality: 重要服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 4.1K (threshold: 50, deviation: 82.6x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 408 (threshold: 25, deviation: 16.3x)  [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 3.5K (threshold: 50, deviation: 70.3x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 346 (threshold: 25, deviation: 13.8x)  [peak: 16:16:00, duration: 22.0min]

    Pod: productcatalogservice-2
      CRITICAL: 请求数量 = 2.6K (threshold: 50, deviation: 51.2x) [peak: 16:15:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率16.0%(23次事件) (严重程度:high, 评分:3)
      CRITICAL: 响应数量 = 2.2K (threshold: 50, deviation: 43.0x) [peak: 16:15:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率16.0%(23次事件) (严重程度:high, 评分:3)
      MEDIUM: 请求数量 = 641 (threshold: 25, deviation: 25.6x) [peak: 16:15:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率16.0%(23次事件) (严重程度:high, 评分:3)
      MEDIUM: 响应数量 = 532 (threshold: 25, deviation: 21.3x) [peak: 16:15:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率16.0%(23次事件) (严重程度:high, 评分:3)

    Pod: productcatalogservice-1
      CRITICAL: 请求数量 = 2.1K (threshold: 50, deviation: 42.8x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率14.1%(23次事件) (严重程度:high, 评分:3)
      CRITICAL: 响应数量 = 1.8K (threshold: 50, deviation: 36.8x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率14.1%(23次事件) (严重程度:high, 评分:3)
      MEDIUM: 请求数量 = 936 (threshold: 25, deviation: 37.4x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率14.1%(23次事件) (严重程度:high, 评分:3)
      MEDIUM: 响应数量 = 789 (threshold: 25, deviation: 31.6x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率14.1%(23次事件) (严重程度:high, 评分:3)

    Pod: productcatalogservice-0
      MEDIUM: 请求数量 = 28 (threshold: 25, deviation: 1.1x) [peak: 16:28:00, duration: 21.0min, trend: increasing]
        🎯 APM业务异常: 丢包率14.3%(23次事件) (严重程度:high, 评分:3)


  Service: emailservice (Criticality: 普通服务)
    Service Level Metrics:
      LOW: 平均响应时间 = 31.7ms (threshold: 30.0ms, deviation: 1.1x)  [peak: 16:21:00, duration: 15.0min]
      MEDIUM: 平均响应时间 = 24.3ms (threshold: 15.0ms, deviation: 1.6x)  [peak: 16:21:00, duration: 20.0min]
      MEDIUM: 请求数量 = 28 (threshold: 25, deviation: 1.1x)  [peak: 16:20:00, duration: 22.0min]
      MEDIUM: 响应数量 = 28 (threshold: 25, deviation: 1.1x)  [peak: 16:20:00, duration: 22.0min]

    Pod: emailservice-1
      MEDIUM: 平均响应时间 = 40.1ms (threshold: 30.0ms, deviation: 1.3x) [peak: 16:22:00, duration: 8.0min]
      MEDIUM: 平均响应时间 = 36.7ms (threshold: 15.0ms, deviation: 2.4x) [peak: 16:22:00, duration: 20.0min, trend: increasing]

    Pod: emailservice-2
      MEDIUM: 平均响应时间 = 39.0ms (threshold: 30.0ms, deviation: 1.3x) [peak: 16:28:00, duration: 15.0min, trend: increasing]
        🎯 APM业务异常: 丢包率11.1%(3次事件) (严重程度:high, 评分:3)
      MEDIUM: 平均响应时间 = 25.7ms (threshold: 15.0ms, deviation: 1.7x) [peak: 16:28:00, duration: 20.0min]
        🎯 APM业务异常: 丢包率11.1%(3次事件) (严重程度:high, 评分:3)

    Pod: emailservice-0
      MEDIUM: 平均响应时间 = 51.5ms (threshold: 30.0ms, deviation: 1.7x) [peak: 16:26:00, duration: 11.0min, trend: increasing]
        🎯 APM业务异常: 丢包率11.1%(1次事件) (严重程度:high, 评分:3)
      MEDIUM: 平均响应时间 = 41.2ms (threshold: 15.0ms, deviation: 2.7x) [peak: 16:26:00, duration: 18.0min, trend: increasing]
        🎯 APM业务异常: 丢包率11.1%(1次事件) (严重程度:high, 评分:3)


  Service: currencyservice (Criticality: 普通服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 1.1K (threshold: 50, deviation: 23.0x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 356 (threshold: 25, deviation: 14.2x)  [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 1.1K (threshold: 50, deviation: 22.4x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 333 (threshold: 25, deviation: 13.3x)  [peak: 16:16:00, duration: 22.0min]

    Pod: currencyservice-0
      CRITICAL: 请求数量 = 476 (threshold: 50, deviation: 9.5x) [peak: 16:13:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率2.5%(13次事件) (严重程度:medium, 评分:2)
      CRITICAL: 响应数量 = 468 (threshold: 50, deviation: 9.4x) [peak: 16:13:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率2.5%(13次事件) (严重程度:medium, 评分:2)
      MEDIUM: 请求数量 = 194 (threshold: 25, deviation: 7.8x) [peak: 16:13:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率2.5%(13次事件) (严重程度:medium, 评分:2)
      MEDIUM: 响应数量 = 188 (threshold: 25, deviation: 7.5x) [peak: 16:13:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率2.5%(13次事件) (严重程度:medium, 评分:2)

    Pod: currencyservice-1
      CRITICAL: 请求数量 = 808 (threshold: 50, deviation: 16.2x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率3.3%(17次事件) (严重程度:medium, 评分:2)
      CRITICAL: 响应数量 = 781 (threshold: 50, deviation: 15.6x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率3.3%(17次事件) (严重程度:medium, 评分:2)
      MEDIUM: 请求数量 = 466 (threshold: 25, deviation: 18.6x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率3.3%(17次事件) (严重程度:medium, 评分:2)
      MEDIUM: 响应数量 = 440 (threshold: 25, deviation: 17.6x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率3.3%(17次事件) (严重程度:medium, 评分:2)


  Service: recommendationservice (Criticality: 重要服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 346 (threshold: 50, deviation: 6.9x)  [peak: 16:10:00, duration: 22.0min]
      MEDIUM: 请求数量 = 36 (threshold: 25, deviation: 1.4x)  [peak: 16:10:00, duration: 22.0min]
      CRITICAL: 响应数量 = 366 (threshold: 50, deviation: 7.3x)  [peak: 16:10:00, duration: 22.0min]
      MEDIUM: 响应数量 = 36 (threshold: 25, deviation: 1.4x)  [peak: 16:10:00, duration: 22.0min]

    Pod: recommendationservice-0
      CRITICAL: 请求数量 = 346 (threshold: 50, deviation: 6.9x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 数据一致性问题1个 (严重程度:medium, 评分:1)
      CRITICAL: 响应数量 = 366 (threshold: 50, deviation: 7.3x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 数据一致性问题1个 (严重程度:medium, 评分:1)
      MEDIUM: 请求数量 = 36 (threshold: 25, deviation: 1.4x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 数据一致性问题1个 (严重程度:medium, 评分:1)
      MEDIUM: 响应数量 = 36 (threshold: 25, deviation: 1.4x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 数据一致性问题1个 (严重程度:medium, 评分:1)


  Service: frontend (Criticality: 关键服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 3.7K (threshold: 50, deviation: 74.9x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 684 (threshold: 25, deviation: 27.4x)  [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 3.7K (threshold: 50, deviation: 74.9x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 683 (threshold: 25, deviation: 27.3x)  [peak: 16:16:00, duration: 22.0min]

    Pod: frontend-1
      CRITICAL: 请求数量 = 1.1K (threshold: 50, deviation: 22.4x) [peak: 16:20:00, duration: 22.0min]
      CRITICAL: 响应数量 = 1.1K (threshold: 50, deviation: 22.4x) [peak: 16:20:00, duration: 22.0min]
      MEDIUM: 请求数量 = 314 (threshold: 25, deviation: 12.6x) [peak: 16:20:00, duration: 22.0min]
      MEDIUM: 响应数量 = 314 (threshold: 25, deviation: 12.6x) [peak: 16:20:00, duration: 22.0min]

    Pod: frontend-0
      CRITICAL: 请求数量 = 1.1K (threshold: 50, deviation: 23.0x) [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 1.1K (threshold: 50, deviation: 23.0x) [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 276 (threshold: 25, deviation: 11.0x) [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 284 (threshold: 25, deviation: 11.4x) [peak: 16:16:00, duration: 22.0min]

    Pod: frontend-2
      CRITICAL: 请求数量 = 1.5K (threshold: 50, deviation: 30.4x) [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 1.5K (threshold: 50, deviation: 30.3x) [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 400 (threshold: 25, deviation: 16.0x) [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 399 (threshold: 25, deviation: 16.0x) [peak: 16:16:00, duration: 22.0min]


  Service: checkoutservice (Criticality: 关键服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 464 (threshold: 50, deviation: 9.3x)  [peak: 16:28:00, duration: 22.0min]
      MEDIUM: 请求数量 = 210 (threshold: 25, deviation: 8.4x)  [peak: 16:28:00, duration: 22.0min]
      CRITICAL: 响应数量 = 464 (threshold: 50, deviation: 9.3x)  [peak: 16:28:00, duration: 22.0min]
      MEDIUM: 响应数量 = 210 (threshold: 25, deviation: 8.4x)  [peak: 16:28:00, duration: 22.0min]

    Pod: checkoutservice-2
      CRITICAL: 请求数量 = 464 (threshold: 50, deviation: 9.3x) [peak: 16:28:00, duration: 22.0min]
      CRITICAL: 响应数量 = 464 (threshold: 50, deviation: 9.3x) [peak: 16:28:00, duration: 22.0min]
      MEDIUM: 请求数量 = 210 (threshold: 25, deviation: 8.4x) [peak: 16:28:00, duration: 22.0min]
      MEDIUM: 响应数量 = 210 (threshold: 25, deviation: 8.4x) [peak: 16:28:00, duration: 22.0min]


  Service: cartservice (Criticality: 重要服务)
    Service Level Metrics:
      CRITICAL: 请求数量 = 630 (threshold: 50, deviation: 12.6x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 请求数量 = 79 (threshold: 25, deviation: 3.2x)  [peak: 16:16:00, duration: 22.0min]
      CRITICAL: 响应数量 = 630 (threshold: 50, deviation: 12.6x)  [peak: 16:16:00, duration: 22.0min]
      MEDIUM: 响应数量 = 79 (threshold: 25, deviation: 3.2x)  [peak: 16:16:00, duration: 22.0min]

    Pod: cartservice-1
      CRITICAL: 请求数量 = 568 (threshold: 50, deviation: 11.4x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.3%(2次事件), 超时6次 (严重程度:high, 评分:4)
      CRITICAL: 响应数量 = 568 (threshold: 50, deviation: 11.4x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.3%(2次事件), 超时6次 (严重程度:high, 评分:4)
      MEDIUM: 请求数量 = 73 (threshold: 25, deviation: 2.9x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.3%(2次事件), 超时6次 (严重程度:high, 评分:4)
      MEDIUM: 响应数量 = 73 (threshold: 25, deviation: 2.9x) [peak: 16:16:00, duration: 22.0min]
        🎯 APM业务异常: 丢包率1.3%(2次事件), 超时6次 (严重程度:high, 评分:4)


  Service: shippingservice (Criticality: 普通服务)
    Service Level Metrics:
      HIGH: 请求数量 = 166 (threshold: 50, deviation: 3.3x)  [peak: 16:11:00, duration: 22.0min]
      MEDIUM: 请求数量 = 96 (threshold: 25, deviation: 3.8x)  [peak: 16:11:00, duration: 22.0min]
      HIGH: 响应数量 = 166 (threshold: 50, deviation: 3.3x)  [peak: 16:11:00, duration: 22.0min]
      MEDIUM: 响应数量 = 96 (threshold: 25, deviation: 3.8x)  [peak: 16:11:00, duration: 22.0min]

    Pod: shippingservice-0
      HIGH: 请求数量 = 140 (threshold: 50, deviation: 2.8x) [peak: 16:11:00, duration: 22.0min]
      HIGH: 响应数量 = 140 (threshold: 50, deviation: 2.8x) [peak: 16:11:00, duration: 22.0min]
      MEDIUM: 请求数量 = 86 (threshold: 25, deviation: 3.4x) [peak: 16:11:00, duration: 22.0min]
      MEDIUM: 响应数量 = 86 (threshold: 25, deviation: 3.4x) [peak: 16:11:00, duration: 22.0min]


  Service: adservice (Criticality: 普通服务)
    Service Level Metrics:
      CRITICAL: 异常比例 = 1.36% (threshold: 0.01%, deviation: 272.0x)  [peak: 16:24:00, duration: 13.0min, trend: decreasing]
      MEDIUM: 异常比例 = 1.36% (threshold: 0.00%, deviation: 544.0x)  [peak: 16:24:00, duration: 13.0min, trend: decreasing]
      CRITICAL: 请求数量 = 536 (threshold: 50, deviation: 10.7x)  [peak: 16:10:00, duration: 22.0min]
      MEDIUM: 请求数量 = 139 (threshold: 25, deviation: 5.6x)  [peak: 16:10:00, duration: 22.0min]
      CRITICAL: 响应数量 = 539 (threshold: 50, deviation: 10.8x)  [peak: 16:10:00, duration: 22.0min]
      MEDIUM: 响应数量 = 139 (threshold: 25, deviation: 5.6x)  [peak: 16:10:00, duration: 22.0min]

    Pod: adservice-0
      CRITICAL: 异常比例 = 1.74% (threshold: 0.01%, deviation: 348.0x) [peak: 16:24:00, duration: 13.0min, trend: decreasing]
        🎯 APM业务异常: 错误率突增至174.0%(3次), 数据一致性问题1个 (严重程度:high, 评分:4)
      CRITICAL: 请求数量 = 440 (threshold: 50, deviation: 8.8x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 错误率突增至174.0%(3次), 数据一致性问题1个 (严重程度:high, 评分:4)
      CRITICAL: 响应数量 = 443 (threshold: 50, deviation: 8.9x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 错误率突增至174.0%(3次), 数据一致性问题1个 (严重程度:high, 评分:4)
      MEDIUM: 异常比例 = 1.74% (threshold: 0.00%, deviation: 696.0x) [peak: 16:24:00, duration: 13.0min, trend: decreasing]
        🎯 APM业务异常: 错误率突增至174.0%(3次), 数据一致性问题1个 (严重程度:high, 评分:4)
      MEDIUM: 请求数量 = 139 (threshold: 25, deviation: 5.6x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 错误率突增至174.0%(3次), 数据一致性问题1个 (严重程度:high, 评分:4)
      MEDIUM: 响应数量 = 139 (threshold: 25, deviation: 5.6x) [peak: 16:10:00, duration: 22.0min]
        🎯 APM业务异常: 错误率突增至174.0%(3次), 数据一致性问题1个 (严重程度:high, 评分:4)


  Service: tidb (Criticality: 数据库组件)

INFRASTRUCTURE LAYER ANOMALIES:
  Node Level Anomalies:
    Node: aiops-k8s-04
      Node内存使用率: 29.23% (deviation: 34.4x)  [peak: 16:28:00, duration: 22.0min, trend: increasing]
      Node内存使用率: 0.53% (deviation: 1.2x)  [peak: 16:28:00, duration: 22.0min, trend: increasing]
      Node磁盘使用率: 36.41% (deviation: 40.5x)  [peak: 16:32:00, duration: 22.0min]
      Node磁盘使用率: 21.91% (deviation: 48.7x)  [peak: 16:32:00, duration: 22.0min]
      Node CPU使用率: 25.98% (deviation: 32.5x)  [peak: 16:12:00, duration: 22.0min]
      Node CPU使用率: 3.99% (deviation: 10.0x)  [peak: 16:12:00, duration: 22.0min]

    Node: aiops-k8s-05
      Node内存使用率: 34.63% (deviation: 40.7x)  [peak: 16:27:00, duration: 22.0min, trend: increasing]
      Node内存使用率: 0.72% (deviation: 1.7x)  [peak: 16:27:00, duration: 22.0min, trend: increasing]
      Node磁盘使用率: 36.01% (deviation: 40.0x)  [peak: 16:13:00, duration: 22.0min]
      Node磁盘使用率: 21.51% (deviation: 47.8x)  [peak: 16:13:00, duration: 22.0min]
      Node磁盘读取字节: 109.07KB (deviation: 7.7x)  [peak: 16:13:00]
      Node CPU使用率: 19.35% (deviation: 24.2x)  [peak: 16:25:00, duration: 22.0min, trend: increasing]
      Node CPU使用率: 3.01% (deviation: 7.5x)  [peak: 16:25:00, duration: 22.0min, trend: increasing]

    Node: k8s-master3
      Node内存使用率: 36.69% (deviation: 43.2x)  [peak: 16:29:00, duration: 22.0min]
      Node内存使用率: 3.49% (deviation: 8.2x)  [peak: 16:29:00, duration: 22.0min]
      Node磁盘使用率: 68.32% (deviation: 75.9x)  [peak: 16:11:00, duration: 22.0min]
      Node CPU使用率: 14.68% (deviation: 18.3x)  [peak: 16:13:00, duration: 22.0min]
      Node CPU使用率: 4.05% (deviation: 10.1x)  [peak: 16:13:00, duration: 22.0min]

    Node: k8s-master2
      Node内存使用率: 48.51% (deviation: 57.1x)  [peak: 16:30:00, duration: 22.0min]
      Node内存使用率: 3.03% (deviation: 7.1x)  [peak: 16:30:00, duration: 22.0min]
      Node磁盘使用率: 63.14% (deviation: 70.2x)  [peak: 16:10:00, duration: 22.0min]
      Node磁盘使用率: 56.10% (deviation: 124.7x)  [peak: 16:10:00, duration: 22.0min]
      Node CPU使用率: 16.68% (deviation: 20.8x)  [peak: 16:29:00, duration: 22.0min]
      Node CPU使用率: 4.07% (deviation: 10.2x)  [peak: 16:29:00, duration: 22.0min]

    Node: k8s-master1
      Node内存使用率: 35.67% (deviation: 42.0x)  [peak: 16:25:00, duration: 22.0min]
      Node内存使用率: 3.76% (deviation: 8.8x)  [peak: 16:25:00, duration: 22.0min]
      Node磁盘使用率: 80.95% (deviation: 89.9x)  [peak: 16:10:00, duration: 22.0min]
      Node磁盘使用率: 67.12% (deviation: 149.2x)  [peak: 16:10:00, duration: 22.0min]
      Node CPU使用率: 12.03% (deviation: 15.0x)  [peak: 16:16:00, duration: 22.0min]
      Node CPU使用率: 1.49% (deviation: 3.7x)  [peak: 16:16:00, duration: 22.0min]

    Node: aiops-k8s-06
      Node内存使用率: 30.15% (deviation: 35.5x)  [peak: 16:11:00, duration: 22.0min, trend: decreasing]
      Node内存使用率: 1.21% (deviation: 2.8x)  [peak: 16:11:00, duration: 22.0min, trend: decreasing]
      Node磁盘使用率: 66.16% (deviation: 73.5x)  [peak: 16:14:00, duration: 22.0min]
      Node磁盘使用率: 23.93% (deviation: 53.2x)  [peak: 16:14:00, duration: 22.0min]
      Node CPU使用率: 32.39% (deviation: 40.5x)  [peak: 16:30:00, duration: 22.0min]
      Node CPU使用率: 5.05% (deviation: 12.6x)  [peak: 16:30:00, duration: 22.0min]

    Node: aiops-k8s-07
      Node内存使用率: 68.14% (deviation: 80.2x)  [peak: 16:28:00, duration: 22.0min, trend: increasing]
      Node内存使用率: 0.90% (deviation: 2.1x)  [peak: 16:28:00, duration: 22.0min, trend: increasing]
      Node磁盘使用率: 56.84% (deviation: 63.2x)  [peak: 16:26:00, duration: 22.0min]
      Node磁盘使用率: 13.97% (deviation: 31.0x)  [peak: 16:26:00, duration: 22.0min]
      Node CPU使用率: 25.83% (deviation: 32.3x)  [peak: 16:21:00, duration: 22.0min]
      Node CPU使用率: 6.95% (deviation: 17.4x)  [peak: 16:21:00, duration: 22.0min]

    Node: aiops-k8s-08
      Node内存使用率: 59.84% (deviation: 70.4x)  [peak: 16:23:00, duration: 22.0min, trend: increasing]
      Node内存使用率: 1.25% (deviation: 2.9x)  [peak: 16:23:00, duration: 22.0min, trend: increasing]
      Node磁盘使用率: 64.78% (deviation: 72.0x)  [peak: 16:20:00, duration: 22.0min]
      Node磁盘使用率: 22.17% (deviation: 49.3x)  [peak: 16:20:00, duration: 22.0min]
      Node CPU使用率: 32.93% (deviation: 41.2x)  [peak: 16:21:00, duration: 22.0min]
      Node CPU使用率: 5.94% (deviation: 14.9x)  [peak: 16:21:00, duration: 22.0min]
      node_disk_write_time_seconds_total: 0.01 (deviation: 3.8x)  [peak: 16:21:00, duration: 7.0min]

    Node: aiops-k8s-01
      Node内存使用率: 51.56% (deviation: 60.7x)  [peak: 16:32:00, duration: 22.0min, trend: increasing]
      Node内存使用率: 0.93% (deviation: 2.2x)  [peak: 16:32:00, duration: 22.0min, trend: increasing]
      Node磁盘使用率: 71.43% (deviation: 79.4x)  [peak: 16:21:00, duration: 22.0min]
      Node磁盘使用率: 41.40% (deviation: 92.0x)  [peak: 16:21:00, duration: 22.0min]
      Node CPU使用率: 25.13% (deviation: 31.4x)  [peak: 16:18:00, duration: 22.0min]
      Node CPU使用率: 3.57% (deviation: 8.9x)  [peak: 16:18:00, duration: 22.0min]

    Node: aiops-k8s-02
      Node内存使用率: 17.16% (deviation: 20.2x)  [peak: 16:12:00, duration: 22.0min]
      Node磁盘使用率: 36.66% (deviation: 40.7x)  [peak: 16:10:00, duration: 22.0min]
      Node磁盘使用率: 13.61% (deviation: 30.2x)  [peak: 16:10:00, duration: 22.0min]
      Node磁盘写入字节: 17.00KB (deviation: 2.5x)  [peak: 16:12:00]
      Node CPU使用率: 6.84% (deviation: 8.5x)  [peak: 16:19:00, duration: 22.0min]
      Node CPU使用率: 0.75% (deviation: 1.9x)  [peak: 16:19:00, duration: 22.0min]

    Node: aiops-k8s-03
      Node内存使用率: 50.15% (deviation: 59.0x)  [peak: 16:30:00, duration: 22.0min, trend: increasing]
      Node内存使用率: 1.57% (deviation: 3.7x)  [peak: 16:30:00, duration: 22.0min, trend: increasing]
      Node磁盘使用率: 36.47% (deviation: 40.5x)  [peak: 16:23:00, duration: 22.0min]
      Node磁盘使用率: 22.22% (deviation: 49.4x)  [peak: 16:23:00, duration: 22.0min]
      Node CPU使用率: 38.92% (deviation: 48.6x)  [peak: 16:29:00, duration: 22.0min, trend: increasing]
      Node CPU使用率: 4.13% (deviation: 10.3x)  [peak: 16:29:00, duration: 22.0min, trend: increasing]

  Pod Level Anomalies:
    Pod: paymentservice-1
      pod_network_receive_packets: 0.78 (deviation: 1.1x)  [peak: 16:16:00, duration: 8.0min]
      Pod网络接收字节: 67B (deviation: 1.1x)  [peak: 16:16:00, duration: 14.0min, trend: increasing]
      pod_network_transmit_packets: 0.74 (deviation: 1.2x)  [peak: 16:24:00, duration: 8.0min]
      Pod网络发送字节: 61B (deviation: 1.1x)  [peak: 16:24:00, duration: 8.0min]

    Pod: paymentservice-2
      pod_network_receive_packets: 0.87 (deviation: 1.3x)  [peak: 16:16:00]
      Pod网络接收字节: 73B (deviation: 1.3x)  [peak: 16:16:00, duration: 3.0min]
      pod_network_transmit_packets: 0.68 (deviation: 1.2x)  [peak: 16:16:00]
      Pod网络发送字节: 58B (deviation: 1.2x)  [peak: 16:16:00, duration: 3.0min]

    Pod: emailservice-2
      pod_network_receive_packets: 1.68 (deviation: 1.2x)  [peak: 16:24:00]
      Pod网络接收字节: 182B (deviation: 1.3x)  [peak: 16:24:00, duration: 8.0min]
      Pod进程数: 10.00 (deviation: 1.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod进程数: 1.00 (deviation: 0.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod进程数: 9.00 (deviation: 2.2x)  [peak: 16:11:00, duration: 20.0min]
      pod_network_transmit_packets: 1.68 (deviation: 1.1x)  [peak: 16:24:00, duration: 8.0min]
      Pod网络发送字节: 212B (deviation: 1.1x)  [peak: 16:24:00, duration: 8.0min]
      Pod CPU使用率: 0.40% (deviation: 4.0x)  [peak: 16:11:00, duration: 20.0min]
      Pod CPU使用率: 0.40% (deviation: 8.0x)  [peak: 16:11:00, duration: 20.0min]

    Pod: currencyservice-2
      pod_network_receive_packets: 2.72 (deviation: 1.1x)  [peak: 16:24:00]
      Pod网络接收字节: 234B (deviation: 1.0x)  [peak: 16:24:00]

    Pod: paymentservice-0
      pod_network_receive_packets: 0.89 (deviation: 1.1x)  [peak: 16:12:00, duration: 9.0min]
      Pod网络接收字节: 77B (deviation: 1.1x)  [peak: 16:12:00]
      pod_network_transmit_packets: 0.74 (deviation: 1.1x)  [peak: 16:21:00]
      Pod网络发送字节: 60B (deviation: 1.0x)  [peak: 16:21:00, duration: 9.0min]

    Pod: adservice-2
      Pod文件系统写入字节: 1.80KB (deviation: 1.3x)  [peak: 16:29:00, duration: 20.0min, trend: increasing]

    Pod: redis-cart-0
      Pod文件系统写入字节: 1.11MB (deviation: 7.7x)  [peak: 16:28:00]
      Pod CPU使用率: 0.11% (deviation: 1.1x)  [peak: 16:28:00]
      Pod CPU使用率: 0.11% (deviation: 2.2x)  [peak: 16:28:00]

    Pod: emailservice-1
      Pod进程数: 10.00 (deviation: 1.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod进程数: 1.00 (deviation: 0.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod进程数: 9.00 (deviation: 2.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod CPU使用率: 0.40% (deviation: 4.0x)  [peak: 16:11:00, duration: 20.0min]
      Pod CPU使用率: 0.40% (deviation: 8.0x)  [peak: 16:11:00, duration: 20.0min]

    Pod: emailservice-0
      Pod进程数: 10.00 (deviation: 1.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod进程数: 1.00 (deviation: 0.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod进程数: 9.00 (deviation: 2.2x)  [peak: 16:11:00, duration: 20.0min]
      Pod CPU使用率: 0.40% (deviation: 4.0x)  [peak: 16:11:00, duration: 20.0min]
      Pod CPU使用率: 0.40% (deviation: 8.0x)  [peak: 16:11:00, duration: 20.0min]

CLUSTER COMPONENT ANOMALIES:
  Component Type: tidb
    Select查询QPS: 11.27 (deviation: 1.4x)
    Use数据库QPS: 11.27 (deviation: 1.4x)
    Commit提交QPS: 0.29 (deviation: 1.4x)
    Insert插入QPS: 0.20 (deviation: 1.3x)
    Begin事务QPS: 0.29 (deviation: 1.4x)
    Update更新QPS: 0.09 (deviation: 1.8x)

  Component Type: tikv
    server_is_up: 1.00 (deviation: 2.0x)
    可用存储大小: 53.13GB (deviation: 5.3x)
    scan_lock操作QPS: 4.51 (deviation: 2.3x)
    悲观锁获取QPS: 0.16 (deviation: 1.6x)
    Get操作QPS: 11.27 (deviation: 2.3x)
    io_util: 0.98% (deviation: 1.2x)
    KV Get操作QPS: 11 (deviation: 2.3x)

  Component Type: pd
    leader_primary: 1.00 (deviation: 1.1x)
    storage_capacity: 99809230848.00 (deviation: 1.9x)
    空Region数量: 191.00 (deviation: 19.1x)
    副本不足Region数量: 198.00 (deviation: 19.8x)
    缺失副本Region数量: 203.00 (deviation: 20.3x)
    Leader数量: 203 (deviation: 225.6x)
    缺失副本Region数量: 203 (deviation: 4.1x)