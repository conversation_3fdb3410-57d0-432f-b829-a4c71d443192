# 🚀 AIOps 系统使用示例

本文档提供了 AIOps 智能故障诊断系统的详细使用示例。

## 📋 目录

- [基础使用](#基础使用)
- [高级配置](#高级配置)
- [批量处理](#批量处理)
- [自定义智能体](#自定义智能体)
- [故障排除](#故障排除)

## 🔧 基础使用

### 1. 环境设置

```bash
# 1. 克隆项目
git clone https://gitee.com/haishin/aiops.git
cd aiops

# 2. 创建虚拟环境
python -m venv aiops-env
source aiops-env/bin/activate  # Linux/Mac
# aiops-env\Scripts\activate   # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 设置环境变量
export OPENAI_API_KEY="your-openai-api-key"
export UNI_MODEL_API_KEY="your-uni-model-token"
```

### 2. 配置 LLM

#### OpenAI 配置
```python
# config/llm_config.py
OPENAI_CONFIG = {
    "model": "gpt-4",
    "api_key": "sk-your-api-key",
    "base_url": "https://api.openai.com/v1",
    "temperature": 0.1,
    "max_tokens": 2000
}
```

#### uni大模型配置
```python
UNI_MODEL_CONFIG = {
    "model": "deepseek-v3:671b",
    "api_key": "your-bearer-token",
    "base_url": "https://uni-api.cstcloud.cn/v1",
    "temperature": 0.1,
    "max_tokens": 2000
}
```

### 3. 准备输入数据

创建 `input.json` 文件：
```json
[
    {
        "Anomaly Description": "The system experienced an anomaly from 2025-06-05T16:10:02Z to 2025-06-05T16:31:02Z. Please infer the possible cause.",
        "uuid": "345fbe93-80"
    }
]
```

### 4. 运行诊断

#### 单案例诊断
```bash
python a2a_batch_diagnosis.py \
    --input input.json \
    --output single_result.json \
    --cases 1 \
    --verbose
```

#### 查看结果
```bash
cat single_result.json | jq '.[0]'
```

输出示例：
```json
{
    "uuid": "345fbe93-80",
    "component": "frontend",
    "reason": "resource_bottleneck",
    "timestamp": "2025-06-05T16:15:32Z",
    "reasoning_trace": [
        {
            "step": 1,
            "agent": "MetricAnalysisAgent",
            "analysis": "检测到 frontend Pod CPU 使用率异常升高至 95%",
            "evidence": ["cpu_usage: 95%", "memory_usage: 87%"],
            "confidence": 0.85
        }
    ],
    "confidence": 0.92,
    "diagnosis_time": "2025-01-11T10:30:45Z"
}
```

## ⚙️ 高级配置

### 1. 自定义智能体配置

```python
# config/agent_config.py
AGENT_CONFIG = {
    "reasoning_agent": {
        "max_rounds": 3,                    # 最大推理轮数
        "confidence_threshold": 0.8,        # 置信度阈值
        "enable_cross_validation": True,    # 启用交叉验证
        "hypothesis_limit": 5               # 假设数量限制
    },
    "metric_agent": {
        "anomaly_threshold": 2.0,           # 异常检测阈值
        "time_window": "5m",                # 时间窗口
        "smoothing_factor": 0.3             # 平滑因子
    },
    "trace_agent": {
        "error_threshold": 0.05,            # 错误率阈值
        "latency_threshold": "2s",          # 延迟阈值
        "sampling_rate": 0.1                # 采样率
    },
    "log_agent": {
        "error_patterns": [                 # 错误模式
            "ERROR", "FATAL", "Exception"
        ],
        "context_lines": 3                  # 上下文行数
    }
}
```

### 2. 数据源配置

```python
# config/data_config.py
DATA_CONFIG = {
    "metric_path": "/data/metric",
    "trace_path": "/data/trace", 
    "log_path": "/data/log",
    "time_format": "%Y-%m-%dT%H:%M:%SZ",
    "timezone": "UTC",
    "file_patterns": {
        "metric": "metric_*.parquet",
        "trace": "trace_jaeger-span_*.parquet",
        "log": "log_*.parquet"
    }
}
```

### 3. 性能优化配置

```python
# config/performance_config.py
PERFORMANCE_CONFIG = {
    "max_workers": 4,                   # 最大工作线程
    "concurrent_limit": 2,              # 并发案例限制
    "timeout": 300,                     # 超时时间(秒)
    "retry_attempts": 3,                # 重试次数
    "chunk_size": 1000,                 # 数据块大小
    "memory_limit": "4GB",              # 内存限制
    "cache_enabled": True,              # 启用缓存
    "cache_ttl": 3600                   # 缓存过期时间
}
```

## 📦 批量处理

### 1. 批量诊断多个案例

```bash
# 处理所有案例
python a2a_batch_diagnosis.py \
    --input input.json \
    --output batch_results.json \
    --max-workers 4 \
    --concurrent-limit 2 \
    --timeout 300
```

### 2. 流式输出

```bash
# 启用流式输出，实时查看结果
python a2a_batch_diagnosis.py \
    --input input.json \
    --output streaming_results.json \
    --streaming \
    --verbose
```

### 3. 并发控制

```bash
# 高并发处理 (适用于高性能服务器)
python a2a_batch_diagnosis.py \
    --input input.json \
    --output high_concurrency_results.json \
    --max-workers 8 \
    --concurrent-limit 4 \
    --chunk-size 50
```

### 4. 错误恢复

```bash
# 启用错误恢复和重试
python a2a_batch_diagnosis.py \
    --input input.json \
    --output robust_results.json \
    --retry-attempts 3 \
    --continue-on-error \
    --save-errors error_log.json
```

## 🔧 自定义智能体

### 1. 创建自定义分析智能体

```python
# custom_agents/network_analysis_agent.py
from aiops_diagnosis.autogen_agents.base_aiops_agent import BaseAIOpsAgent
from typing import Dict, List, Any

class NetworkAnalysisAgent(BaseAIOpsAgent):
    """网络分析专业智能体"""
    
    def __init__(self, name: str, llm_config: dict):
        super().__init__(name, llm_config)
        self.analysis_type = "network"
        
    async def analyze_network_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析网络指标数据"""
        
        # 构建分析提示
        prompt = f"""
        作为网络分析专家，请分析以下网络指标数据：
        
        数据时间范围: {data.get('time_range')}
        网络指标: {data.get('network_metrics')}
        
        请识别：
        1. 网络延迟异常
        2. 带宽瓶颈
        3. 丢包率异常
        4. 连接数异常
        
        输出格式：
        {{
            "suspicious_entities": [具体的网络组件],
            "anomaly_type": "网络异常类型",
            "evidence": ["证据1", "证据2"],
            "confidence": 置信度(0-1),
            "analysis": "详细分析"
        }}
        """
        
        # 调用 LLM 分析
        result = await self.llm_analyze(prompt, data)
        
        # 标准化输出
        return self.standardize_output(result)
    
    def standardize_output(self, raw_result: str) -> Dict[str, Any]:
        """标准化输出格式"""
        try:
            import json
            parsed = json.loads(raw_result)
            return {
                "agent_type": self.analysis_type,
                "suspicious_entities": parsed.get("suspicious_entities", []),
                "anomaly_type": parsed.get("anomaly_type", "unknown"),
                "evidence": parsed.get("evidence", []),
                "confidence": parsed.get("confidence", 0.0),
                "analysis": parsed.get("analysis", ""),
                "timestamp": self.get_current_timestamp()
            }
        except Exception as e:
            return self.create_error_output(f"解析失败: {str(e)}")
```

### 2. 注册自定义智能体

```python
# config/custom_agent_registry.py
from custom_agents.network_analysis_agent import NetworkAnalysisAgent

CUSTOM_AGENTS = {
    "network_analysis": {
        "class": NetworkAnalysisAgent,
        "config": {
            "name": "NetworkAnalysisAgent",
            "description": "专业网络分析智能体",
            "capabilities": ["network_latency", "bandwidth_analysis", "packet_loss"]
        }
    }
}

def register_custom_agents(agent_manager):
    """注册自定义智能体"""
    for agent_name, agent_info in CUSTOM_AGENTS.items():
        agent_manager.register_agent(
            name=agent_name,
            agent_class=agent_info["class"],
            config=agent_info["config"]
        )
```

### 3. 使用自定义智能体

```python
# examples/use_custom_agent.py
import asyncio
from aiops_diagnosis.optimized_autogen_system import OptimizedAutogenSystem
from config.custom_agent_registry import register_custom_agents

async def main():
    # 创建系统实例
    system = OptimizedAutogenSystem()
    
    # 注册自定义智能体
    register_custom_agents(system.agent_manager)
    
    # 使用自定义智能体进行分析
    case_data = {
        "uuid": "test-001",
        "time_range": "2025-01-11T10:00:00Z to 2025-01-11T10:30:00Z",
        "network_metrics": {
            "latency": [100, 150, 200, 300],
            "bandwidth": [80, 75, 60, 45],
            "packet_loss": [0.1, 0.2, 0.5, 1.0]
        }
    }
    
    # 运行诊断
    result = await system.diagnose_with_custom_agents(case_data)
    print(f"诊断结果: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🐛 故障排除

### 1. 常见错误及解决方案

#### LLM 连接错误
```bash
# 错误: OpenAI API 连接失败
# 解决方案:
export OPENAI_API_KEY="your-correct-api-key"
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
```

#### 数据加载错误
```bash
# 错误: 数据文件不存在
# 解决方案:
ls -la data/metric/
ls -la data/trace/
ls -la data/log/

# 检查文件权限
chmod 644 data/**/*.parquet
```

#### 内存不足错误
```python
# 错误: MemoryError
# 解决方案: 调整配置
PERFORMANCE_CONFIG = {
    "max_workers": 2,        # 减少工作线程
    "concurrent_limit": 1,   # 降低并发
    "chunk_size": 100        # 减少数据块大小
}
```

### 2. 调试模式

```bash
# 启用详细调试输出
export AIOPS_DEBUG=1
export AIOPS_LOG_LEVEL=DEBUG

python a2a_batch_diagnosis.py \
    --input input.json \
    --output debug_results.json \
    --verbose \
    --debug
```

### 3. 性能监控

```python
# examples/performance_monitoring.py
from aiops_diagnosis.utils.performance_monitor import PerformanceMonitor
import time

# 启动性能监控
monitor = PerformanceMonitor()
monitor.start_monitoring()

# 运行诊断任务
start_time = time.time()
# ... 运行诊断代码 ...
end_time = time.time()

# 获取性能指标
metrics = monitor.get_metrics()
print(f"总耗时: {end_time - start_time:.2f}s")
print(f"平均诊断时间: {metrics['avg_diagnosis_time']:.2f}s")
print(f"成功率: {metrics['success_rate']:.2%}")
print(f"内存使用: {metrics['memory_usage']:.2f}MB")
```

## 📊 结果分析

### 1. 结果质量评估

```python
# examples/quality_assessment.py
from aiops_diagnosis.utils.quality_assessor import QualityAssessor

assessor = QualityAssessor()

# 评估单个诊断结果
result = {
    "component": "frontend",
    "reason": "resource_bottleneck",
    "confidence": 0.92,
    "reasoning_trace": [...]
}

quality_score = assessor.evaluate_diagnosis(result)
print(f"诊断质量评分: {quality_score:.2f}/10")

# 批量评估
batch_results = [...]  # 批量诊断结果
overall_quality = assessor.evaluate_batch(batch_results)
print(f"整体质量评分: {overall_quality:.2f}/10")
```

### 2. 结果可视化

```python
# examples/result_visualization.py
import matplotlib.pyplot as plt
import json

# 加载结果
with open('batch_results.json', 'r') as f:
    results = json.load(f)

# 统计组件分布
components = [r['component'] for r in results]
component_counts = {}
for comp in components:
    component_counts[comp] = component_counts.get(comp, 0) + 1

# 绘制图表
plt.figure(figsize=(10, 6))
plt.bar(component_counts.keys(), component_counts.values())
plt.title('故障组件分布')
plt.xlabel('组件')
plt.ylabel('故障次数')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('component_distribution.png')
plt.show()
```

---

更多示例和详细文档，请参考 [项目文档](docs/) 目录。
