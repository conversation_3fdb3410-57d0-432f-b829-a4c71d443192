# JSON 到 JSONL 转换总结报告

## 📋 转换概览

- **输入文件**: `seconde_output.json`
- **输出文件**: `seconde_output.jsonl`
- **转换时间**: 2025-07-11
- **总记录数**: 114 条

## 🔄 格式转换

### 原始格式 (JSON Array)
```json
[
  {
    "uuid": "74a44ae7-81",
    "component": "checkoutservice",
    "reason": "response delay",
    "time": "2025-06-05 17:10:04",
    "reasoning_trace": [...]
  },
  ...
]
```

### 目标格式 (JSONL)
```json
{"component": "checkoutservice", "uuid": "74a44ae7-81", "reason": "response delay", "reasoning_trace": [...]}
{"component": "checkoutservice", "uuid": "b1ab098d-83", "reason": "response delay", "reasoning_trace": [...]}
...
```

## 🔧 组件名称标准化

### 修复的问题组件
1. **`"unknown"`** → **`""`** (1 次)
   - 原因：分析失败的记录，清空组件名
   
2. **`"database-cluster"`** → **`"tidb"`** (1 次)
   - 原因：数据库集群映射到标准的 TiDB 组件

### 标准组件分布统计
| 组件名称 | 记录数量 | 占比 |
|---------|---------|------|
| frontend | 31 | 27.2% |
| checkoutservice | 26 | 22.8% |
| paymentservice | 17 | 14.9% |
| recommendationservice | 11 | 9.6% |
| tidb | 8 | 7.0% |
| productcatalogservice | 7 | 6.1% |
| redis | 4 | 3.5% |
| adservice | 4 | 3.5% |
| cartservice | 2 | 1.8% |
| shippingservice | 1 | 0.9% |
| emailservice | 1 | 0.9% |
| currencyservice | 1 | 0.9% |
| (空) | 1 | 0.9% |

## ✅ 验证结果

### 格式验证
- ✅ 所有 114 行都是有效的 JSON 对象
- ✅ 每行包含必需的字段：`component`, `uuid`, `reason`, `reasoning_trace`
- ✅ 符合 JSONL 标准格式

### 组件名称验证
- ✅ 113 个记录使用标准组件名称
- ✅ 1 个分析失败记录的组件名被正确清空
- ✅ 所有组件名称符合 AIOps 规则要求

## 📊 文件信息

- **输出文件大小**: 54,139 字节
- **行数**: 114 行
- **编码**: UTF-8
- **格式**: JSONL (JSON Lines)

## 🎯 使用说明

生成的 `seconde_output.jsonl` 文件可以直接用于：

1. **AIOps 系统评分**: 每行一个诊断结果，便于批量处理
2. **数据分析**: 可以使用 `jq`, `pandas` 等工具逐行解析
3. **系统集成**: 符合标准 JSONL 格式，易于集成到其他系统

### 示例读取代码

```python
import json

# 读取 JSONL 文件
with open('seconde_output.jsonl', 'r', encoding='utf-8') as f:
    for line in f:
        record = json.loads(line.strip())
        print(f"UUID: {record['uuid']}, Component: {record['component']}")
```

## 🔍 质量保证

- **数据完整性**: 所有原始数据都被保留，只进行了格式转换和组件名标准化
- **标准合规**: 组件名称严格遵循 AIOps 规则要求的标准组件白名单
- **错误处理**: 对无效组件名进行了适当的清理和映射

---

**转换完成时间**: 2025-07-11  
**脚本版本**: convert_to_jsonl.py v1.1  
**状态**: ✅ 成功完成
