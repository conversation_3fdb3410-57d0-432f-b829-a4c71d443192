#!/usr/bin/env python3
"""
uni大模型配置示例

本示例展示如何在AutoGen中使用uni大模型服务。
uni大模型完全兼容OpenAI API，只需要配置正确的base_url即可。
"""

import asyncio
import os
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import UserMessage


def create_uni_model_client():
    """
    创建uni大模型客户端

    uni大模型配置说明：
    - base_url: https://uni-api.cstcloud.cn/v1
    - 完全兼容OpenAI API规范
    - 支持标准的OpenAI客户端库调用方式
    """

    # 方式1: 使用uni大模型的实际模型ID
    client = OpenAIChatCompletionClient(
        model="deepseek-v3:671b",  # uni大模型的实际模型名称
        base_url="https://uni-api.cstcloud.cn/v1",
        api_key="2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286",
        # 必须指定模型能力，因为模型不在标准OpenAI模型列表中
        model_capabilities={
            "vision": False,
            "function_calling": True,
            "json_output": True,
        },
        # 可选：其他配置
        timeout=30.0,
        max_retries=3,
    )

    return client


def create_uni_model_client_with_model_info():
    """
    使用model_info参数创建uni大模型客户端（推荐方式）
    """
    from autogen_core.models import ModelFamily

    client = OpenAIChatCompletionClient(
        model="deepseek-v3:671b",
        base_url="https://uni-api.cstcloud.cn/v1",
        api_key=os.getenv("UNI_API_KEY", "2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286"),
        # 使用model_info替代model_capabilities（新版本推荐）
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
        },
        timeout=30.0,
        max_retries=3,
    )

    return client


def create_uni_model_client_from_config():
    """
    通过配置字典创建uni大模型客户端
    """
    from autogen_core.models import ChatCompletionClient, ModelFamily

    config = {
        "provider": "OpenAIChatCompletionClient",
        "config": {
            "model": "deepseek-v3:671b",
            "base_url": "https://uni-api.cstcloud.cn/v1",
            "api_key": "2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286",
            "model_info": {
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
            },
            "timeout": 30.0,
            "max_retries": 3,
        }
    }

    client = ChatCompletionClient.load_component(config)
    return client


async def test_uni_model():
    """
    测试uni大模型客户端
    """
    print("创建uni大模型客户端...")
    client = create_uni_model_client_with_model_info()  # 使用推荐的方式

    print("发送测试消息...")
    try:
        response = await client.create([
            UserMessage(content="你好，请介绍一下你自己。", source="user")
        ])

        print("uni大模型响应:")
        print(f"- 内容: {response.content}")
        print(f"- 使用情况: {response.usage}")
        if hasattr(response, 'model'):
            print(f"- 模型: {response.model}")

    except Exception as e:
        print(f"调用uni大模型时出错: {e}")
        print("请检查:")
        print("1. UNI_API_KEY环境变量是否设置正确")
        print("2. 网络连接是否正常")
        print("3. uni大模型服务是否可用")
        print("4. 模型名称是否正确（当前使用: deepseek-r1:32b-16k）")


async def test_uni_model_with_tools():
    """
    测试uni大模型的工具调用功能
    """
    from autogen_core.tools import FunctionTool
    from pydantic import BaseModel
    
    # 定义一个简单的工具
    class CalculatorArgs(BaseModel):
        a: float
        b: float
        operation: str
    
    def calculator(args: CalculatorArgs) -> str:
        """简单计算器工具"""
        if args.operation == "add":
            return str(args.a + args.b)
        elif args.operation == "multiply":
            return str(args.a * args.b)
        else:
            return "不支持的操作"
    
    tool = FunctionTool(calculator, description="执行基本数学运算")
    
    print("创建支持工具调用的uni大模型客户端...")
    client = create_uni_model_client()
    
    print("测试工具调用...")
    try:
        response = await client.create(
            messages=[UserMessage(content="请计算 15 + 27", source="user")],
            tools=[tool]
        )
        
        print("uni大模型工具调用响应:")
        print(f"- 内容: {response.content}")
        
    except Exception as e:
        print(f"工具调用测试失败: {e}")


def main():
    """
    主函数 - 运行所有测试
    """
    print("=" * 50)
    print("uni大模型集成测试")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv("UNI_API_KEY"):
        print("警告: 未设置UNI_API_KEY环境变量")
        print("请设置环境变量: export UNI_API_KEY=your-actual-api-key")
        print()
    
    # 运行基本测试
    print("1. 基本对话测试")
    asyncio.run(test_uni_model())
    print()
    
    # 运行工具调用测试
    print("2. 工具调用测试")
    asyncio.run(test_uni_model_with_tools())
    print()
    
    print("测试完成！")


if __name__ == "__main__":
    main()
