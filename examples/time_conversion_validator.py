#!/usr/bin/env python3
"""
时间转换验证脚本
验证UTC时间与北京时间转换的正确性，确保文件定位准确
"""

from datetime import datetime, timedelta
from typing import List, <PERSON><PERSON>


def utc_to_beijing_conversion_test():
    """
    测试UTC时间到北京时间的转换逻辑
    """
    print("=" * 60)
    print("UTC时间 → 北京时间转换验证")
    print("=" * 60)
    
    test_cases = [
        # 格式: (UTC时间, 预期北京日期, 预期北京小时, 说明)
        ("2025-04-26T19:10:15Z", "2025-04-27", "03", "UTC晚上19点 -> 北京时间次日凌晨3点"),
        ("2025-04-26T23:30:00Z", "2025-04-27", "07", "UTC晚上23点半 -> 北京时间次日早上7点半"),
        ("2025-04-27T01:00:00Z", "2025-04-27", "09", "UTC凌晨1点 -> 北京时间上午9点"),
        ("2025-04-26T16:00:00Z", "2025-04-27", "00", "UTC下午4点 -> 北京时间午夜"),
        ("2025-04-26T15:59:59Z", "2025-04-26", "23", "UTC下午3点59分 -> 北京时间晚上11点59分"),
        ("2025-04-27T00:00:00Z", "2025-04-27", "08", "UTC午夜 -> 北京时间上午8点"),
    ]
    
    for utc_time, expected_date, expected_hour, description in test_cases:
        # 执行转换
        utc_dt = datetime.fromisoformat(utc_time.replace('Z', '+00:00'))
        beijing_dt = utc_dt + timedelta(hours=8)
        
        actual_date = beijing_dt.strftime('%Y-%m-%d')
        actual_hour = beijing_dt.strftime('%H')
        
        # 验证结果
        date_correct = actual_date == expected_date
        hour_correct = actual_hour == expected_hour
        
        status = "✅" if (date_correct and hour_correct) else "❌"
        
        print(f"{status} {description}")
        print(f"   UTC时间: {utc_time}")
        print(f"   北京时间: {actual_date} {actual_hour}:{beijing_dt.strftime('%M')}:{beijing_dt.strftime('%S')}")
        print(f"   预期: {expected_date} {expected_hour}:xx:xx")
        
        if not (date_correct and hour_correct):
            print(f"   ❌ 错误: 日期{'正确' if date_correct else '错误'}, 小时{'正确' if hour_correct else '错误'}")
        print()


def file_path_construction_test():
    """
    测试文件路径构建逻辑
    """
    print("=" * 60)
    print("文件路径构建验证")
    print("=" * 60)
    
    def construct_file_paths(utc_time: str) -> dict:
        """根据UTC时间构建文件路径"""
        utc_dt = datetime.fromisoformat(utc_time.replace('Z', '+00:00'))
        beijing_dt = utc_dt + timedelta(hours=8)
        
        beijing_date = beijing_dt.strftime('%Y-%m-%d')
        beijing_hour = beijing_dt.strftime('%H')
        
        base_path = f"/data/phaseone_data/{beijing_date}/{beijing_date}"
        
        return {
            "metric_dir": f"{base_path}/metric-parquet/",
            "trace_file": f"{base_path}/trace-parquet/trace_jaeger-span_{beijing_date}_{beijing_hour}-00-00.parquet",
            "log_file": f"{base_path}/log-parquet/log_filebeat-server_{beijing_date}_{beijing_hour}-00-00.parquet"
        }
    
    test_cases = [
        ("2025-04-26T19:10:15Z", "故障时间在UTC晚上7点"),
        ("2025-04-26T23:45:30Z", "故障时间在UTC晚上11点45分"),
        ("2025-04-27T01:30:00Z", "故障时间在UTC凌晨1点30分"),
    ]
    
    for utc_time, description in test_cases:
        print(f"📁 {description}")
        print(f"   UTC时间: {utc_time}")
        
        paths = construct_file_paths(utc_time)
        
        for path_type, path in paths.items():
            print(f"   {path_type}: {path}")
        print()


def time_range_file_mapping_test():
    """
    测试时间范围到文件映射的逻辑
    """
    print("=" * 60)
    print("时间范围文件映射验证")
    print("=" * 60)
    
    def get_required_files(start_utc: str, end_utc: str) -> List[Tuple[str, str]]:
        """获取时间范围内需要的所有文件"""
        start_dt = datetime.fromisoformat(start_utc.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_utc.replace('Z', '+00:00'))
        
        # 扩展时间窗口
        extended_start = start_dt - timedelta(hours=1)
        extended_end = end_dt + timedelta(hours=1)
        
        files = []
        current_dt = extended_start
        
        while current_dt <= extended_end:
            beijing_dt = current_dt + timedelta(hours=8)
            beijing_date = beijing_dt.strftime('%Y-%m-%d')
            beijing_hour = beijing_dt.strftime('%H')
            
            files.append((beijing_date, beijing_hour))
            current_dt += timedelta(hours=1)
        
        return files
    
    test_cases = [
        ("2025-04-26T19:10:15Z", "2025-04-26T19:40:15Z", "30分钟故障窗口"),
        ("2025-04-26T23:30:00Z", "2025-04-27T01:30:00Z", "跨日期2小时故障窗口"),
        ("2025-04-26T22:00:00Z", "2025-04-27T02:00:00Z", "跨日期4小时故障窗口"),
    ]
    
    for start_utc, end_utc, description in test_cases:
        print(f"⏰ {description}")
        print(f"   故障时间范围: {start_utc} to {end_utc}")
        
        required_files = get_required_files(start_utc, end_utc)
        
        print(f"   需要加载的文件（包含扩展窗口）:")
        for beijing_date, beijing_hour in required_files:
            print(f"     - {beijing_date} {beijing_hour}:00 (北京时间)")
        
        print(f"   总计: {len(required_files)} 个小时的文件")
        print()


def data_filtering_logic_test():
    """
    测试数据过滤逻辑
    """
    print("=" * 60)
    print("数据过滤逻辑验证")
    print("=" * 60)
    
    # 模拟数据记录
    sample_data = [
        {"timestamp": "2025-04-26T19:05:00Z", "component": "frontend", "value": 100},
        {"timestamp": "2025-04-26T19:15:00Z", "component": "checkoutservice", "value": 200},
        {"timestamp": "2025-04-26T19:25:00Z", "component": "cartservice", "value": 150},
        {"timestamp": "2025-04-26T19:35:00Z", "component": "paymentservice", "value": 180},
        {"timestamp": "2025-04-26T19:45:00Z", "component": "emailservice", "value": 120},
    ]
    
    # 故障时间窗口
    fault_start = "2025-04-26T19:10:15Z"
    fault_end = "2025-04-26T19:40:15Z"
    
    print(f"故障时间窗口: {fault_start} to {fault_end}")
    print("原始数据记录:")
    for record in sample_data:
        print(f"  {record['timestamp']} - {record['component']}: {record['value']}")
    
    # 过滤逻辑
    start_dt = datetime.fromisoformat(fault_start.replace('Z', '+00:00'))
    end_dt = datetime.fromisoformat(fault_end.replace('Z', '+00:00'))
    
    filtered_data = []
    for record in sample_data:
        record_dt = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
        if start_dt <= record_dt <= end_dt:
            filtered_data.append(record)
    
    print(f"\n过滤后的数据记录 ({len(filtered_data)}/{len(sample_data)}):")
    for record in filtered_data:
        print(f"  ✅ {record['timestamp']} - {record['component']}: {record['value']}")
    
    print(f"\n被过滤掉的记录:")
    for record in sample_data:
        if record not in filtered_data:
            print(f"  ❌ {record['timestamp']} - {record['component']}: {record['value']}")


def main():
    """
    运行所有验证测试
    """
    print("🕐 AIOps数据加载时间转换验证工具")
    print("验证UTC时间与北京时间转换的正确性")
    print()
    
    # 运行所有测试
    utc_to_beijing_conversion_test()
    file_path_construction_test()
    time_range_file_mapping_test()
    data_filtering_logic_test()
    
    print("=" * 60)
    print("✅ 所有验证测试完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
