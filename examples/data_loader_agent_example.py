#!/usr/bin/env python3
"""
DataLoaderAgent 实现示例
重点展示UTC时间与北京时间的转换逻辑，以及数据文件的准确定位和加载
"""

import os
import pandas as pd
import pyarrow.parquet as pq
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any
from pathlib import Path


class DataLoaderAgent:
    """
    数据加载智能体
    核心功能：处理UTC与北京时间转换，准确定位和加载数据文件
    """
    
    def __init__(self, data_root_path: str = "/data/phaseone_data"):
        self.data_root_path = Path(data_root_path)
        
    def utc_to_beijing_time(self, utc_time_str: str) -> Tuple[str, str]:
        """
        将UTC时间转换为北京时间，用于文件名定位
        
        Args:
            utc_time_str: UTC时间字符串，如 "2025-04-26T19:10:15Z"
            
        Returns:
            Tuple[str, str]: (北京日期, 北京小时) 如 ("2025-04-27", "03")
        """
        # 解析UTC时间
        utc_dt = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
        
        # 转换为北京时间（UTC+8）
        beijing_dt = utc_dt + timedelta(hours=8)
        
        beijing_date = beijing_dt.strftime('%Y-%m-%d')
        beijing_hour = beijing_dt.strftime('%H')
        
        return beijing_date, beijing_hour
    
    def get_time_range_files(self, start_utc: str, end_utc: str) -> Dict[str, List[str]]:
        """
        根据UTC时间范围确定需要加载的所有数据文件

        Args:
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）

        Returns:
            Dict[str, List[str]]: 按数据类型分组的文件路径列表
        """
        # 不扩展时间窗口，只加载实际需要的时间范围
        start_dt = datetime.fromisoformat(start_utc.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_utc.replace('Z', '+00:00'))

        files = {
            'metrics': [],
            'traces': [],
            'logs': []
        }

        # 用于去重的集合
        metric_dates = set()
        trace_files = set()
        log_files = set()

        # 转换开始和结束时间为北京时间
        start_beijing_dt = start_dt + timedelta(hours=8)
        end_beijing_dt = end_dt + timedelta(hours=8)

        # 确定需要的小时范围
        start_hour = start_beijing_dt.replace(minute=0, second=0, microsecond=0)
        end_hour = end_beijing_dt.replace(minute=0, second=0, microsecond=0)

        # 遍历时间范围内的每个小时
        current_hour = start_hour
        while current_hour <= end_hour:
            beijing_date = current_hour.strftime('%Y-%m-%d')
            beijing_hour = current_hour.strftime('%H')

            # 构建文件路径
            date_dir = self.data_root_path / beijing_date / beijing_date

            # Metric文件（按日期组织，需要遍历所有子目录和文件）
            if beijing_date not in metric_dates:
                metric_dir = date_dir / "metric-parquet"
                if metric_dir.exists():
                    # 遍历metric目录下的所有parquet文件
                    for subdir in metric_dir.iterdir():
                        if subdir.is_dir():
                            # APM, Infra, Other等子目录
                            for sub_subdir in subdir.iterdir():
                                if sub_subdir.is_dir():
                                    # pod, service等更深层目录
                                    for parquet_file in sub_subdir.glob("*.parquet"):
                                        files['metrics'].append(str(parquet_file))
                                elif sub_subdir.suffix == '.parquet':
                                    # 直接的parquet文件
                                    files['metrics'].append(str(sub_subdir))
                        elif subdir.suffix == '.parquet':
                            # 直接在metric-parquet下的parquet文件
                            files['metrics'].append(str(subdir))
                    metric_dates.add(beijing_date)

            # Trace文件（按小时，避免重复）
            trace_filename = f"trace_jaeger-span_{beijing_date}_{beijing_hour}-00-00.parquet"
            trace_file = date_dir / "trace-parquet" / trace_filename
            if trace_file.exists() and str(trace_file) not in trace_files:
                files['traces'].append(str(trace_file))
                trace_files.add(str(trace_file))

            # Log文件（按小时，避免重复）
            log_filename = f"log_filebeat-server_{beijing_date}_{beijing_hour}-00-00.parquet"
            log_file = date_dir / "log-parquet" / log_filename
            if log_file.exists() and str(log_file) not in log_files:
                files['logs'].append(str(log_file))
                log_files.add(str(log_file))

            # 移动到下一小时
            current_hour += timedelta(hours=1)

        return files
    
    def load_metric_data(self, start_utc: str, end_utc: str) -> pd.DataFrame:
        """
        加载指标数据
        
        Args:
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）
            
        Returns:
            pd.DataFrame: 过滤后的指标数据
        """
        files = self.get_time_range_files(start_utc, end_utc)
        all_metric_data = []
        
        for metric_dir in files['metrics']:
            metric_path = Path(metric_dir)
            
            # 加载APM指标
            apm_dir = metric_path / "apm"
            if apm_dir.exists():
                # 加载pod级别APM数据
                pod_dir = apm_dir / "pod"
                if pod_dir.exists():
                    for pod_file in pod_dir.glob("*.parquet"):
                        df = pd.read_parquet(pod_file)
                        all_metric_data.append(df)
                
                # 加载service级别APM数据
                service_dir = apm_dir / "service"
                if service_dir.exists():
                    for service_file in service_dir.glob("*.parquet"):
                        df = pd.read_parquet(service_file)
                        all_metric_data.append(df)
            
            # 加载Infra指标
            infra_dir = metric_path / "infra"
            if infra_dir.exists():
                for subdir in infra_dir.iterdir():
                    if subdir.is_dir():
                        for infra_file in subdir.glob("*.parquet"):
                            df = pd.read_parquet(infra_file)
                            all_metric_data.append(df)
        
        if not all_metric_data:
            return pd.DataFrame()
        
        # 合并所有指标数据
        combined_df = pd.concat(all_metric_data, ignore_index=True)
        
        # 使用UTC时间过滤数据
        return self.filter_data_by_utc_time(combined_df, start_utc, end_utc, 'time')
    
    def load_trace_data(self, start_utc: str, end_utc: str) -> pd.DataFrame:
        """
        加载调用链数据
        
        Args:
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）
            
        Returns:
            pd.DataFrame: 过滤后的调用链数据
        """
        files = self.get_time_range_files(start_utc, end_utc)
        all_trace_data = []
        
        for trace_file in files['traces']:
            if os.path.exists(trace_file):
                df = pd.read_parquet(trace_file)
                all_trace_data.append(df)
        
        if not all_trace_data:
            return pd.DataFrame()
        
        # 合并所有调用链数据
        combined_df = pd.concat(all_trace_data, ignore_index=True)
        
        # 使用UTC时间过滤数据（基于startTimeMillis字段）
        return self.filter_trace_data_by_utc_time(combined_df, start_utc, end_utc)
    
    def load_log_data(self, start_utc: str, end_utc: str) -> pd.DataFrame:
        """
        加载日志数据
        
        Args:
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）
            
        Returns:
            pd.DataFrame: 过滤后的日志数据
        """
        files = self.get_time_range_files(start_utc, end_utc)
        all_log_data = []
        
        for log_file in files['logs']:
            if os.path.exists(log_file):
                df = pd.read_parquet(log_file)
                all_log_data.append(df)
        
        if not all_log_data:
            return pd.DataFrame()
        
        # 合并所有日志数据
        combined_df = pd.concat(all_log_data, ignore_index=True)
        
        # 使用UTC时间过滤数据
        return self.filter_data_by_utc_time(combined_df, start_utc, end_utc, '@timestamp')
    
    def filter_data_by_utc_time(self, df: pd.DataFrame, start_utc: str, end_utc: str, time_column: str) -> pd.DataFrame:
        """
        使用UTC时间过滤数据
        
        Args:
            df: 数据DataFrame
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）
            time_column: 时间列名
            
        Returns:
            pd.DataFrame: 过滤后的数据
        """
        if df.empty or time_column not in df.columns:
            return df
        
        # 转换时间列为datetime类型
        df[time_column] = pd.to_datetime(df[time_column])
        
        # 转换UTC时间字符串为datetime
        start_dt = pd.to_datetime(start_utc)
        end_dt = pd.to_datetime(end_utc)
        
        # 过滤数据
        mask = (df[time_column] >= start_dt) & (df[time_column] <= end_dt)
        return df[mask].copy()
    
    def filter_trace_data_by_utc_time(self, df: pd.DataFrame, start_utc: str, end_utc: str) -> pd.DataFrame:
        """
        使用UTC时间过滤调用链数据（特殊处理startTimeMillis字段）
        
        Args:
            df: 调用链数据DataFrame
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）
            
        Returns:
            pd.DataFrame: 过滤后的调用链数据
        """
        if df.empty or 'startTimeMillis' not in df.columns:
            return df
        
        # 转换UTC时间字符串为毫秒时间戳
        start_dt = pd.to_datetime(start_utc)
        end_dt = pd.to_datetime(end_utc)
        start_millis = int(start_dt.timestamp() * 1000)
        end_millis = int(end_dt.timestamp() * 1000)
        
        # 过滤数据
        mask = (df['startTimeMillis'] >= start_millis) & (df['startTimeMillis'] <= end_millis)
        return df[mask].copy()
    
    def load_all_data(self, start_utc: str, end_utc: str) -> Dict[str, pd.DataFrame]:
        """
        加载所有类型的数据
        
        Args:
            start_utc: 开始时间（UTC）
            end_utc: 结束时间（UTC）
            
        Returns:
            Dict[str, pd.DataFrame]: 包含所有数据类型的字典
        """
        print(f"Loading data for time range: {start_utc} to {end_utc}")
        
        # 显示时间转换信息
        start_beijing_date, start_beijing_hour = self.utc_to_beijing_time(start_utc)
        end_beijing_date, end_beijing_hour = self.utc_to_beijing_time(end_utc)
        print(f"Beijing time range: {start_beijing_date} {start_beijing_hour}:00 to {end_beijing_date} {end_beijing_hour}:00")
        
        data = {
            'metrics': self.load_metric_data(start_utc, end_utc),
            'traces': self.load_trace_data(start_utc, end_utc),
            'logs': self.load_log_data(start_utc, end_utc)
        }
        
        # 显示加载统计
        for data_type, df in data.items():
            print(f"Loaded {len(df)} records of {data_type} data")
        
        return data


def test_data_loader():
    """
    测试DataLoaderAgent的时间转换和数据加载功能
    """
    # 创建DataLoaderAgent实例
    loader = DataLoaderAgent()
    
    # 测试时间转换
    test_cases = [
        "2025-06-06T10:10:15Z",  
        "2025-06-06T12:30:00Z",  
        "2025-06-06T13:00:00Z",  
    ]
    
    print("=== 时间转换测试 ===")
    for utc_time in test_cases:
        beijing_date, beijing_hour = loader.utc_to_beijing_time(utc_time)
        print(f"UTC: {utc_time} -> 北京时间: {beijing_date} {beijing_hour}:00")
    
    # 测试1: 同一小时内的时间范围
    print("\n=== 测试1: 同一小时内的时间范围 ===")
    start_utc = "2025-06-06T11:10:15Z"
    end_utc = "2025-06-06T11:40:15Z"

    files = loader.get_time_range_files(start_utc, end_utc)
    print(f"时间范围: {start_utc} to {end_utc}")

    # 显示时间转换信息
    start_beijing_date, start_beijing_hour = loader.utc_to_beijing_time(start_utc)
    end_beijing_date, end_beijing_hour = loader.utc_to_beijing_time(end_utc)
    print(f"北京时间范围: {start_beijing_date} {start_beijing_hour}:10 to {end_beijing_date} {end_beijing_hour}:40")
    print(f"预期结果: 只需要 {start_beijing_hour}:00 的文件（同一小时内）")

    for data_type, file_list in files.items():
        print(f"{data_type}: {len(file_list)} files")
        for file_path in file_list:
            print(f"  - {file_path}")

    # 测试2: 跨小时的情况
    print("\n=== 测试2: 跨小时的时间范围 ===")
    start_utc2 = "2025-06-06T10:30:00Z"  # UTC 10:30 -> 北京时间 18:30
    end_utc2 = "2025-06-06T12:30:00Z"    # UTC 12:30 -> 北京时间 20:30

    files2 = loader.get_time_range_files(start_utc2, end_utc2)
    print(f"时间范围: {start_utc2} to {end_utc2}")

    start_beijing_date2, start_beijing_hour2 = loader.utc_to_beijing_time(start_utc2)
    end_beijing_date2, end_beijing_hour2 = loader.utc_to_beijing_time(end_utc2)
    print(f"北京时间范围: {start_beijing_date2} {start_beijing_hour2}:30 to {end_beijing_date2} {end_beijing_hour2}:30")
    print(f"预期结果: 需要 {start_beijing_hour2}:00, 19:00, {end_beijing_hour2}:00 的文件（跨3个小时）")

    for data_type, file_list in files2.items():
        print(f"{data_type}: {len(file_list)} files")
        for file_path in file_list:
            print(f"  - {file_path}")


if __name__ == "__main__":
    test_data_loader()
