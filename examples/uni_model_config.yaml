# uni大模型配置文件示例
# 适用于AutoGen项目中需要使用配置文件的场景

# 基本uni大模型配置（推荐方式）
uni_model_basic:
  provider: "OpenAIChatCompletionClient"
  config:
    model: "deepseek-v3:671b"  # uni大模型的实际模型名称
    base_url: "https://uni-api.cstcloud.cn/v1"
    api_key: "2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286"  # 从环境变量读取
    # 必须指定model_info，因为模型不在标准OpenAI模型列表中
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "UNKNOWN"
    timeout: 30.0
    max_retries: 3

# 带完整配置的uni大模型
uni_model_full_config:
  provider: "OpenAIChatCompletionClient"
  config:
    model: "deepseek-v3:671b"
    base_url: "https://uni-api.cstcloud.cn/v1"
    api_key: "2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286"
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "UNKNOWN"
    timeout: 60.0
    max_retries: 5
    temperature: 0.7
    max_tokens: 2048

# 用于生产环境的uni大模型配置
uni_model_production:
  provider: "OpenAIChatCompletionClient"
  config:
    model: "deepseek-v3:671b"
    base_url: "https://uni-api.cstcloud.cn/v1"
    api_key: "2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286"
    model_capabilities:
      vision: false
      function_calling: true
      json_output: true
    # 生产环境优化配置
    timeout: 120.0
    max_retries: 3
    temperature: 0.1  # 更稳定的输出
    max_tokens: 4096
    # 可选的创建参数
    frequency_penalty: 0.0
    presence_penalty: 0.0
    top_p: 1.0




