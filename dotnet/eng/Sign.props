<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
<PropertyGroup>
  <SignType></SignType>
</PropertyGroup>

<ItemGroup Condition="'$(SignType)' == 'Test' OR '$(SignType)' == 'REAL'">
    <PackageReference Include="Microsoft.VisualStudioEng.MicroBuild.Core" Version="1.0.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>

    <FilesToSign Include="$(OutDir)\AutoGen*.dll"> 
        <Authenticode>Microsoft400</Authenticode> 
    </FilesToSign> 

    <!-- nuget package -->
    <FilesToSign Include="$(OutDir)\AutoGen*.nupkg"> 
        <Authenticode>NuGet</Authenticode>
    </FilesToSign>
</ItemGroup>
</Project>
