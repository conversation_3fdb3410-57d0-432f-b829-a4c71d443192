<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <OutputType>Exe</OutputType>
      <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
      <ImplicitUsings>enable</ImplicitUsings>
      <Nullable>enable</Nullable>
      <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\src\AutoGen.Anthropic\AutoGen.Anthropic.csproj" />
      <ProjectReference Include="..\..\..\src\AutoGen.DotnetInteractive\AutoGen.DotnetInteractive.csproj" />
      <ProjectReference Include="..\..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
      <ProjectReference Include="..\..\..\src\AutoGen\AutoGen.csproj" />
      <PackageReference Include="FluentAssertions" />
    </ItemGroup>

</Project>
