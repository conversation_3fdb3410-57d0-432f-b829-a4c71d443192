# Multiproject App Host for HelloAgent

This is a [.NET Aspire](https://learn.microsoft.com/en-us/dotnet/aspire/get-started/aspire-overview) App Host that starts up the HelloAgent project and the agents backend. Once the project starts up you will be able to view the telemetry and logs in the [Aspire Dashboard](https://learn.microsoft.com/en-us/dotnet/aspire/get-started/aspire-dashboard) using the link provided in the console.

```shell
cd Hello.AppHost
dotnet run
```

For more info see the HelloAgent [README](../HelloAgent/README.md).
