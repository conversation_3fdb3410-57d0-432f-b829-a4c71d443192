<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>e8874200-80ab-41e3-bb56-b5bb93974eea</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" />
    <PackageReference Include="Aspire.Hosting.Azure.ApplicationInsights" />
    <PackageReference Include="Aspire.Hosting.Azure.CognitiveServices" />
    <PackageReference Include="Aspire.Hosting.Orleans" />
    <PackageReference Include="Aspire.Hosting.Qdrant" />
  </ItemGroup>

   <ItemGroup>
    <ProjectReference Include="..\DevTeam.Backend\DevTeam.Backend.csproj" />
  </ItemGroup>

</Project>
