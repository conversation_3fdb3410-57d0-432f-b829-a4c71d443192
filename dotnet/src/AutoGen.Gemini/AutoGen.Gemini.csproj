<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.Gemini</Title>
    <Description>
      This package provides the intergration with Gemini.
    </Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Google.Cloud.AIPlatform.V1" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="AutoGen.Gemini.Tests" />
  </ItemGroup>
</Project>
