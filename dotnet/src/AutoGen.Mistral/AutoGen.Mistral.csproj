﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
    <RootNamespace>AutoGen.Mistral</RootNamespace>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.Mistral</Title>
    <Description>
      Provide support for consuming Mistral model in AutoGen
    </Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
  </ItemGroup>


</Project>
