<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
    <RootNamespace>AutoGen.OpenAI</RootNamespace>
    <NoWarn>$(NoWarn);OPENAI001</NoWarn>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.OpenAI</Title>
    <Description>
      OpenAI Intergration for AutoGen.
      If your project still depends on Azure.AI.OpenAI v1, please use AutoGen.OpenAI.V1 package instead.
    </Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="OpenAI" Version="$(OpenAISDKVersion)" />
    <ProjectReference Include="..\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
  </ItemGroup>

</Project>
