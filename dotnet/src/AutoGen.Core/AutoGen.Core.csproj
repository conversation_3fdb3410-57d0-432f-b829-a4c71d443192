<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
    <RootNamespace>AutoGen.Core</RootNamespace>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.Core</Title>
    <Description>
      Core library for AutoGen. This package provides contracts and core functionalities for AutoGen.
    </Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="JsonSchema.Net.Generation" />
    <PackageReference Include="System.Memory.Data" />
    <PackageReference Include="Microsoft.Extensions.AI.Abstractions" />
  </ItemGroup>
  
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" />
   </ItemGroup>

</Project>
