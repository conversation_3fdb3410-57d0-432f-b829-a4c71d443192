<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
    <RootNamespace>AutoGen.OpenAI</RootNamespace>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.OpenAI.V1</Title>
    <Description>
      OpenAI Intergration for AutoGen.
      This package connects to openai using Azure.AI.OpenAI v1 package. It is reserved to keep compatibility with the projects which stick to that v1 package.
      To use the latest version of OpenAI SDK, please use AutoGen.OpenAI package.
    </Description>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Azure.AI.OpenAI" VersionOverride="1.0.0-beta.17" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
  </ItemGroup>

</Project>
