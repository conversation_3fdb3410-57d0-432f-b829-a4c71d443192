﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ContainerRepository>autogen-host</ContainerRepository>
    <ContainerFamily>alpine</ContainerFamily>
    <EnableSdkContainerSupport>true</EnableSdkContainerSupport>
     </PropertyGroup>
  <ItemGroup>
    <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="5001" />
    <ContainerPort Include="5001" Type="tcp" />
  </ItemGroup>
  <ItemGroup>
	   <PackageReference Include="Microsoft.Extensions.Hosting" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Runtime.Grpc\Microsoft.AutoGen.Runtime.Grpc.csproj" />
    <ProjectReference Include="..\Extensions\Aspire\Microsoft.AutoGen.Extensions.Aspire.csproj" />
  </ItemGroup>
</Project>