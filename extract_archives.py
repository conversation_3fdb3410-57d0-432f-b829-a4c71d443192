#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解压工具：将 /data/phasetwo 目录下的压缩包解压到 /data/phaseone_data 目录
支持的压缩格式：zip, tar, tar.gz, tar.bz2, tar.xz, 7z, rar
"""

import os
import sys
import zipfile
import tarfile
import shutil
import logging
from pathlib import Path
from typing import List, Optional
import subprocess


class ArchiveExtractor:
    """压缩包解压器"""
    
    def __init__(self, source_dir: str = "/data/phasetwo", target_dir: str = "/data/phaseone_data"):
        """
        初始化解压器
        
        Args:
            source_dir: 源目录，包含压缩包的目录
            target_dir: 目标目录，解压后文件存放的目录
        """
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('extract_archives.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 支持的压缩格式
        self.supported_formats = {
            '.zip': self._extract_zip,
            '.tar': self._extract_tar,
            '.tar.gz': self._extract_tar,
            '.tgz': self._extract_tar,
            '.tar.bz2': self._extract_tar,
            '.tbz2': self._extract_tar,
            '.tar.xz': self._extract_tar,
            '.txz': self._extract_tar,
            '.7z': self._extract_7z,
            '.rar': self._extract_rar
        }
    
    def _create_target_dir(self) -> None:
        """创建目标目录"""
        try:
            self.target_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"目标目录已创建或已存在: {self.target_dir}")
        except Exception as e:
            self.logger.error(f"创建目标目录失败: {e}")
            raise
    
    def _get_archive_files(self) -> List[Path]:
        """获取源目录下的所有压缩包文件"""
        if not self.source_dir.exists():
            self.logger.error(f"源目录不存在: {self.source_dir}")
            return []
        
        archive_files = []
        for file_path in self.source_dir.rglob('*'):
            if file_path.is_file():
                # 检查文件扩展名
                file_lower = file_path.name.lower()
                for ext in self.supported_formats.keys():
                    if file_lower.endswith(ext):
                        archive_files.append(file_path)
                        break
        
        self.logger.info(f"找到 {len(archive_files)} 个压缩包文件")
        return archive_files
    
    def _get_extract_path(self, archive_path: Path) -> Path:
        """获取解压路径，基于压缩包文件名创建子目录"""
        # 移除所有可能的压缩扩展名
        name = archive_path.name
        for ext in sorted(self.supported_formats.keys(), key=len, reverse=True):
            if name.lower().endswith(ext):
                name = name[:-len(ext)]
                break
        
        return self.target_dir / name
    
    def _extract_zip(self, archive_path: Path, extract_path: Path) -> bool:
        """解压 ZIP 文件"""
        try:
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            return True
        except Exception as e:
            self.logger.error(f"解压 ZIP 文件失败 {archive_path}: {e}")
            return False
    
    def _extract_tar(self, archive_path: Path, extract_path: Path) -> bool:
        """解压 TAR 相关文件"""
        try:
            with tarfile.open(archive_path, 'r:*') as tar_ref:
                tar_ref.extractall(extract_path)
            return True
        except Exception as e:
            self.logger.error(f"解压 TAR 文件失败 {archive_path}: {e}")
            return False
    
    def _extract_7z(self, archive_path: Path, extract_path: Path) -> bool:
        """解压 7Z 文件（需要 7z 命令行工具）"""
        try:
            # 检查 7z 命令是否可用
            result = subprocess.run(['7z'], capture_output=True, text=True)
            if result.returncode != 0:
                self.logger.warning("7z 命令不可用，跳过 7z 文件")
                return False
            
            # 使用 7z 命令解压
            cmd = ['7z', 'x', str(archive_path), f'-o{extract_path}', '-y']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                self.logger.error(f"7z 解压失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"解压 7Z 文件失败 {archive_path}: {e}")
            return False
    
    def _extract_rar(self, archive_path: Path, extract_path: Path) -> bool:
        """解压 RAR 文件（需要 unrar 命令行工具）"""
        try:
            # 检查 unrar 命令是否可用
            result = subprocess.run(['unrar'], capture_output=True, text=True)
            if result.returncode != 0:
                self.logger.warning("unrar 命令不可用，跳过 RAR 文件")
                return False
            
            # 使用 unrar 命令解压
            cmd = ['unrar', 'x', str(archive_path), str(extract_path) + '/']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                self.logger.error(f"unrar 解压失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"解压 RAR 文件失败 {archive_path}: {e}")
            return False
    
    def extract_single_archive(self, archive_path: Path) -> bool:
        """解压单个压缩包"""
        self.logger.info(f"开始解压: {archive_path}")
        
        # 确定解压路径
        extract_path = self._get_extract_path(archive_path)
        extract_path.mkdir(parents=True, exist_ok=True)
        
        # 确定解压方法
        file_lower = archive_path.name.lower()
        extract_method = None
        
        for ext, method in self.supported_formats.items():
            if file_lower.endswith(ext):
                extract_method = method
                break
        
        if extract_method is None:
            self.logger.warning(f"不支持的文件格式: {archive_path}")
            return False
        
        # 执行解压
        success = extract_method(archive_path, extract_path)
        
        if success:
            self.logger.info(f"解压成功: {archive_path} -> {extract_path}")
        else:
            self.logger.error(f"解压失败: {archive_path}")
            # 清理失败的解压目录
            if extract_path.exists() and extract_path.is_dir():
                try:
                    shutil.rmtree(extract_path)
                except Exception as e:
                    self.logger.warning(f"清理失败目录时出错: {e}")
        
        return success
    
    def extract_all(self) -> dict:
        """解压所有压缩包"""
        self.logger.info("开始批量解压操作")
        
        # 创建目标目录
        self._create_target_dir()
        
        # 获取所有压缩包文件
        archive_files = self._get_archive_files()
        
        if not archive_files:
            self.logger.warning("未找到任何压缩包文件")
            return {"success": 0, "failed": 0, "total": 0}
        
        # 统计结果
        results = {"success": 0, "failed": 0, "total": len(archive_files)}
        failed_files = []
        
        # 逐个解压
        for archive_path in archive_files:
            try:
                if self.extract_single_archive(archive_path):
                    results["success"] += 1
                else:
                    results["failed"] += 1
                    failed_files.append(str(archive_path))
            except Exception as e:
                self.logger.error(f"处理文件时发生异常 {archive_path}: {e}")
                results["failed"] += 1
                failed_files.append(str(archive_path))
        
        # 输出统计结果
        self.logger.info(f"解压完成! 总计: {results['total']}, 成功: {results['success']}, 失败: {results['failed']}")
        
        if failed_files:
            self.logger.warning(f"失败的文件: {failed_files}")
        
        return results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="解压工具")
    parser.add_argument("--source", "-s", default="/data/phasetwo", 
                       help="源目录路径 (默认: /data/phasetwo)")
    parser.add_argument("--target", "-t", default="/data/phaseone_data", 
                       help="目标目录路径 (默认: /data/phaseone_data)")
    parser.add_argument("--file", "-f", help="解压指定的单个文件")
    
    args = parser.parse_args()
    
    # 创建解压器
    extractor = ArchiveExtractor(args.source, args.target)
    
    try:
        if args.file:
            # 解压单个文件
            file_path = Path(args.file)
            if not file_path.exists():
                print(f"错误: 文件不存在 {file_path}")
                sys.exit(1)
            
            success = extractor.extract_single_archive(file_path)
            sys.exit(0 if success else 1)
        else:
            # 批量解压
            results = extractor.extract_all()
            sys.exit(0 if results["failed"] == 0 else 1)
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
