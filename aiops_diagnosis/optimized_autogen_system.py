#!/usr/bin/env python3
"""
优化版AutoGen AIOps诊断系统
实现并行处理、两阶段数据处理流程和标准化输出
"""

import asyncio
import logging
import os
import time
from typing import Dict, Any, Optional, List
from autogen_core import SingleThreadedAgentRuntime, AgentId, AgentProxy
from .autogen_agents.data_loader_agent import AutoGenDataLoaderAgent
from .autogen_agents.simple_metric_agent import SimpleMetricAgent
from .autogen_agents.enhanced_trace_agent import EnhancedTraceAnalysisAgent
from .autogen_agents.trace_analysis_agent import AutoGenTraceAnalysisAgent
from .autogen_agents.log_analysis_agent import AutoGenLogAnalysisAgent
from .autogen_agents.new_reasoning_agent import NewReasoningAgent
from .autogen_agents.base_aiops_agent import (
    DiagnosisRequest,
    DataLoadRequest,
    AnalysisRequest,
    ReasoningRequest,
    PreciseDataLoadRequest,
    DataLocation
)
from .config.config_manager import config_manager

logger = logging.getLogger(__name__)


class OptimizedAutoGenAIOpsSystem:
    """优化版AutoGen AIOps诊断系统 - 并行处理和两阶段数据流程"""
    
    def __init__(self, data_root_path: str = "/data/phaseone_data", enable_llm: bool = True,
                 performance_mode: str = "balanced"):
        self.data_root_path = data_root_path
        self.enable_llm = True  # AIOps系统必须依赖LLM
        self.runtime = None
        self.agent_proxies = {}

        # 加载并发配置
        self.concurrency_config = config_manager.load_config(performance_mode=performance_mode)

        # 优化配置 - 🚨 紧急提升超时阈值
        self.config = {
            "parallel_timeout": 900,  # 并行处理超时时间（提升到15分钟）
            "individual_timeout": 600,  # 单个Agent超时时间（提升到10分钟）
            "llm_quality_threshold": 0.7,  # LLM质量阈值
            "max_retry_attempts": 3,  # 最大重试次数
            "retry_delay": 2,  # 重试延迟（秒）
            "enable_two_stage_processing": True,  # 启用两阶段处理
            "agent_execution_times": {}  # 添加Agent执行时间记录
        }
        
    async def initialize(self):
        """初始化优化系统"""
        try:
            logger.info("🚀 初始化优化版AutoGen AIOps系统...")

            # 打印并发配置摘要
            config_manager.print_config_summary()

            # 创建运行时
            self.runtime = SingleThreadedAgentRuntime()

            # 注册数据加载智能体
            await AutoGenDataLoaderAgent.register(
                self.runtime,
                "DataLoader",
                lambda: AutoGenDataLoaderAgent(self.data_root_path)
            )
            self.agent_proxies["data_loader"] = AgentProxy(AgentId("DataLoader", "default"), self.runtime)

            # 注册增强版指标分析智能体 - 支持多层级异常对象检测和基线配置
            baseline_config_path = os.path.join(os.path.dirname(__file__), "config", "baseline.json")
            await SimpleMetricAgent.register(
                self.runtime,
                "MetricAnalyzer",
                lambda: SimpleMetricAgent(
                    enable_llm=self.enable_llm,
                    data_root_path=self.data_root_path,
                    baseline_config_path=baseline_config_path
                )
            )
            self.agent_proxies["metric_analyzer"] = AgentProxy(AgentId("MetricAnalyzer", "default"), self.runtime)

            # 注册增强版调用链分析智能体 - 优化性能
            await EnhancedTraceAnalysisAgent.register(
                self.runtime,
                "TraceAnalyzer",
                lambda: EnhancedTraceAnalysisAgent(self.enable_llm, self.data_root_path)
            )
            self.agent_proxies["trace_analyzer"] = AgentProxy(AgentId("TraceAnalyzer", "default"), self.runtime)

            # 注册日志分析智能体
            await AutoGenLogAnalysisAgent.register(
                self.runtime,
                "LogAnalyzer",
                lambda: AutoGenLogAnalysisAgent(self.enable_llm)
            )
            self.agent_proxies["log_analyzer"] = AgentProxy(AgentId("LogAnalyzer", "default"), self.runtime)

            # 注册新版推理智能体
            await NewReasoningAgent.register(
                self.runtime,
                "NewReasoner",
                lambda: NewReasoningAgent(self.enable_llm, self.data_root_path)
            )
            self.agent_proxies["new_reasoner"] = AgentProxy(AgentId("NewReasoner", "default"), self.runtime)

            # 启动运行时
            self.runtime.start()

            logger.info("✅ 优化版AutoGen AIOps系统初始化成功")
            logger.info(f"   注册的Agent数量: {len(self.agent_proxies)}")

        except Exception as e:
            logger.error(f"❌ 优化系统初始化失败: {e}")
            raise

    async def _send_message_with_retry(self, agent_name: str, message: Any, max_retries: int = 3, timeout: int = 120) -> Any:
        """带重试机制的消息发送"""
        # 🚨 修复Agent查找逻辑
        agent_key_mapping = {
            "MetricAnalyzer": "metric_analyzer",
            "TraceAnalyzer": "trace_analyzer",
            "LogAnalyzer": "log_analyzer",
            "NewReasoner": "new_reasoner",  # 🚨 添加NewReasoner映射
            "DataLoader": "data_loader"
        }

        agent_key = agent_key_mapping.get(agent_name)
        if not agent_key:
            # 备用查找逻辑
            agent_key = agent_name.lower().replace("analyzer", "_analyzer")

        agent_proxy = self.agent_proxies.get(agent_key)
        if not agent_proxy:
            available_agents = list(self.agent_proxies.keys())
            raise ValueError(f"Agent {agent_name} not found. Available agents: {available_agents}")

        last_exception = None
        start_time = time.time()  # 🚨 记录开始时间

        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 {agent_name} 尝试 {attempt + 1}/{max_retries}")

                # 使用超时发送消息
                result = await asyncio.wait_for(
                    self.runtime.send_message(message, agent_proxy.id),
                    timeout=timeout
                )

                # 验证结果
                if hasattr(result, 'success') and result.success:
                    execution_time = time.time() - start_time  # 🚨 计算执行时间
                    self.config["agent_execution_times"][agent_name] = execution_time
                    logger.info(f"✅ {agent_name} 第 {attempt + 1} 次尝试成功 (耗时: {execution_time:.2f}秒)")
                    return result
                else:
                    error_msg = getattr(result, 'error', 'Unknown error')
                    logger.warning(f"⚠️ {agent_name} 第 {attempt + 1} 次尝试失败: {error_msg}")
                    last_exception = Exception(f"Agent returned failure: {error_msg}")

            except asyncio.TimeoutError as e:
                logger.warning(f"⚠️ {agent_name} 第 {attempt + 1} 次尝试超时 ({timeout}s)")
                last_exception = e

            except Exception as e:
                logger.warning(f"⚠️ {agent_name} 第 {attempt + 1} 次尝试异常: {e}")
                last_exception = e

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                await asyncio.sleep(self.config["retry_delay"])

        # 所有重试都失败了 - 🚨 记录失败时的执行时间
        execution_time = time.time() - start_time
        self.config["agent_execution_times"][f"{agent_name}_FAILED"] = execution_time
        logger.error(f"❌ {agent_name} 所有 {max_retries} 次尝试都失败 (总耗时: {execution_time:.2f}秒)")
        raise last_exception or Exception(f"{agent_name} failed after {max_retries} attempts")
    
    async def diagnose(self, case_uuid: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """执行优化的故障诊断流程"""
        if not self.runtime or not self.agent_proxies:
            raise RuntimeError("System not initialized. Call initialize() first.")

        try:
            logger.info(f"🎯 开始优化诊断流程: {case_uuid}")

            # 阶段1: 增强并行LLM预处理 - 包含完整数据证据
            logger.info("📋 阶段1: 增强并行LLM预处理...")
            anomaly_summaries = await self._stage_1_parallel_llm_preprocessing(
                case_uuid, start_time, end_time
            )

            # 阶段2已优化：专业Agent在阶段1已提供完整数据，无需二次请求
            logger.info("📋 阶段2: 数据完整性验证...")
            self._validate_complete_data_availability(anomaly_summaries)

            # 阶段3: 新版推理Agent综合分析（直接使用完整数据）
            logger.info("📋 阶段3: 综合推理分析...")
            final_result = await self._stage_3_comprehensive_reasoning(
                case_uuid, start_time, end_time, anomaly_summaries, None  # 不再需要detailed_data
            )

            return self._format_diagnosis_result(final_result)

        except Exception as e:
            logger.error(f"❌ 优化诊断失败: {e}")
            # 直接抛出异常，不使用备用机制
            raise e

    async def _stage_1_parallel_llm_preprocessing(self, case_uuid: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """阶段1: 并行LLM预处理 - 识别和提取异常信息"""
        logger.info("⚡ 启动并行LLM预处理...")
        
        # 创建分析请求
        analysis_requests = {
            "metrics": AnalysisRequest(
                case_uuid=case_uuid,
                start_time=start_time,
                end_time=end_time,
                data={"stage": "llm_preprocessing", "focus": "anomaly_detection"},
                analysis_type="metrics"
            ),
            "traces": AnalysisRequest(
                case_uuid=case_uuid,
                start_time=start_time,
                end_time=end_time,
                data={"stage": "llm_preprocessing", "focus": "anomaly_detection"},
                analysis_type="traces"
            ),
            "logs": AnalysisRequest(
                case_uuid=case_uuid,
                start_time=start_time,
                end_time=end_time,
                data={"stage": "llm_preprocessing", "focus": "anomaly_detection"},
                analysis_type="logs"
            )
        }

        # 并行发送请求给所有专业Agent（使用重试机制）
        tasks = []
        agent_name_mapping = {
            "metrics": "MetricAnalyzer",
            "traces": "TraceAnalyzer",
            "logs": "LogAnalyzer"
        }

        for analysis_type, request in analysis_requests.items():
            agent_name = agent_name_mapping[analysis_type]
            task = asyncio.create_task(
                self._send_message_with_retry(
                    agent_name,
                    request,
                    max_retries=self.config["max_retry_attempts"],
                    timeout=self.config["individual_timeout"]
                )
            )
            tasks.append((analysis_type, task))

        # 等待所有Agent完成（带超时）
        logger.info("⏳ 等待所有专业Agent并行完成LLM预处理...")
        start_time_processing = time.time()

        try:
            results = await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=self.config["parallel_timeout"]
            )
        except asyncio.TimeoutError:
            logger.warning(f"⚠️ 并行LLM预处理超时({self.config['parallel_timeout']}s)，使用部分结果")
            # 取消未完成的任务
            for _, task in tasks:
                if not task.done():
                    task.cancel()
            results = [None] * len(tasks)

        end_time_processing = time.time()
        processing_duration = end_time_processing - start_time_processing

        # 处理结果
        anomaly_summaries = {}
        success_count = 0

        for i, (analysis_type, _) in enumerate(tasks):
            result = results[i] if i < len(results) else None

            if isinstance(result, Exception):
                logger.warning(f"⚠️ {analysis_type}Agent 异常: {result}")
                anomaly_summaries[analysis_type] = {"error": str(result)}
                continue

            if result is None:
                logger.warning(f"⚠️ {analysis_type}Agent 返回空结果")
                anomaly_summaries[analysis_type] = {"error": "No result returned"}
                continue

            if not hasattr(result, 'success') or not result.success:
                error_msg = getattr(result, 'error', 'Unknown error')
                logger.warning(f"⚠️ {analysis_type}Agent 失败: {error_msg}")
                anomaly_summaries[analysis_type] = {"error": error_msg}
                continue

            if not hasattr(result, 'results') or not result.results:
                logger.warning(f"⚠️ {analysis_type}Agent 缺少results字段")
                anomaly_summaries[analysis_type] = {"error": "Missing results field"}
                continue

            # 提取标准化输出
            standardized_output = result.results.get("standardized_output", {})
            if standardized_output:
                anomaly_summaries[analysis_type] = {
                    "agent_type": standardized_output.get("agent_type"),
                    "suspicious_entities": standardized_output.get("suspicious_entities", []),
                    "raw_data_sample": standardized_output.get("raw_data_sample", []),
                    "quality_score": self._calculate_quality_score(standardized_output),
                    "processing_time": processing_duration,
                    "analysis_metadata": standardized_output.get("analysis_metadata", {})
                }
                success_count += 1
                entity_count = len(standardized_output.get("suspicious_entities", []))
                logger.info(f"✅ {analysis_type}Agent LLM预处理成功: {entity_count} 个可疑实体")
            else:
                logger.warning(f"⚠️ {analysis_type}Agent 缺少标准化输出")
                anomaly_summaries[analysis_type] = {"error": "Missing standardized output"}

        logger.info(f"📊 增强并行LLM预处理完成: {success_count}/3 个Agent成功, 耗时: {processing_duration:.2f}秒")
        return anomaly_summaries

    def _validate_complete_data_availability(self, anomaly_summaries: Dict[str, Any]) -> None:
        """验证专业Agent是否提供了完整的数据证据"""
        logger.info("🔍 验证数据完整性...")

        validation_results = {}

        for agent_type, summary in anomaly_summaries.items():
            if "error" in summary:
                validation_results[agent_type] = "error"
                continue

            suspicious_entities = summary.get("suspicious_entities", [])
            if not suspicious_entities:
                validation_results[agent_type] = "no_entities"
                continue

            # 检查每个可疑实体是否包含完整数据
            complete_entities = 0
            for entity in suspicious_entities:
                # 🔧 修复：正确访问SuspiciousEntity属性
                if hasattr(entity, 'raw_data_records') and hasattr(entity, 'anomaly_feature'):
                    # SuspiciousEntity dataclass对象
                    has_raw_data = bool(getattr(entity, 'raw_data_records', None))
                    has_anomaly_feature = bool(getattr(entity, 'anomaly_feature', None))
                    has_statistical_analysis = bool(getattr(entity, 'data_summary', None))
                else:
                    # 字典格式（向后兼容）
                    has_raw_data = bool(entity.get("raw_data_evidence")) if isinstance(entity, dict) else False
                    has_anomaly_feature = bool(entity.get("anomaly_feature")) if isinstance(entity, dict) else False
                    has_statistical_analysis = bool(entity.get("statistical_analysis")) if isinstance(entity, dict) else False

                if has_raw_data and has_anomaly_feature:
                    complete_entities += 1

            completeness_ratio = complete_entities / len(suspicious_entities) if suspicious_entities else 0
            validation_results[agent_type] = f"complete_{completeness_ratio:.1%}"

            logger.info(f"📊 {agent_type}: {len(suspicious_entities)} 个实体, "
                       f"完整度 {completeness_ratio:.1%}")

        logger.info(f"✅ 数据完整性验证完成: {validation_results}")

    async def _stage_2_detailed_data_retrieval(self, case_uuid: str, start_time: str, end_time: str,
                                             anomaly_summaries: Dict[str, Any]) -> Dict[str, Any]:
        """阶段2: 基于异常信息的详细数据获取"""
        logger.info("📊 启动详细数据获取...")

        detailed_data = {}
        data_requests = []

        # 基于LLM识别的异常构建精确数据请求
        for agent_type, summary in anomaly_summaries.items():
            if "error" in summary:
                continue

            suspicious_entities = summary.get("suspicious_entities", [])
            if not suspicious_entities:
                continue

            # 为每个可疑实体构建精确数据请求
            for entity in suspicious_entities:
                # 🔧 修复：正确访问SuspiciousEntity属性
                if hasattr(entity, 'data_location'):
                    # SuspiciousEntity dataclass对象
                    data_location = getattr(entity, 'data_location', None)
                else:
                    # 字典格式（向后兼容）
                    data_location = entity.get("data_location") if isinstance(entity, dict) else None

                if data_location:
                    # 构建精确数据加载请求
                    precise_request = PreciseDataLoadRequest(
                        case_uuid=case_uuid,
                        start_time=start_time,
                        end_time=end_time,
                        data={"agent_type": agent_type, "entity": entity},
                        source_analysis_id=f"{agent_type}_{case_uuid}",
                        data_location=data_location
                    )

                    data_requests.append((agent_type, entity, precise_request))

        # 并行发送精确数据请求
        if data_requests:
            logger.info(f"📤 发送 {len(data_requests)} 个精确数据请求...")

            tasks = []
            for agent_type, entity, request in data_requests:
                task = self.runtime.send_message(request, self.agent_proxies["data_loader"].id)
                tasks.append((agent_type, entity, task))

            # 等待所有数据加载完成
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*[task for _, _, task in tasks], return_exceptions=True),
                    timeout=self.config["parallel_timeout"]
                )

                # 处理详细数据结果
                for i, (agent_type, entity, _) in enumerate(tasks):
                    result = results[i] if i < len(results) else None

                    if (not isinstance(result, Exception) and
                        hasattr(result, 'success') and result.success):

                        if agent_type not in detailed_data:
                            detailed_data[agent_type] = []

                        detailed_data[agent_type].append({
                            "entity": entity,
                            "extracted_data": result.extracted_data,
                            "load_id": result.load_id
                        })

                logger.info(f"✅ 详细数据获取完成: {len(detailed_data)} 个Agent类型")

            except asyncio.TimeoutError:
                logger.warning("⚠️ 详细数据获取超时")

        else:
            logger.warning("⚠️ 没有找到需要详细数据的异常实体")

        return detailed_data

    async def _stage_3_comprehensive_reasoning(self, case_uuid: str, start_time: str, end_time: str,
                                             anomaly_summaries: Dict[str, Any],
                                             detailed_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """阶段3: 增强推理Agent综合分析（直接使用完整数据）"""
        logger.info("🧠 启动增强综合推理分析...")

        # 验证输入数据质量
        if not self._validate_reasoning_input_data(anomaly_summaries):
            raise Exception("输入数据质量不足，无法进行推理分析")

        # 构建证据链（基于完整的anomaly_summaries）
        evidence = self._build_evidence_chain_from_complete_data(anomaly_summaries)

        # 构建分析结果摘要（基于完整数据）
        analysis_results = self._build_analysis_results_summary_from_complete_data(anomaly_summaries)

        # 创建推理请求
        reasoning_request = ReasoningRequest(
            case_uuid=case_uuid,
            start_time=start_time,
            end_time=end_time,
            data={
                "stage": "comprehensive_reasoning",
                "anomaly_summaries": anomaly_summaries,
                "detailed_data_available": True  # 提供完整的原始数据
            },
            analysis_results=analysis_results,
            evidence=evidence
        )

        # 发送给新版推理Agent（使用重试机制）
        try:
            reasoning_result = await self._send_message_with_retry(
                "NewReasoner",
                reasoning_request,
                max_retries=self.config["max_retry_attempts"],
                timeout=self.config["individual_timeout"]
            )

            if hasattr(reasoning_result, 'success') and reasoning_result.success:
                logger.info(f"✅ 综合推理完成: {reasoning_result.component} (置信度: {reasoning_result.confidence:.2f})")
                return {
                    "reasoning_result": reasoning_result,
                    "anomaly_summaries": anomaly_summaries,
                    "detailed_data": None,  # 不再使用detailed_data
                    "evidence_count": len(evidence)
                }
            else:
                # 直接抛出错误，不使用备用机制
                error_msg = f"推理分析失败: {getattr(reasoning_result, 'error', 'Unknown error')}"
                logger.error(f"❌ {error_msg}")
                raise Exception(error_msg)

        except Exception as e:
            logger.error(f"❌ 综合推理失败: {e}")
            # 直接抛出异常，不使用备用机制
            raise e

    def _validate_reasoning_input_data(self, anomaly_summaries: Dict[str, Any]) -> bool:
        """验证推理输入数据的质量 - 紧急修复版"""
        if not anomaly_summaries:
            logger.warning("⚠️ 没有异常摘要数据，但尝试继续推理")
            return True  # 🚨 紧急修复：允许空数据继续推理

        successful_agents = 0
        total_entities = 0
        has_analysis_data = False

        for agent_type, summary in anomaly_summaries.items():
            if "error" not in summary:
                successful_agents += 1
                entities = summary.get("suspicious_entities", [])
                total_entities += len(entities)
                logger.info(f"📊 {agent_type}: {len(entities)} 个可疑实体")

                # 检查是否有任何分析数据
                if summary.get("analysis_summary") or summary.get("anomalies") or entities:
                    has_analysis_data = True
            else:
                logger.warning(f"⚠️ {agent_type}: 分析失败 - {summary.get('error', 'Unknown error')}")

        # 🚨 紧急修复：大幅放宽验证条件
        if successful_agents == 0:
            logger.warning("⚠️ 没有成功的Agent，但允许基于备用分析推理")
            # 检查是否有任何可用的分析数据
            for agent_type, summary in anomaly_summaries.items():
                if isinstance(summary, dict) and (summary.get("analysis_summary") or summary.get("anomalies")):
                    logger.info(f"📊 {agent_type} 有备用分析数据可用")
                    return True

        # 只要有任何数据就允许推理
        if successful_agents > 0 or total_entities > 0 or has_analysis_data:
            logger.info(f"✅ 推理输入验证通过: {successful_agents} 个成功Agent, {total_entities} 个可疑实体")
            return True

        logger.warning("⚠️ 数据质量不足，但仍尝试推理")
        return True  # 🚨 紧急修复：总是允许推理

    def _build_evidence_chain_from_complete_data(self, anomaly_summaries: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于完整数据构建证据链"""
        evidence = []

        for agent_type, summary in anomaly_summaries.items():
            if "error" in summary:
                continue

            suspicious_entities = summary.get("suspicious_entities", [])
            logger.info(f"🔧 处理{agent_type}的{len(suspicious_entities)}个SuspiciousEntity")

            # 🔧 调试：检查emailservice实体
            emailservice_count = 0
            for i, entity in enumerate(suspicious_entities):
                try:
                    if hasattr(entity, 'entity') and hasattr(entity.entity, 'service'):
                        if 'emailservice' in str(entity.entity.service).lower():
                            emailservice_count += 1
                            logger.info(f"🔧 发现emailservice实体{emailservice_count}: index={i}, service={entity.entity.service}, pod={getattr(entity.entity, 'pod', 'unknown')}")
                except Exception as e:
                    logger.debug(f"检查实体{i}失败: {e}")

            logger.info(f"🔧 {agent_type}中emailservice实体总数: {emailservice_count}")

            for entity in suspicious_entities:
                # 🔧 修复：正确访问SuspiciousEntity属性
                if hasattr(entity, 'entity') and hasattr(entity, 'anomaly_feature'):
                    # SuspiciousEntity dataclass对象
                    service_name = getattr(entity.entity, 'service', 'unknown')
                    pod_name = getattr(entity.entity, 'pod', 'unknown')
                    pattern = getattr(entity.anomaly_feature, 'pattern', 'unknown')

                    # 🔧 调试：记录基线异常证据构建
                    if 'emailservice' in service_name.lower():
                        logger.info(f"🔧 构建基线异常证据: service={service_name}, pod={pod_name}, pattern={pattern}")

                    evidence_item = {
                        "agent_type": agent_type,
                        "entity_info": {
                            "service": service_name,
                            "namespace": getattr(entity.entity, 'namespace', 'unknown'),
                            "pod": pod_name
                        },
                        "anomaly_feature": {
                            "pattern": pattern,
                            "confidence": getattr(entity.anomaly_feature, 'confidence', 0.5),
                            "latency": getattr(entity.anomaly_feature, 'latency', None),
                            "metric_value": getattr(entity.anomaly_feature, 'metric_value', None),
                            "threshold": getattr(entity.anomaly_feature, 'threshold', None),
                            "metric_type": getattr(entity.anomaly_feature, 'metric_type', None),
                            "severity": getattr(entity.anomaly_feature, 'severity', None),
                            # 🔧 新增：基线异常的详细信息
                            "detection_method": getattr(entity.anomaly_feature, 'detection_method', 'dynamic'),
                            "deviation_ratio": getattr(entity.anomaly_feature, 'deviation_ratio', 1.0),
                            "baseline_key": getattr(entity.anomaly_feature, 'baseline_key', ''),
                            "value_type": getattr(entity.anomaly_feature, 'value_type', '')
                        },
                        "raw_data_evidence": getattr(entity, 'raw_data_records', []) or [],
                        "statistical_analysis": getattr(entity, 'data_summary', {}) or {},
                        "data_summary": getattr(entity, 'data_summary', {}) or {},  # 🎯 新增：确保data_summary字段正确传递
                        "confidence": getattr(entity, 'confidence', 0.5),
                        "data_location": {
                            "file_type": getattr(entity.data_location, 'file_type', ''),
                            "root_dir": getattr(entity.data_location, 'root_dir', ''),
                            "file_paths": getattr(entity.data_location, 'file_paths', [])
                        },
                        "correlation_context": getattr(entity, 'correlation_context', {}) or {}
                    }
                else:
                    # 字典格式（向后兼容）
                    evidence_item = {
                        "agent_type": agent_type,
                        "entity_info": entity.get("entity", {}) if isinstance(entity, dict) else {},
                        "anomaly_feature": entity.get("anomaly_feature", {}) if isinstance(entity, dict) else {},
                        "raw_data_evidence": entity.get("raw_data_evidence", []) if isinstance(entity, dict) else [],
                        "statistical_analysis": entity.get("statistical_analysis", {}) if isinstance(entity, dict) else {},
                        "confidence": entity.get("confidence", 0.5) if isinstance(entity, dict) else 0.5,
                        "data_location": entity.get("data_location", {}) if isinstance(entity, dict) else {},
                        "correlation_context": entity.get("correlation_context", {}) if isinstance(entity, dict) else {}
                    }
                evidence.append(evidence_item)

        logger.info(f"📊 构建完整证据链: {len(evidence)} 个证据项")

        # 🔧 详细调试：打印每个证据项的详细信息
        for i, evidence_item in enumerate(evidence, 1):
            agent_type = evidence_item.get("agent_type", "unknown")
            entity_info = evidence_item.get("entity_info", {})
            service = entity_info.get("service", "unknown")
            anomaly_feature = evidence_item.get("anomaly_feature", {})
            latency = anomaly_feature.get("latency", 0)
            confidence = evidence_item.get("confidence", 0)

            logger.info(f"   证据{i}: agent_type={agent_type}, service={service}, latency={latency}ms, confidence={confidence}")

            # 打印更多详细信息
            if anomaly_feature:
                logger.info(f"      anomaly_feature keys: {list(anomaly_feature.keys())}")
            if entity_info:
                logger.info(f"      entity_info keys: {list(entity_info.keys())}")

        return evidence

    def _build_analysis_results_summary_from_complete_data(self, anomaly_summaries: Dict[str, Any]) -> Dict[str, Any]:
        """基于完整数据构建分析结果摘要"""
        summary = {
            "total_agents": len(anomaly_summaries),
            "successful_agents": 0,
            "total_suspicious_entities": 0,
            "agent_details": {}
        }

        for agent_type, agent_summary in anomaly_summaries.items():
            if "error" not in agent_summary:
                summary["successful_agents"] += 1
                suspicious_entities = agent_summary.get("suspicious_entities", [])
                summary["total_suspicious_entities"] += len(suspicious_entities)

                # 🔧 修复：正确检查raw_data
                has_raw_data = False
                for entity in suspicious_entities:
                    if hasattr(entity, 'raw_data_records'):
                        # SuspiciousEntity dataclass对象
                        if getattr(entity, 'raw_data_records', None):
                            has_raw_data = True
                            break
                    elif isinstance(entity, dict):
                        # 字典格式（向后兼容）
                        if entity.get("raw_data_evidence"):
                            has_raw_data = True
                            break

                summary["agent_details"][agent_type] = {
                    "entity_count": len(suspicious_entities),
                    "quality_score": agent_summary.get("analysis_metadata", {}).get("data_quality_score", 0.5),
                    "has_raw_data": has_raw_data
                }

        return summary

    def _build_evidence_chain(self, anomaly_summaries: Dict[str, Any],
                            detailed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建证据链"""
        evidence = []

        for agent_type, summary in anomaly_summaries.items():
            if "error" in summary:
                continue

            suspicious_entities = summary.get("suspicious_entities", [])
            logger.info(f"🔧 处理{agent_type}的{len(suspicious_entities)}个SuspiciousEntity")

            for entity in suspicious_entities:
                # 🔧 修复：正确访问SuspiciousEntity dataclass属性
                try:
                    # 检查entity是否为SuspiciousEntity对象
                    if hasattr(entity, 'entity') and hasattr(entity, 'anomaly_feature'):
                        # SuspiciousEntity dataclass对象
                        evidence_entry = {
                            "type": f"{agent_type}_anomaly",
                            "agent_source": agent_type,
                            "entity": {
                                "service": getattr(entity.entity, 'service', 'unknown'),
                                "namespace": getattr(entity.entity, 'namespace', 'unknown'),
                                "pod": getattr(entity.entity, 'pod', 'unknown')
                            },
                            "anomaly_feature": {
                                "pattern": getattr(entity.anomaly_feature, 'pattern', 'unknown'),
                                "confidence": getattr(entity.anomaly_feature, 'confidence', 0.5),
                                "metric_value": getattr(entity.anomaly_feature, 'metric_value', None),
                                "threshold": getattr(entity.anomaly_feature, 'threshold', None)
                            },
                            "confidence": getattr(entity, 'confidence', 0.5),
                            "time_range": {
                                "start": getattr(entity.time_range, 'start', ''),
                                "end": getattr(entity.time_range, 'end', '')
                            },
                            "data_location": {
                                "file_type": getattr(entity.data_location, 'file_type', ''),
                                "root_dir": getattr(entity.data_location, 'root_dir', ''),
                                "file_paths": getattr(entity.data_location, 'file_paths', [])
                            },
                            "has_detailed_data": agent_type in detailed_data
                        }
                    else:
                        # 字典格式（向后兼容）
                        evidence_entry = {
                            "type": f"{agent_type}_anomaly",
                            "agent_source": agent_type,
                            "entity": entity.get("entity", {}) if isinstance(entity, dict) else {},
                            "anomaly_feature": entity.get("anomaly_feature", {}) if isinstance(entity, dict) else {},
                            "confidence": entity.get("confidence", 0.5) if isinstance(entity, dict) else 0.5,
                            "time_range": entity.get("time_range", {}) if isinstance(entity, dict) else {},
                            "data_location": entity.get("data_location", {}) if isinstance(entity, dict) else {},
                            "has_detailed_data": agent_type in detailed_data
                        }
                except Exception as e:
                    self.logger.error(f"❌ 处理可疑实体失败: {e}")
                    continue

                # 添加详细数据引用
                if agent_type in detailed_data:
                    for detail in detailed_data[agent_type]:
                        if detail["entity"] == entity:
                            evidence_entry["detailed_data_ref"] = detail["load_id"]
                            evidence_entry["extracted_records"] = detail["extracted_data"].total_records
                            break

                evidence.append(evidence_entry)

        return evidence

    def _build_analysis_results_summary(self, anomaly_summaries: Dict[str, Any],
                                       detailed_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建分析结果摘要"""
        analysis_results = {}

        for agent_type, summary in anomaly_summaries.items():
            if "error" in summary:
                analysis_results[f"{agent_type}_analysis"] = {"status": "failed", "error": summary["error"]}
            else:
                analysis_results[f"{agent_type}_analysis"] = {
                    "status": "success",
                    "anomaly_count": len(summary.get("suspicious_entities", [])),
                    "quality_score": summary.get("quality_score", 0.5),
                    "has_detailed_data": agent_type in detailed_data,
                    "agent_type": summary.get("agent_type")
                }

        return analysis_results

    def _calculate_quality_score(self, standardized_output: Dict[str, Any]) -> float:
        """计算LLM输出质量分数"""
        score = 0.0

        # 检查必需字段
        if standardized_output.get("agent_type"):
            score += 0.2
        if standardized_output.get("suspicious_entities"):
            score += 0.3
        if standardized_output.get("raw_data_sample"):
            score += 0.2

        # 检查可疑实体的完整性
        suspicious_entities = standardized_output.get("suspicious_entities", [])
        if suspicious_entities:
            complete_entities = 0
            for entity in suspicious_entities:
                # 🔧 修复：正确访问SuspiciousEntity属性
                if hasattr(entity, 'entity') and hasattr(entity, 'anomaly_feature'):
                    # SuspiciousEntity dataclass对象
                    has_entity = bool(getattr(entity, 'entity', None))
                    has_anomaly_feature = bool(getattr(entity, 'anomaly_feature', None))
                    has_data_location = bool(getattr(entity, 'data_location', None))
                    has_confidence = getattr(entity, 'confidence', 0) > 0
                else:
                    # 字典格式（向后兼容）
                    has_entity = bool(entity.get("entity")) if isinstance(entity, dict) else False
                    has_anomaly_feature = bool(entity.get("anomaly_feature")) if isinstance(entity, dict) else False
                    has_data_location = bool(entity.get("data_location")) if isinstance(entity, dict) else False
                    has_confidence = entity.get("confidence", 0) > 0 if isinstance(entity, dict) else False

                if has_entity and has_anomaly_feature and has_data_location and has_confidence:
                    complete_entities += 1

            completeness_ratio = complete_entities / len(suspicious_entities)
            score += 0.3 * completeness_ratio

        return min(1.0, score)

    def _format_diagnosis_result(self, final_result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化诊断结果为标准输出格式"""
        # 🔧 调试：检查final_result的结构
        logger.info(f"🔍 final_result类型: {type(final_result)}")
        logger.info(f"🔍 final_result键: {list(final_result.keys()) if isinstance(final_result, dict) else 'NOT_DICT'}")

        # 安全地获取reasoning_result
        reasoning_result = final_result.get("reasoning_result") if isinstance(final_result, dict) else None
        logger.info(f"🔍 reasoning_result: {reasoning_result}")

        if reasoning_result and hasattr(reasoning_result, 'success') and reasoning_result.success:
            # 🔧 调试：检查reasoning_result的属性
            logger.info(f"🔍 reasoning_result类型: {type(reasoning_result)}")
            logger.info(f"🔍 reasoning_result属性: {dir(reasoning_result)}")
            logger.info(f"🔍 reasoning_result.data存在: {hasattr(reasoning_result, 'data')}")

            # 优先使用data字段中的LLM原始输出，如果没有则使用顶层字段
            llm_data = getattr(reasoning_result, 'data', None)
            logger.info(f"🔍 llm_data获取结果: {llm_data}")

            if llm_data and isinstance(llm_data, dict):
                # 使用LLM的原始输出（保持reason和time的正确格式）
                logger.info("✅ 使用data字段中的LLM原始输出")
                logger.info(f"   LLM reason: {llm_data.get('reason')}")
                logger.info(f"   LLM time: {llm_data.get('time')}")
                return {
                    "uuid": llm_data.get("uuid", reasoning_result.case_uuid),
                    "component": llm_data.get("component", reasoning_result.component),
                    "reason": llm_data.get("reason", reasoning_result.failure_type),
                    "time": llm_data.get("time", getattr(reasoning_result, 'time', self._get_current_time())),
                    "reasoning_trace": llm_data.get("reasoning_trace", reasoning_result.reasoning_trace),
                    "confidence": llm_data.get("confidence", reasoning_result.confidence),
                    "diagnosis_method": "optimized_autogen_parallel_system",
                    "anomaly_summary": self._build_anomaly_summary(final_result),
                    "data_traceability": self._build_data_traceability(final_result)
                }
            else:
                # 🚨 LLM分析失败 - 直接报错，不提供误导性结果
                error_msg = f"❌ LLM根因分析失败: data字段无效 (存在={llm_data is not None}, 类型={type(llm_data)}, 内容={llm_data})"
                logger.error(error_msg)
                raise Exception(error_msg)
        else:
            # 直接抛出错误，不使用备用机制
            error_msg = "推理结果无效或失败"
            logger.error(f"❌ {error_msg}")
            raise Exception(error_msg)

    def _build_anomaly_summary(self, final_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建异常摘要"""
        anomaly_summaries = final_result.get("anomaly_summaries", {})

        summary = {
            "total_agents": len(anomaly_summaries),
            "successful_agents": len([s for s in anomaly_summaries.values() if "error" not in s]),
            "total_anomalies": 0,
            "by_agent": {}
        }

        for agent_type, agent_summary in anomaly_summaries.items():
            if "error" not in agent_summary:
                anomaly_count = len(agent_summary.get("suspicious_entities", []))
                summary["total_anomalies"] += anomaly_count
                summary["by_agent"][agent_type] = {
                    "anomaly_count": anomaly_count,
                    "quality_score": agent_summary.get("quality_score", 0.0)
                }
            else:
                summary["by_agent"][agent_type] = {"error": agent_summary["error"]}

        return summary

    def _build_data_traceability(self, final_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建数据追溯性信息"""
        detailed_data = final_result.get("detailed_data", {})
        evidence_count = final_result.get("evidence_count", 0)

        traceability = {
            "evidence_chain_length": evidence_count,
            "detailed_data_sources": len(detailed_data) if detailed_data else 0,
            "file_references": [],
            "data_completeness": "complete" if detailed_data else "summary_only"
        }

        # 收集文件引用（如果有详细数据）
        if detailed_data:
            for agent_type, data_list in detailed_data.items():
                for data_item in data_list:
                    extracted_data = data_item.get("extracted_data")
                    if extracted_data and hasattr(extracted_data, 'file_paths'):
                        traceability["file_references"].extend(extracted_data.file_paths)

        return traceability

    # 所有备用机制已移除 - 系统将直接报告错误

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _print_agent_execution_times(self):
        """打印各个Agent的执行时间统计"""
        execution_times = self.config.get("agent_execution_times", {})

        if not execution_times:
            logger.info("📊 没有Agent执行时间记录")
            return

        logger.info("📊 Agent执行时间统计:")
        logger.info("=" * 60)

        total_time = 0
        successful_agents = []
        failed_agents = []

        for agent_name, exec_time in execution_times.items():
            if agent_name.endswith("_FAILED"):
                real_name = agent_name.replace("_FAILED", "")
                failed_agents.append((real_name, exec_time))
                logger.info(f"❌ {real_name:<15}: {exec_time:>8.2f}秒 (失败)")
            else:
                successful_agents.append((agent_name, exec_time))
                logger.info(f"✅ {agent_name:<15}: {exec_time:>8.2f}秒 (成功)")
                total_time += exec_time

        logger.info("=" * 60)
        logger.info(f"📈 成功Agent数量: {len(successful_agents)}")
        logger.info(f"📉 失败Agent数量: {len(failed_agents)}")
        logger.info(f"⏱️  成功Agent总耗时: {total_time:.2f}秒")

        if successful_agents:
            avg_time = total_time / len(successful_agents)
            logger.info(f"📊 平均执行时间: {avg_time:.2f}秒")

            # 找出最慢和最快的Agent
            slowest = max(successful_agents, key=lambda x: x[1])
            fastest = min(successful_agents, key=lambda x: x[1])
            logger.info(f"🐌 最慢Agent: {slowest[0]} ({slowest[1]:.2f}秒)")
            logger.info(f"🚀 最快Agent: {fastest[0]} ({fastest[1]:.2f}秒)")

        logger.info("=" * 60)

    # 备用置信度计算方法已移除

    async def cleanup(self):
        """清理资源"""
        # 🚨 输出各个Agent的执行时间统计
        self._print_agent_execution_times()

        if self.runtime:
            try:
                await self.runtime.stop()
                logger.info("✅ 优化系统运行时已停止")
            except Exception as e:
                logger.warning(f"⚠️ 运行时停止时出现警告: {e}")
