# AIOps系统统一并发配置管理

## 概述

本配置系统提供了统一的并发控制和性能调优功能，支持：
- 全局并发控制
- Agent级别配置
- 段大小设置
- 自适应调优
- 环境变量覆盖

## 使用方式

### 1. 性能模式配置

```bash
# 快速模式 - 高并发，适合高性能服务器
python a2a_batch_diagnosis.py -i input.json -o output.json --performance-mode fast

# 平衡模式 - 默认配置，适合大多数环境
python a2a_batch_diagnosis.py -i input.json -o output.json --performance-mode balanced

# 保守模式 - 低并发，适合资源受限环境
python a2a_batch_diagnosis.py -i input.json -o output.json --performance-mode conservative

# 内存优化模式 - 针对内存受限环境
python a2a_batch_diagnosis.py -i input.json -o output.json --performance-mode memory_optimized
```

### 2. 自定义配置文件

```bash
# 使用自定义配置文件
python a2a_batch_diagnosis.py -i input.json -o output.json --config-file my_config.json
```

### 3. 环境变量配置

```bash
# 并发配置
export AIOPS_GLOBAL_CONCURRENT=60
export AIOPS_METRIC_CONCURRENT=40
export AIOPS_TRACE_CONCURRENT=25
export AIOPS_LOG_CONCURRENT=30
export AIOPS_LLM_CONCURRENT=50

# 段大小配置
export AIOPS_METRIC_BATCH_SIZE=30
export AIOPS_TRACE_SEGMENT_SIZE=1500
export AIOPS_LOG_SEGMENT_SIZE=800
export AIOPS_TRACE_MAX_SEGMENTS=40
export AIOPS_LOG_MAX_SEGMENTS=80

# 优化选项
export AIOPS_ENABLE_ADAPTIVE_SEGMENTATION=true
export AIOPS_ENABLE_CROSS_AGENT_PARALLEL=true

python a2a_batch_diagnosis.py -i input.json -o output.json
```

## 配置参数说明

### 全局配置
- `global_max_concurrent`: 全局最大并发数
- `enable_cross_agent_parallel`: 是否启用跨Agent并行执行

### Agent配置
- `metric_agent_concurrent`: MetricAgent并发数
- `metric_agent_batch_size`: MetricAgent批处理大小
- `trace_agent_concurrent`: TraceAgent并发数
- `trace_agent_segment_size`: TraceAgent段大小
- `log_agent_concurrent`: LogAgent并发数
- `log_agent_segment_size`: LogAgent段大小

### LLM配置
- `llm_max_concurrent`: LLM调用最大并发数
- `llm_timeout_seconds`: LLM调用超时时间
- `llm_retry_attempts`: LLM调用重试次数

### 优化选项
- `enable_adaptive_segmentation`: 自适应分段
- `enable_adaptive_concurrency`: 自适应并发调整
- `enable_intelligent_sampling`: 智能采样（谨慎使用）

## 性能模式对比

| 配置项 | 保守模式 | 平衡模式 | 快速模式 | 内存优化 |
|--------|----------|----------|----------|----------|
| 全局并发 | 20 | 50 | 80 | 40 |
| MetricAgent | 10并发/80批 | 30并发/50批 | 50并发/30批 | 25并发/20批 |
| TraceAgent | 8并发/3000段 | 20并发/2000段 | 30并发/1500段 | 15并发/1000段 |
| LogAgent | 10并发/1500段 | 25并发/1000段 | 35并发/800段 | 20并发/500段 |
| LLM并发 | 15 | 40 | 60 | 30 |
| 预估耗时 | 400-500秒 | 200-300秒 | 120-180秒 | 200-280秒 |
| 适用场景 | 资源受限 | 通用环境 | 高性能服务器 | 内存受限 |

## 自动调优

系统会根据硬件配置自动调优：
- **CPU核心数**: 影响并发度设置
- **内存大小**: 影响段大小设置
- **系统负载**: 动态调整并发度（如果启用）

## 监控指标

系统会监控以下指标：
- CPU使用率
- 内存使用率
- 并发使用情况
- LLM调用成功率

## 故障排除

### 内存不足
- 使用 `memory_optimized` 模式
- 减小段大小参数
- 降低并发数

### CPU过载
- 使用 `conservative` 模式
- 设置 `AIOPS_ENABLE_ADAPTIVE_CONCURRENCY=true`

### LLM调用失败
- 增加 `llm_timeout_seconds`
- 增加 `llm_retry_attempts`
- 降低 `llm_max_concurrent`
