{"fault_thresholds": {"apm_metrics": {"error_ratio": {"critical": 0.15, "high": 0.05, "medium": 0.01, "description": "服务错误率阈值"}, "server_error_ratio": {"critical": 0.1, "high": 0.03, "medium": 0.005, "description": "服务端错误率阈值"}, "client_error_ratio": {"critical": 0.08, "high": 0.02, "medium": 0.005, "description": "客户端错误率阈值"}, "rrt": {"critical": 5000000, "high": 2000000, "medium": 500000, "unit": "μs", "description": "平均响应时间阈值（微秒）"}, "rrt_max": {"critical": 20000000, "high": 10000000, "medium": 5000000, "unit": "μs", "description": "最大响应时间阈值（微秒）"}}, "infrastructure_metrics": {"pod_cpu_usage": {"critical": 95.0, "high": 85.0, "medium": 70.0, "unit": "%", "description": "Pod CPU使用率阈值"}, "pod_memory_working_set_bytes": {"critical": 1500000000, "high": 1200000000, "medium": 1000000000, "unit": "bytes", "description": "Pod内存使用量阈值"}, "node_cpu_usage_rate": {"critical": 90.0, "high": 80.0, "medium": 70.0, "unit": "%", "description": "节点CPU使用率阈值"}, "node_memory_usage_rate": {"critical": 90.0, "high": 80.0, "medium": 70.0, "unit": "%", "description": "节点内存使用率阈值"}}, "database_metrics": {"tidb_cpu_usage": {"critical": 90.0, "high": 80.0, "medium": 70.0, "unit": "%", "description": "TiDB CPU使用率阈值"}, "tidb_memory_usage": {"critical": 90.0, "high": 80.0, "medium": 70.0, "unit": "%", "description": "TiDB内存使用率阈值"}, "tikv_io_util": {"critical": 1000.0, "high": 500.0, "medium": 300.0, "unit": "MB/s", "description": "TiKV IO使用率阈值"}}}, "critical_components": ["frontend", "cartservice", "checkoutservice", "paymentservice", "tidb", "tikv"], "component_weights": {"frontend": 1.5, "cartservice": 1.3, "checkoutservice": 1.4, "paymentservice": 1.5, "tidb": 1.6, "tikv": 1.6, "default": 1.0}, "detection_settings": {"max_suspicious_entities": 10, "min_confidence_threshold": 0.6, "enable_adaptive_thresholds": false, "historical_baseline_days": 7}}