"""
AIOps系统配置管理器
支持自动调优、环境变量覆盖、配置文件加载等功能
"""

import os
import json
import psutil
from typing import Optional, Dict, Any
from .concurrency_config import ConcurrencyConfig

class ConfigManager:
    """增强的统一配置管理器"""
    
    _instance: Optional['ConfigManager'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not getattr(self, '_initialized', False):
            self._config: Optional[ConcurrencyConfig] = None
            self._system_info = self._get_system_info()
            self._initialized = True
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息用于自适应配置"""
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "memory_gb": psutil.virtual_memory().total // (1024**3),
                "cpu_freq": psutil.cpu_freq().max if psutil.cpu_freq() else 0
            }
        except Exception:
            # 如果psutil不可用，使用默认值
            return {
                "cpu_count": 8,
                "memory_gb": 16,
                "cpu_freq": 2400
            }
    
    def load_config(self, config_path: Optional[str] = None, 
                   performance_mode: str = "balanced",
                   auto_tune: bool = True) -> ConcurrencyConfig:
        """加载配置，支持自动调优"""
        
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            self._config = ConcurrencyConfig(**config_dict)
        else:
            self._config = ConcurrencyConfig.get_optimized_config(performance_mode)
        
        # 自动调优
        if auto_tune:
            self._auto_tune_config()
        
        # 环境变量覆盖
        self._apply_env_overrides()
        
        return self._config
    
    def _auto_tune_config(self):
        """基于系统资源自动调优配置"""
        cpu_count = self._system_info["cpu_count"]
        memory_gb = self._system_info["memory_gb"]
        
        # 基于CPU核心数调整并发度
        if cpu_count >= 16:  # 高性能服务器
            self._config.global_max_concurrent = min(80, cpu_count * 4)
            self._config.llm_max_concurrent = min(60, cpu_count * 3)
        elif cpu_count >= 8:  # 中等性能
            self._config.global_max_concurrent = min(50, cpu_count * 3)
            self._config.llm_max_concurrent = min(40, cpu_count * 2)
        else:  # 低性能
            self._config.global_max_concurrent = min(20, cpu_count * 2)
            self._config.llm_max_concurrent = min(15, int(cpu_count * 1.5))
        
        # 基于内存调整段大小
        if memory_gb >= 32:  # 大内存
            self._config.trace_agent_segment_size = 3000
            self._config.log_agent_segment_size = 1500
        elif memory_gb >= 16:  # 中等内存
            self._config.trace_agent_segment_size = 2000
            self._config.log_agent_segment_size = 1000
        else:  # 小内存
            self._config.trace_agent_segment_size = 1000
            self._config.log_agent_segment_size = 500
            self._config.memory_threshold_mb = memory_gb * 1024 // 2
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        env_mappings = {
            # 并发配置
            'AIOPS_GLOBAL_CONCURRENT': 'global_max_concurrent',
            'AIOPS_METRIC_CONCURRENT': 'metric_agent_concurrent',
            'AIOPS_TRACE_CONCURRENT': 'trace_agent_concurrent',
            'AIOPS_LOG_CONCURRENT': 'log_agent_concurrent',
            'AIOPS_LLM_CONCURRENT': 'llm_max_concurrent',
            
            # 段大小配置
            'AIOPS_METRIC_BATCH_SIZE': 'metric_agent_batch_size',
            'AIOPS_TRACE_SEGMENT_SIZE': 'trace_agent_segment_size',
            'AIOPS_LOG_SEGMENT_SIZE': 'log_agent_segment_size',
            
            # 限制配置
            'AIOPS_TRACE_MAX_SEGMENTS': 'trace_agent_max_segments',
            'AIOPS_LOG_MAX_SEGMENTS': 'log_agent_max_segments',
        }
        
        for env_key, config_key in env_mappings.items():
            if env_key in os.environ:
                try:
                    setattr(self._config, config_key, int(os.environ[env_key]))
                except ValueError:
                    print(f"警告: 环境变量 {env_key} 的值无效，使用默认值")
        
        # 布尔值配置
        bool_mappings = {
            'AIOPS_ENABLE_ADAPTIVE_SEGMENTATION': 'enable_adaptive_segmentation',
            'AIOPS_ENABLE_CROSS_AGENT_PARALLEL': 'enable_cross_agent_parallel',
            'AIOPS_ENABLE_INTELLIGENT_SAMPLING': 'enable_intelligent_sampling',
        }
        
        for env_key, config_key in bool_mappings.items():
            if env_key in os.environ:
                setattr(self._config, config_key, os.environ[env_key].lower() == 'true')
    
    @property
    def config(self) -> ConcurrencyConfig:
        """获取当前配置"""
        if self._config is None:
            self.load_config()
        return self._config
    
    def save_config(self, config_path: str):
        """保存当前配置到文件"""
        if self._config is None:
            raise ValueError("没有可保存的配置")
        
        # 将dataclass转换为字典
        config_dict = {
            field.name: getattr(self._config, field.name)
            for field in self._config.__dataclass_fields__.values()
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

    def print_config_summary(self):
        """打印配置摘要"""
        if self._config is None:
            print("❌ 配置未加载")
            return

        summary = self._config.get_performance_summary()

        print("🚀 AIOps系统并发配置摘要")
        print("=" * 60)
        print(f"🖥️  系统信息: {self._system_info['cpu_count']}核CPU, {self._system_info['memory_gb']}GB内存")
        print()

        profile = summary["performance_profile"]
        print(f"⚡ 全局配置:")
        print(f"   全局最大并发: {profile['global_concurrent']}")
        print(f"   LLM最大并发: {profile['total_llm_concurrent']}")
        print(f"   跨Agent并行: {'✅' if profile['cross_agent_parallel'] else '❌'}")
        print()

        agents = summary["agent_configs"]
        print(f"🤖 Agent配置:")
        print(f"   MetricAgent: {agents['metric']['concurrent']}并发, 批大小{agents['metric']['batch_size']}")
        print(f"   TraceAgent:  {agents['trace']['concurrent']}并发, 段大小{agents['trace']['segment_size']}, 最大{agents['trace']['max_segments']}段")
        print(f"   LogAgent:    {agents['log']['concurrent']}并发, 段大小{agents['log']['segment_size']}, 最大{agents['log']['max_segments']}段")
        print()

        opts = summary["optimizations"]
        print(f"🔧 优化选项:")
        print(f"   自适应分段: {'✅' if opts['adaptive_segmentation'] else '❌'}")
        print(f"   自适应并发: {'✅' if opts['adaptive_concurrency'] else '❌'}")
        print(f"   智能采样: {'✅' if opts['intelligent_sampling'] else '❌'}")
        print("=" * 60)


# 全局配置实例
config_manager = ConfigManager()
