#!/usr/bin/env python3
"""
基于AutoGen的AIOps智能体基类
"""

import logging
from typing import Dict, Any, List, Optional
from autogen_core import BaseAgent, MessageContext, default_subscription
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class AIOpsMessage:
    """AIOps系统消息基类"""
    case_uuid: str
    start_time: str
    end_time: str
    data: Dict[str, Any]
    
    
@dataclass
class DataLoadRequest(AIOpsMessage):
    """数据加载请求"""
    pass


@dataclass
class DataLoadResponse(AIOpsMessage):
    """数据加载响应"""
    success: bool
    error: Optional[str] = None


@dataclass
class AnalysisRequest(AIOpsMessage):
    """分析请求"""
    analysis_type: str  # "metrics", "traces", "logs"


@dataclass
class AnalysisResponse(AIOpsMessage):
    """分析响应"""
    analysis_type: str
    results: Dict[str, Any]
    success: bool
    error: Optional[str] = None


@dataclass
class ReasoningRequest(AIOpsMessage):
    """推理请求"""
    analysis_results: Dict[str, Any]
    evidence: List[Dict[str, Any]]


@dataclass
class ReasoningResponse(AIOpsMessage):
    """推理响应"""
    component: str
    failure_type: str
    reasoning_trace: List[Dict[str, Any]]
    confidence: float
    success: bool
    error: Optional[str] = None


@dataclass
class DiagnosisRequest:
    """诊断请求"""
    case_uuid: str
    start_time: str
    end_time: str


@dataclass
class DiagnosisResponse:
    """诊断响应"""
    case_uuid: str
    component: str
    reason: str
    time: str
    reasoning_trace: List[Dict[str, Any]]  # 统一使用reasoning_trace
    success: bool
    error: Optional[str] = None


# ============================================================================
# 文件路径定位和数据交互格式优化
# ============================================================================

@dataclass
class DataLocation:
    """数据位置信息"""
    file_type: str  # "log", "metric", "trace"
    root_dir: str  # 根目录，如 "2025-06-06/"
    file_paths: List[str]  # 具体文件路径列表
    file_matching_rule: str  # 匹配逻辑说明


@dataclass
class EntityInfo:
    """实体信息"""
    service: str
    pod: Optional[str] = None
    namespace: Optional[str] = None
    metric_type: Optional[str] = None  # 用于metric类型


@dataclass
class TimeRange:
    """时间范围"""
    start: str  # ISO格式时间
    end: str    # ISO格式时间


@dataclass
class AnomalyFeature:
    """异常特征"""
    # 通用字段
    pattern: str
    confidence: float

    # Log专用字段
    log_level: Optional[str] = None
    key_log_fragment: Optional[str] = None

    # Metric专用字段
    metric_value: Optional[float] = None
    threshold: Optional[float] = None
    metric_type: Optional[str] = None  # 🔧 新增：指标类型（如error_ratio, rrt, cpu_usage等）
    severity: Optional[str] = None     # 🔧 新增：严重程度（critical, high, medium, low）

    # 🔧 新增：基线异常专用字段
    detection_method: Optional[str] = None    # 检测方法（baseline, baseline_v2, dynamic）
    deviation_ratio: Optional[float] = None   # 偏差倍数
    baseline_key: Optional[str] = None        # 基线键值
    value_type: Optional[str] = None          # 值类型（mean, max, p95等）

    # Trace专用字段
    latency: Optional[float] = None
    normal_latency: Optional[float] = None

    # 新增：原始数据上下文
    raw_data_context: Optional[Dict[str, Any]] = None
    precise_timestamp: Optional[str] = None
    source_record_id: Optional[str] = None
    source_service: Optional[str] = None
    target_service: Optional[str] = None
    span_id: Optional[str] = None


@dataclass
class SuspiciousEntity:
    """可疑实体（增强版：包含完整原始数据）"""
    entity: EntityInfo
    time_range: TimeRange
    anomaly_feature: AnomalyFeature
    data_location: DataLocation
    confidence: float

    # 新增：原始数据完整性字段
    raw_data_records: List[Dict[str, Any]] = None  # 完整的原始数据记录
    data_summary: Dict[str, Any] = None  # 数据摘要统计
    correlation_context: Dict[str, Any] = None  # 关联上下文信息


@dataclass
class ExtractedData:
    """提取的数据"""
    entity: str
    data_type: str
    file_paths: List[str]
    total_records: int
    data: List[Dict[str, Any]]
    metadata: Dict[str, Any]


@dataclass
class EnhancedAnalysisResponse(AIOpsMessage):
    """增强的分析响应 - 包含文件路径和可追溯性"""
    agent_type: str  # "log_agent", "metric_agent", "trace_agent"
    analysis_id: str
    suspicious_entities: List[SuspiciousEntity]
    raw_data_sample: List[str]  # 原始数据样本
    analysis_type: str
    results: Dict[str, Any]
    success: bool
    error: Optional[str] = None


@dataclass
class PreciseDataLoadRequest(AIOpsMessage):
    """精确数据加载请求"""
    source_analysis_id: str  # 关联的专业Agent分析ID
    data_location: DataLocation


@dataclass
class PreciseDataLoadResponse(AIOpsMessage):
    """精确数据加载响应"""
    load_id: str
    source_analysis_id: str
    extracted_data: ExtractedData
    success: bool
    error: Optional[str] = None


@dataclass
class LLMGuidedDataLoadRequest(AIOpsMessage):
    """基于LLM分析结果的精确数据加载请求"""
    agent_type: str  # "metric", "trace", "log"
    llm_analysis_result: Dict[str, Any]  # LLM分析出的可疑组件和时间范围
    target_components: List[str]  # 目标组件列表
    target_time_ranges: List[Dict[str, str]]  # 精确时间范围列表
    data_requirements: Dict[str, Any]  # 具体数据需求


@dataclass
class LLMGuidedDataLoadResponse(AIOpsMessage):
    """基于LLM分析结果的精确数据加载响应"""
    agent_type: str
    targeted_data: Dict[str, Any]  # 按组件和时间范围组织的精确数据
    data_quality_metrics: Dict[str, float]  # 数据质量指标
    success: bool
    error: Optional[str] = None


@dataclass
class DependencyContext:
    """服务依赖上下文信息"""
    service_dependencies: Dict[str, List[str]]  # 服务依赖关系
    critical_paths: List[Dict[str, Any]]  # 关键调用路径
    service_layers: Dict[str, List[str]]  # 服务层次
    dependency_graph: Dict[str, Any]  # 完整依赖图
    impact_analysis: Dict[str, Dict[str, Any]]  # 影响分析


@dataclass
class CrossAgentMessage(AIOpsMessage):
    """Agent间消息传递"""
    sender_agent: str
    message_type: str  # 'dependency_context', 'anomaly_correlation', 'evidence_chain'
    content: Dict[str, Any]
    priority: str = 'normal'  # 'low', 'normal', 'high', 'critical'


@dataclass
class EvidenceChainRequest(AIOpsMessage):
    """证据链构建请求"""
    primary_anomaly: SuspiciousEntity  # 主要异常
    dependency_context: Optional[DependencyContext] = None  # 依赖上下文
    correlation_hints: List[str] = None  # 关联提示


@dataclass
class EvidenceChainResponse(AIOpsMessage):
    """证据链构建响应"""
    evidence_chain: List[Dict[str, Any]]  # 完整证据链
    root_cause_hypothesis: Dict[str, Any]  # 根因假设
    confidence_score: float
    supporting_evidence: List[SuspiciousEntity]  # 支撑证据
    success: bool
    error: Optional[str] = None


@default_subscription
class BaseAIOpsAgent(BaseAgent):
    """AIOps智能体基类"""

    def __init__(self, description: str, enable_llm: bool = True):
        super().__init__(description)
        self.enable_llm = enable_llm
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
    
    async def on_message_impl(self, message: Any, ctx: MessageContext) -> Any:
        """处理消息的通用方法"""
        try:
            # 只记录关键消息类型，减少A2A消息噪音
            if isinstance(message, (DataLoadRequest, PreciseDataLoadRequest, LLMGuidedDataLoadRequest)):
                self.logger.debug(f"Received message: {type(message).__name__}")  # 降级为DEBUG
            else:
                self.logger.info(f"Received message: {type(message).__name__}")
            
            # 根据消息类型分发处理
            if isinstance(message, DataLoadRequest):
                return await self._handle_data_load_request(message, ctx)
            elif isinstance(message, PreciseDataLoadRequest):
                return await self._handle_precise_data_load_request(message, ctx)
            elif isinstance(message, LLMGuidedDataLoadRequest):
                return await self._handle_llm_guided_data_load_request(message, ctx)
            elif isinstance(message, AnalysisRequest):
                return await self._handle_analysis_request(message, ctx)
            elif isinstance(message, ReasoningRequest):
                return await self._handle_reasoning_request(message, ctx)
            elif isinstance(message, DiagnosisRequest):
                return await self._handle_diagnosis_request(message, ctx)
            else:
                self.logger.warning(f"Unknown message type: {type(message)}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
            return self._create_error_response(message, str(e))
    
    async def _handle_data_load_request(self, message: DataLoadRequest, ctx: MessageContext) -> DataLoadResponse:
        """处理数据加载请求"""
        raise NotImplementedError("Subclasses must implement _handle_data_load_request")

    async def _handle_precise_data_load_request(self, message: PreciseDataLoadRequest, ctx: MessageContext) -> PreciseDataLoadResponse:
        """处理精确数据加载请求"""
        raise NotImplementedError("Subclasses must implement _handle_precise_data_load_request")

    async def _handle_llm_guided_data_load_request(self, message: LLMGuidedDataLoadRequest, ctx: MessageContext) -> LLMGuidedDataLoadResponse:
        """处理基于LLM分析结果的精确数据加载请求"""
        raise NotImplementedError("Subclasses must implement _handle_llm_guided_data_load_request")

    async def _handle_analysis_request(self, message: AnalysisRequest, ctx: MessageContext) -> AnalysisResponse:
        """处理分析请求"""
        raise NotImplementedError("Subclasses must implement _handle_analysis_request")
    
    async def _handle_reasoning_request(self, message: ReasoningRequest, ctx: MessageContext) -> ReasoningResponse:
        """处理推理请求"""
        raise NotImplementedError("Subclasses must implement _handle_reasoning_request")
    
    async def _handle_diagnosis_request(self, message: DiagnosisRequest, ctx: MessageContext) -> DiagnosisResponse:
        """处理诊断请求"""
        raise NotImplementedError("Subclasses must implement _handle_diagnosis_request")
    
    def _create_error_response(self, request_message: Any, error: str) -> Any:
        """创建错误响应"""
        if isinstance(request_message, DataLoadRequest):
            return DataLoadResponse(
                case_uuid=request_message.case_uuid,
                start_time=request_message.start_time,
                end_time=request_message.end_time,
                data={},
                success=False,
                error=error
            )
        elif isinstance(request_message, AnalysisRequest):
            return AnalysisResponse(
                case_uuid=request_message.case_uuid,
                start_time=request_message.start_time,
                end_time=request_message.end_time,
                data={},
                analysis_type=request_message.analysis_type,
                results={},
                success=False,
                error=error
            )
        elif isinstance(request_message, ReasoningRequest):
            return ReasoningResponse(
                case_uuid=request_message.case_uuid,
                start_time=request_message.start_time,
                end_time=request_message.end_time,
                data={},
                component="unknown",
                failure_type="unknown",
                reasoning_trace=[],
                confidence=0.0,
                success=False,
                error=error
            )
        elif isinstance(request_message, DiagnosisRequest):
            return DiagnosisResponse(
                case_uuid=request_message.case_uuid,
                component="unknown",
                reason="Diagnosis failed",
                time="",
                reasoning_trace=[],  # 修复为reasoning_trace
                success=False,
                error=error
            )
        else:
            return None
