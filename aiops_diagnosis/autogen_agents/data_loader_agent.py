#!/usr/bin/env python3
"""
基于AutoGen的数据加载智能体
"""

import pandas as pd
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from autogen_core import MessageContext
from .base_aiops_agent import (
    BaseAIOpsAgent, DataLoadRequest, DataLoadResponse,
    PreciseDataLoadRequest, PreciseDataLoadResponse, ExtractedData, DataLocation,
    LLMGuidedDataLoadRequest, LLMGuidedDataLoadResponse
)
from ..utils.file_path_locator import FilePathLocator
from ..knowledge.global_component_manager import get_global_component_manager


class AutoGenDataLoaderAgent(BaseAIOpsAgent):
    """基于AutoGen的数据加载智能体"""

    def __init__(self, data_root_path: str = "/data/phaseone_data"):
        super().__init__("Data loading agent for AIOps diagnosis")
        self.data_root_path = data_root_path
        self.file_locator = FilePathLocator(data_root_path)

        # 🔧 添加全局组件管理器
        self.component_manager = get_global_component_manager(data_root_path)

        # 添加数据缓存机制
        self._data_cache = {}  # 格式: {case_uuid: {metrics: df, traces: df, logs: df, timestamp: float}}
        self._cache_ttl = 300  # 缓存5分钟

    def _get_cached_data(self, case_uuid: str) -> Optional[Dict[str, Any]]:
        """获取缓存的数据"""
        import time

        if case_uuid not in self._data_cache:
            return None

        cache_entry = self._data_cache[case_uuid]
        if time.time() - cache_entry['timestamp'] > self._cache_ttl:
            # 缓存过期，删除
            del self._data_cache[case_uuid]
            return None

        self.logger.info(f"✅ 使用缓存数据: {case_uuid}")
        return cache_entry

    def _cache_data(self, case_uuid: str, metrics_data, traces_data, logs_data):
        """缓存数据"""
        import time

        self._data_cache[case_uuid] = {
            'metrics': metrics_data,
            'traces': traces_data,
            'logs': logs_data,
            'timestamp': time.time()
        }
        self.logger.info(f"💾 数据已缓存: {case_uuid}")
    
    async def _handle_data_load_request(self, message: DataLoadRequest, ctx: MessageContext) -> DataLoadResponse:
        """处理数据加载请求"""
        try:
            self.logger.info(f"Loading data for case: {message.case_uuid}")

            # 首先检查缓存
            cached_data = self._get_cached_data(message.case_uuid)
            if cached_data:
                # 使用缓存数据
                data = {}
                if not cached_data['metrics'].empty:
                    data["metrics"] = cached_data['metrics']
                    self.logger.info(f"Loaded {len(cached_data['metrics'])} metrics records from cache")
                if not cached_data['traces'].empty:
                    data["traces"] = cached_data['traces']
                    self.logger.info(f"Loaded {len(cached_data['traces'])} trace records from cache")
                if not cached_data['logs'].empty:
                    data["logs"] = cached_data['logs']
                    self.logger.info(f"Loaded {len(cached_data['logs'])} log records from cache")
            else:
                # 加载各类数据
                data = {}

                # 加载指标数据
                metrics_data = await self._load_metrics_data(message.case_uuid, message.start_time, message.end_time)
                if not metrics_data.empty:
                    data["metrics"] = metrics_data
                    self.logger.info(f"Loaded {len(metrics_data)} metrics records")

                # 加载调用链数据
                self.logger.info("开始加载调用链数据...")
                traces_data = await self._load_traces_data(message.case_uuid, message.start_time, message.end_time)
                if not traces_data.empty:
                    data["traces"] = traces_data
                    self.logger.info(f"Loaded {len(traces_data)} trace records")
                else:
                    self.logger.info("No trace data loaded")

                # 加载日志数据
                self.logger.info("开始加载日志数据...")
                logs_data = await self._load_logs_data(message.case_uuid, message.start_time, message.end_time)
                if not logs_data.empty:
                    data["logs"] = logs_data
                    self.logger.info(f"Loaded {len(logs_data)} log records")
                else:
                    self.logger.info("No log data loaded")

                # 缓存数据
                self._cache_data(
                    message.case_uuid,
                    metrics_data if 'metrics_data' in locals() and not metrics_data.empty else pd.DataFrame(),
                    traces_data if 'traces_data' in locals() and not traces_data.empty else pd.DataFrame(),
                    logs_data if 'logs_data' in locals() and not logs_data.empty else pd.DataFrame()
                )
            
            return DataLoadResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                data=data,
                success=True
            )
            
        except Exception as e:
            self.logger.error(f"Failed to load data: {e}")
            return DataLoadResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                data={},
                success=False,
                error=str(e)
            )

    async def _handle_precise_data_load_request(self, message: PreciseDataLoadRequest, ctx: MessageContext) -> PreciseDataLoadResponse:
        """处理精确数据加载请求 - 基于文件路径直接提取数据"""
        try:
            self.logger.info(f"🎯 精确数据加载: {message.source_analysis_id}")

            # 处理data_location，可能是字典或DataLocation对象
            if isinstance(message.data_location, dict):
                # 如果是字典，转换为DataLocation对象
                from .base_aiops_agent import DataLocation
                data_location = DataLocation(
                    file_type=message.data_location.get("file_type", "unknown"),
                    root_dir=message.data_location.get("root_dir", ""),
                    file_paths=message.data_location.get("file_paths", []),
                    file_matching_rule=message.data_location.get("file_matching_rule", "")
                )
            else:
                data_location = message.data_location

            # 验证文件路径
            validation_result = self.file_locator.validate_file_paths(data_location)

            if not validation_result["valid_files"]:
                return PreciseDataLoadResponse(
                    case_uuid=message.case_uuid,
                    start_time=message.start_time,
                    end_time=message.end_time,
                    data={},
                    load_id=f"load_{message.case_uuid}_{int(pd.Timestamp.now().timestamp())}",
                    source_analysis_id=message.source_analysis_id,
                    extracted_data=ExtractedData(
                        entity="unknown",
                        data_type=data_location.file_type,
                        file_paths=[],
                        total_records=0,
                        data=[],
                        metadata={"error": "No valid files found"}
                    ),
                    success=False,
                    error="No valid files found"
                )

            # 加载数据
            extracted_data = await self._extract_data_from_files(data_location, validation_result)

            return PreciseDataLoadResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                data={},
                load_id=f"load_{message.case_uuid}_{int(pd.Timestamp.now().timestamp())}",
                source_analysis_id=message.source_analysis_id,
                extracted_data=extracted_data,
                success=True
            )

        except Exception as e:
            self.logger.error(f"❌ 精确数据加载失败: {e}")
            return PreciseDataLoadResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                data={},
                load_id=f"load_error_{int(pd.Timestamp.now().timestamp())}",
                source_analysis_id=message.source_analysis_id,
                extracted_data=ExtractedData(
                    entity="unknown",
                    data_type=data_location.file_type if 'data_location' in locals() else "unknown",
                    file_paths=[],
                    total_records=0,
                    data=[],
                    metadata={"error": str(e)}
                ),
                success=False,
                error=str(e)
            )

    async def _extract_data_from_files(self, data_location: DataLocation,
                                     validation_result: Dict[str, Any]) -> ExtractedData:
        """从文件中提取数据"""
        all_data = []
        total_records = 0
        processed_files = []

        for relative_path in validation_result["valid_files"]:
            try:
                # 构建完整文件路径
                full_path = Path(self.data_root_path) / data_location.root_dir / relative_path

                # 读取Parquet文件
                df = pd.read_parquet(full_path)

                if not df.empty:
                    # 转换为字典列表
                    records = df.to_dict('records')
                    all_data.extend(records)
                    total_records += len(records)
                    processed_files.append(str(relative_path))

                    self.logger.info(f"✅ 成功加载文件: {relative_path}, 记录数: {len(records)}")

            except Exception as e:
                self.logger.warning(f"⚠️ 文件加载失败: {relative_path}, 错误: {e}")

        # 构建元数据
        metadata = {
            "file_size_mb": validation_result["total_size_mb"],
            "parsed_success": len(processed_files) > 0,
            "processed_files": processed_files,
            "warnings": validation_result["warnings"],
            "file_matching_rule": data_location.file_matching_rule
        }

        return ExtractedData(
            entity="extracted_entity",  # 可以根据需要进一步解析
            data_type=data_location.file_type,
            file_paths=[f"{data_location.root_dir}{path}" for path in processed_files],
            total_records=total_records,
            data=all_data,
            metadata=metadata
        )

    def utc_to_beijing_time(self, utc_time_str: str) -> tuple:
        """将UTC时间转换为北京时间，用于文件名定位"""
        from datetime import timedelta
        utc_dt = pd.to_datetime(utc_time_str)
        beijing_dt = utc_dt + timedelta(hours=8)
        beijing_date = beijing_dt.strftime('%Y-%m-%d')
        beijing_hour = beijing_dt.strftime('%H')
        return beijing_date, beijing_hour

    def get_time_range_files(self, start_utc: str, end_utc: str) -> dict:
        """根据UTC时间范围精确确定需要加载的数据文件"""
        from datetime import timedelta
        from pathlib import Path

        start_dt = pd.to_datetime(start_utc)
        end_dt = pd.to_datetime(end_utc)

        files = {'metrics': [], 'traces': [], 'logs': []}

        # 转换为北京时间
        start_beijing_dt = start_dt + timedelta(hours=8)
        end_beijing_dt = end_dt + timedelta(hours=8)

        self.logger.info(f"UTC时间范围: {start_utc} 到 {end_utc}")
        self.logger.info(f"北京时间范围: {start_beijing_dt} 到 {end_beijing_dt}")

        # 确定需要的小时范围
        start_hour = start_beijing_dt.replace(minute=0, second=0, microsecond=0)
        end_hour = end_beijing_dt.replace(minute=0, second=0, microsecond=0)

        # 用于去重
        trace_files = set()
        log_files = set()

        # 遍历时间范围内的每个小时
        current_hour = start_hour
        while current_hour <= end_hour:
            beijing_date = current_hour.strftime('%Y-%m-%d')
            beijing_hour = current_hour.strftime('%H')

            date_dir = Path(self.data_root_path) / beijing_date

            # Trace文件（按小时精确加载）
            trace_filename = f"trace_jaeger-span_{beijing_date}_{beijing_hour}-00-00.parquet"
            trace_file = date_dir / "trace-parquet" / trace_filename
            if trace_file.exists() and str(trace_file) not in trace_files:
                files['traces'].append(str(trace_file))
                trace_files.add(str(trace_file))
                self.logger.debug(f"添加trace文件: {trace_filename}")

            # Log文件（按小时精确加载）
            log_filename = f"log_filebeat-server_{beijing_date}_{beijing_hour}-00-00.parquet"
            log_file = date_dir / "log-parquet" / log_filename
            if log_file.exists() and str(log_file) not in log_files:
                files['logs'].append(str(log_file))
                log_files.add(str(log_file))
                self.logger.debug(f"添加log文件: {log_filename}")

            current_hour += timedelta(hours=1)

        # Metric文件优化：只加载相关日期的文件，并进一步优化选择
        dates_needed = set()
        current_hour = start_hour
        while current_hour <= end_hour:
            dates_needed.add(current_hour.strftime('%Y-%m-%d'))
            current_hour += timedelta(hours=1)

        for beijing_date in dates_needed:
            date_dir = Path(self.data_root_path) / beijing_date
            metric_dir = date_dir / "metric-parquet"

            if metric_dir.exists():
                # 优先加载APM Pod级别数据（最重要的业务指标）
                apm_pod_dir = metric_dir / "apm" / "pod"
                if apm_pod_dir.exists():
                    for parquet_file in apm_pod_dir.glob(f"*_{beijing_date}.parquet"):
                        files['metrics'].append(str(parquet_file))
                        self.logger.debug(f"添加APM Pod文件: {parquet_file.name}")

                # 加载APM Service级别数据
                apm_service_dir = metric_dir / "apm" / "service"
                if apm_service_dir.exists():
                    for parquet_file in apm_service_dir.glob(f"*_{beijing_date}.parquet"):
                        files['metrics'].append(str(parquet_file))
                        self.logger.debug(f"添加APM Service文件: {parquet_file.name}")

                # 加载关键的Infra Pod指标（CPU、内存）
                infra_pod_dir = metric_dir / "infra" / "infra_pod"
                if infra_pod_dir.exists():
                    key_metrics = ['pod_cpu_usage', 'pod_memory_working_set_bytes']
                    for metric in key_metrics:
                        for parquet_file in infra_pod_dir.glob(f"*{metric}_{beijing_date}.parquet"):
                            files['metrics'].append(str(parquet_file))
                            self.logger.debug(f"添加Infra Pod文件: {parquet_file.name}")

                # 加载TiDB/TiKV/PD基础设施数据库组件指标
                infra_components = ['infra_tidb', 'infra_tikv', 'infra_pd']
                for component in infra_components:
                    component_dir = metric_dir / "infra" / component
                    if component_dir.exists():
                        # 加载关键指标：CPU、内存、QPS、响应时间
                        key_metrics = ['cpu_usage', 'memory_usage', 'qps', 'duration_95th', 'duration_99th', 'connection_count']
                        for metric in key_metrics:
                            for parquet_file in component_dir.glob(f"*{metric}_{beijing_date}.parquet"):
                                files['metrics'].append(str(parquet_file))
                                self.logger.debug(f"添加{component}文件: {parquet_file.name}")

                # 加载命名空间级别的APM数据
                for parquet_file in metric_dir.glob(f"*hipstershop_{beijing_date}.parquet"):
                    files['metrics'].append(str(parquet_file))
                    self.logger.debug(f"添加命名空间文件: {parquet_file.name}")

        self.logger.info(f"选择的文件数量 - Metrics: {len(files['metrics'])}, Traces: {len(files['traces'])}, Logs: {len(files['logs'])}")
        return files

    async def _load_metrics_data(self, case_uuid: str, start_time: str, end_time: str) -> pd.DataFrame:
        """加载指标数据 - 基于示例的正确实现"""
        try:
            # 获取需要加载的文件列表
            files = self.get_time_range_files(start_time, end_time)
            all_metrics = []

            self.logger.info(f"Found {len(files['metrics'])} metric files to load")

            for metric_file in files['metrics']:
                try:
                    # 优化：先检查文件的时间范围，避免加载不相关的数据
                    if self._should_skip_file_by_time_range(metric_file, start_time, end_time):
                        self.logger.debug(f"跳过时间范围外的文件: {metric_file}")
                        continue

                    df = pd.read_parquet(metric_file)
                    if not df.empty:
                        # 从文件路径提取指标类型信息
                        metric_type = self._extract_metric_type_from_path(str(metric_file))
                        if metric_type:
                            df['metric_type'] = metric_type

                        # 标准化TiDB/TiKV数据字段
                        df = self._standardize_infrastructure_data(df, metric_type)

                        all_metrics.append(df)
                        self.logger.debug(f"Loaded metric data from {metric_file}: {len(df)} records, type: {metric_type}")
                except Exception as e:
                    self.logger.warning(f"Error reading {metric_file}: {e}")

            if not all_metrics:
                self.logger.warning("No metrics data loaded")
                return pd.DataFrame()

            # 合并所有指标数据
            combined_df = pd.concat(all_metrics, ignore_index=True)

            # 🔧 使用分钟边界范围过滤metric数据（60秒粒度）
            filtered_df = self._filter_metric_data_by_minute_boundaries(combined_df, start_time, end_time, 'time')

            self.logger.info(f"Loaded {len(filtered_df)} metric records from {len(all_metrics)} files after minute boundary filtering")
            return filtered_df

        except Exception as e:
            self.logger.error(f"Error loading metrics data: {e}")
            return pd.DataFrame()

    def _standardize_infrastructure_data(self, df: pd.DataFrame, metric_type: str) -> pd.DataFrame:
        """标准化基础设施数据字段 - 统一TiDB/TiKV/Node数据格式"""
        try:
            # 创建副本避免修改原数据
            standardized_df = df.copy()

            # 标准化时间字段
            if 'time' in standardized_df.columns:
                standardized_df['timestamp'] = standardized_df['time']

            # 标准化组件字段和值字段
            if metric_type and 'tidb' in metric_type:
                # TiDB数据标准化 - 使用实例信息作为组件标识
                if 'instance' in standardized_df.columns and not standardized_df['instance'].isna().all():
                    # 使用instance字段，但简化为tidb组件类型
                    standardized_df['component'] = 'tidb'
                    # 保留原始instance信息用于详细分析
                    standardized_df['instance_detail'] = standardized_df['instance']
                else:
                    standardized_df['component'] = 'tidb'

                # 根据指标类型映射值字段
                if 'cpu_usage' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['cpu_usage']
                elif 'memory_usage' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['memory_usage']
                elif 'connection_count' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['connection_count']
                elif 'qps' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['qps']
                elif 'duration_99th' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['duration_99th']
                elif 'duration_95th' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['duration_95th']
                elif 'duration_avg' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['duration_avg']

            elif metric_type and 'tikv' in metric_type:
                # TiKV数据标准化
                if 'instance' in standardized_df.columns and not standardized_df['instance'].isna().all():
                    standardized_df['component'] = 'tikv'
                    standardized_df['instance_detail'] = standardized_df['instance']
                else:
                    standardized_df['component'] = 'tikv'

                # 根据指标类型映射值字段
                if 'cpu_usage' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['cpu_usage']
                elif 'memory_usage' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['memory_usage']
                elif 'read_mbps' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['read_mbps']
                elif 'write_mbps' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['write_mbps']

            elif metric_type and 'pd' in metric_type:
                # PD数据标准化
                if 'instance' in standardized_df.columns and not standardized_df['instance'].isna().all():
                    standardized_df['component'] = 'pd'
                    standardized_df['instance_detail'] = standardized_df['instance']
                else:
                    standardized_df['component'] = 'pd'

                if 'cpu_usage' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['cpu_usage']
                elif 'memory_usage' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['memory_usage']

            elif metric_type and 'node' in metric_type:
                # Node数据标准化
                if 'kubernetes_node' in standardized_df.columns:
                    standardized_df['component'] = standardized_df['kubernetes_node']
                elif 'instance' in standardized_df.columns:
                    standardized_df['component'] = standardized_df['instance']
                else:
                    standardized_df['component'] = 'unknown_node'

                # Node指标值映射
                if 'cpu_usage_rate' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['cpu_usage_rate']
                elif 'memory_usage_rate' in standardized_df.columns:
                    standardized_df['value'] = standardized_df['memory_usage_rate']

            # 确保有value字段
            if 'value' not in standardized_df.columns:
                # 尝试从其他数值字段推断
                numeric_columns = standardized_df.select_dtypes(include=['number']).columns
                if len(numeric_columns) > 0:
                    # 使用第一个数值字段作为value
                    standardized_df['value'] = standardized_df[numeric_columns[0]]
                    self.logger.debug(f"使用 {numeric_columns[0]} 作为 value 字段")
                else:
                    standardized_df['value'] = 0.0

            return standardized_df

        except Exception as e:
            self.logger.warning(f"基础设施数据标准化失败: {e}")
            return df

    def _extract_metric_type_from_path(self, file_path: str) -> str:
        """从文件路径提取具体的指标类型"""
        import os
        filename = os.path.basename(file_path)

        # 基础设施指标
        if 'infra_pod_pod_cpu_usage' in filename:
            return 'cpu_usage'
        elif 'infra_pod_pod_memory_working_set_bytes' in filename:
            return 'memory_usage'
        elif 'infra_pod_pod_network_receive_bytes' in filename:
            return 'network_receive'
        elif 'infra_pod_pod_network_transmit_bytes' in filename:
            return 'network_transmit'
        elif 'infra_node_node_cpu_usage_rate' in filename:
            return 'node_cpu_usage'
        elif 'infra_node_node_memory_usage_rate' in filename:
            return 'node_memory_usage'
        elif 'infra_tidb_cpu_usage' in filename:
            return 'tidb_cpu_usage'
        elif 'infra_tidb_memory_usage' in filename:
            return 'tidb_memory_usage'
        elif 'infra_tikv_cpu_usage' in filename:
            return 'tikv_cpu_usage'
        elif 'infra_tikv_memory_usage' in filename:
            return 'tikv_memory_usage'
        elif 'infra_pd_cpu_usage' in filename:
            return 'pd_cpu_usage'
        elif 'infra_pd_memory_usage' in filename:
            return 'pd_memory_usage'

        # APM指标
        elif filename.startswith('pod_') and not filename.startswith('pod_ns_'):
            return 'apm_pod_metrics'
        elif filename.startswith('service_'):
            return 'apm_service_metrics'
        elif filename.startswith('pod_ns_'):
            return 'apm_namespace_metrics'

        # 其他指标
        elif 'qps' in filename:
            return 'qps'
        elif 'duration' in filename:
            return 'response_time'
        elif 'disk' in filename:
            return 'disk_io'
        elif 'network' in filename:
            return 'network_io'

        # 默认返回通用类型
        return 'system_metrics'

    def _filter_data_by_utc_time(self, df: pd.DataFrame, start_utc: str, end_utc: str, time_column: str) -> pd.DataFrame:
        """使用UTC时间过滤数据"""
        if df.empty or time_column not in df.columns:
            return df

        try:
            # 转换时间列为datetime类型，确保时区一致
            df[time_column] = pd.to_datetime(df[time_column], utc=True)

            # 转换UTC时间字符串为datetime，确保时区一致
            start_dt = pd.to_datetime(start_utc, utc=True)
            end_dt = pd.to_datetime(end_utc, utc=True)

            # 过滤数据
            mask = (df[time_column] >= start_dt) & (df[time_column] <= end_dt)
            return df[mask].copy()

        except Exception as e:
            self.logger.error(f"Time filtering failed: {e}")
            # 如果时间过滤失败，返回原始数据
            return df

    def _filter_metric_data_by_minute_boundaries(self, df: pd.DataFrame, start_utc: str, end_utc: str, time_column: str = 'time') -> pd.DataFrame:
        """🔧 新增：按分钟边界范围过滤metric数据（60秒粒度）"""
        if df.empty or time_column not in df.columns:
            return df

        try:
            # 转换时间列为datetime类型，确保时区一致
            df[time_column] = pd.to_datetime(df[time_column], utc=True)

            # 转换UTC时间字符串为datetime
            start_dt = pd.to_datetime(start_utc, utc=True)
            end_dt = pd.to_datetime(end_utc, utc=True)

            # 映射到分钟边界范围
            mapped_start, mapped_end = self._map_to_minute_boundaries(start_dt, end_dt)

            # 使用分钟边界范围过滤数据
            mask = (df[time_column] >= mapped_start) & (df[time_column] <= mapped_end)
            filtered_df = df[mask].copy()

            self.logger.debug(f"🔧 分钟边界映射: {start_utc} - {end_utc}")
            self.logger.debug(f"🔧 映射到范围: {mapped_start} - {mapped_end}")
            self.logger.debug(f"🔧 过滤结果: {len(filtered_df)} 条记录")

            return filtered_df

        except Exception as e:
            self.logger.error(f"Minute boundary filtering failed: {e}")
            # 如果分钟边界过滤失败，回退到普通时间过滤
            return self._filter_data_by_utc_time(df, start_utc, end_utc, time_column)

    def _map_to_minute_boundaries(self, start_time: datetime, end_time: datetime) -> Tuple[datetime, datetime]:
        """将任意时间范围映射到分钟边界范围"""

        # 开始时间向下取整到分钟边界
        mapped_start = start_time.replace(second=0, microsecond=0)

        # 结束时间向上取整到分钟边界
        if end_time.second > 0 or end_time.microsecond > 0:
            mapped_end = end_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        else:
            mapped_end = end_time.replace(second=0, microsecond=0)

        return mapped_start, mapped_end
    
    async def _load_traces_data(self, case_uuid: str, start_time: str, end_time: str) -> pd.DataFrame:
        """加载调用链数据 - 基于示例的正确实现"""
        try:
            # 获取需要加载的文件列表
            files = self.get_time_range_files(start_time, end_time)
            all_traces = []

            self.logger.info(f"Found {len(files['traces'])} trace files to load")

            for trace_file in files['traces']:
                try:
                    df = pd.read_parquet(trace_file)
                    if not df.empty:
                        all_traces.append(df)
                        self.logger.debug(f"Loaded trace data from {trace_file}: {len(df)} records")
                except Exception as e:
                    self.logger.warning(f"Error reading {trace_file}: {e}")

            if not all_traces:
                self.logger.warning("No traces data loaded")
                return pd.DataFrame()

            # 合并所有调用链数据
            combined_df = pd.concat(all_traces, ignore_index=True)

            # 使用UTC时间精确过滤数据（特殊处理startTimeMillis字段）
            filtered_df = self._filter_trace_data_by_utc_time(combined_df, start_time, end_time)

            self.logger.info(f"Loaded {len(filtered_df)} trace records from {len(all_traces)} files after time filtering")
            return filtered_df

        except Exception as e:
            self.logger.error(f"Error loading traces data: {e}")
            return pd.DataFrame()

    def _filter_trace_data_by_utc_time(self, df: pd.DataFrame, start_utc: str, end_utc: str) -> pd.DataFrame:
        """使用UTC时间过滤调用链数据（特殊处理startTimeMillis字段）"""
        if df.empty or 'startTimeMillis' not in df.columns:
            return df

        try:
            # 转换UTC时间字符串为毫秒时间戳，确保时区处理正确
            start_dt = pd.to_datetime(start_utc, utc=True)
            end_dt = pd.to_datetime(end_utc, utc=True)
            start_millis = int(start_dt.timestamp() * 1000)
            end_millis = int(end_dt.timestamp() * 1000)

            # 过滤数据
            mask = (df['startTimeMillis'] >= start_millis) & (df['startTimeMillis'] <= end_millis)
            return df[mask].copy()

        except Exception as e:
            self.logger.error(f"Trace time filtering failed: {e}")
            # 如果时间过滤失败，返回原始数据
            return df
    
    async def _load_logs_data(self, case_uuid: str, start_time: str, end_time: str) -> pd.DataFrame:
        """加载日志数据 - 基于示例的正确实现"""
        try:
            # 获取需要加载的文件列表
            files = self.get_time_range_files(start_time, end_time)
            all_logs = []

            self.logger.info(f"Found {len(files['logs'])} log files to load")

            for log_file in files['logs']:
                try:
                    df = pd.read_parquet(log_file)
                    if not df.empty:
                        all_logs.append(df)
                        self.logger.debug(f"Loaded log data from {log_file}: {len(df)} records")
                except Exception as e:
                    self.logger.warning(f"Error reading {log_file}: {e}")

            if not all_logs:
                self.logger.warning("No logs data loaded")
                return pd.DataFrame()

            # 合并所有日志数据
            combined_df = pd.concat(all_logs, ignore_index=True)

            # 使用UTC时间精确过滤数据
            filtered_df = self._filter_data_by_utc_time(combined_df, start_time, end_time, '@timestamp')

            self.logger.info(f"Loaded {len(filtered_df)} log records from {len(all_logs)} files after time filtering")
            return filtered_df

        except Exception as e:
            self.logger.error(f"Error loading logs data: {e}")
            return pd.DataFrame()

    def _should_skip_file_by_time_range(self, file_path: str, start_time: str, end_time: str) -> bool:
        """基于文件元数据快速判断是否需要跳过该文件（时间范围优化）"""
        try:
            import os
            from pathlib import Path

            # 获取文件的修改时间作为时间范围的参考
            file_stat = os.stat(file_path)
            file_mtime = pd.to_datetime(file_stat.st_mtime, unit='s')

            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)

            # 给文件时间一个缓冲区（1小时），避免边界情况
            buffer_hours = 1
            file_start = file_mtime - pd.Timedelta(hours=buffer_hours)
            file_end = file_mtime + pd.Timedelta(hours=buffer_hours)

            # 如果文件时间范围与查询时间范围没有重叠，则跳过
            if file_end < start_dt or file_start > end_dt:
                return True

            return False

        except Exception as e:
            # 如果检查失败，保守策略：不跳过文件
            self.logger.debug(f"文件时间范围检查失败 {file_path}: {e}")
            return False

    async def _handle_llm_guided_data_load_request(self, message: LLMGuidedDataLoadRequest, ctx: MessageContext) -> LLMGuidedDataLoadResponse:
        """处理基于LLM分析结果的精确数据加载请求"""
        self.logger.info(f"🎯 LLM引导的精确数据加载: {message.agent_type}")

        try:
            # 解析LLM分析结果
            llm_result = message.llm_analysis_result
            target_components = message.target_components
            target_time_ranges = message.target_time_ranges

            self.logger.info(f"🔍 目标组件: {target_components}")
            self.logger.info(f"⏰ 目标时间范围: {len(target_time_ranges)} 个")

            # 根据Agent类型执行精确数据获取
            if message.agent_type == "metric":
                targeted_data = await self._load_targeted_metrics(
                    message.case_uuid, message.start_time, message.end_time,
                    target_components, target_time_ranges, message.data_requirements
                )
            elif message.agent_type == "trace":
                targeted_data = await self._load_targeted_traces(
                    message.case_uuid, message.start_time, message.end_time,
                    target_components, target_time_ranges, message.data_requirements
                )
            elif message.agent_type == "log":
                targeted_data = await self._load_targeted_logs(
                    message.case_uuid, message.start_time, message.end_time,
                    target_components, target_time_ranges, message.data_requirements
                )
            else:
                raise ValueError(f"不支持的Agent类型: {message.agent_type}")

            # 计算数据质量指标
            data_quality_metrics = self._calculate_targeted_data_quality(targeted_data)

            self.logger.info(f"✅ LLM引导数据加载完成: {len(targeted_data)} 个目标数据集")

            return LLMGuidedDataLoadResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                agent_type=message.agent_type,
                targeted_data=targeted_data,
                data_quality_metrics=data_quality_metrics,
                success=True
            )

        except Exception as e:
            self.logger.error(f"LLM引导数据加载失败: {e}")
            return LLMGuidedDataLoadResponse(
                case_uuid=message.case_uuid,
                start_time=message.start_time,
                end_time=message.end_time,
                agent_type=message.agent_type,
                targeted_data={},
                data_quality_metrics={},
                success=False,
                error=str(e)
            )

    async def _load_targeted_metrics(self, case_uuid: str, start_time: str, end_time: str,
                                   target_components: List[str], target_time_ranges: List[Dict[str, str]],
                                   data_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """加载目标指标数据 - 基于精确文件定位"""
        self.logger.info(f"📊 加载目标指标数据: {target_components}")

        targeted_data = {}

        for component in target_components:
            component_data = {}

            # 按时间范围分段获取数据
            for time_range in target_time_ranges:
                range_start = time_range.get('start', start_time)
                range_end = time_range.get('end', end_time)

                # 获取目标文件列表
                target_files = self._get_targeted_metric_files(component, range_start, range_end)

                if target_files:
                    # 加载并合并目标文件数据
                    combined_data = []
                    for file_path in target_files:
                        try:
                            df = pd.read_parquet(file_path)
                            # 按时间过滤
                            filtered_df = self._filter_data_by_utc_time(df, range_start, range_end, 'time')
                            if not filtered_df.empty:
                                combined_data.append(filtered_df)
                        except Exception as e:
                            self.logger.warning(f"读取文件失败 {file_path}: {e}")

                    if combined_data:
                        merged_data = pd.concat(combined_data, ignore_index=True)
                        time_key = f"{range_start}_{range_end}"
                        component_data[time_key] = {
                            'data': merged_data,
                            'metrics_count': len(merged_data),
                            'time_span': f"{range_start} to {range_end}",
                            'key_metrics': self._extract_key_metrics(merged_data, data_requirements),
                            'source_files': target_files
                        }

            if component_data:
                targeted_data[component] = component_data
                self.logger.info(f"✅ {component}: {len(component_data)} 个时间段的精确数据")

        return targeted_data

    async def _load_targeted_traces(self, case_uuid: str, start_time: str, end_time: str,
                                  target_components: List[str], target_time_ranges: List[Dict[str, str]],
                                  data_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """加载目标调用链数据 - 基于精确文件定位"""
        self.logger.info(f"🔗 加载目标调用链数据: {target_components}")

        targeted_data = {}

        for component in target_components:
            component_data = {}

            # 按时间范围分段获取数据
            for time_range in target_time_ranges:
                range_start = time_range.get('start', start_time)
                range_end = time_range.get('end', end_time)

                # 获取目标文件列表
                target_files = self._get_targeted_trace_files(component, range_start, range_end)

                if target_files:
                    # 加载并合并目标文件数据
                    combined_data = []
                    for file_path in target_files:
                        try:
                            df = pd.read_parquet(file_path)
                            # 按时间过滤
                            filtered_df = self._filter_data_by_utc_time(df, range_start, range_end, 'startTime')
                            # 按服务过滤
                            service_filtered_df = self._filter_traces_by_service_and_time(
                                filtered_df, component, range_start, range_end
                            )
                            if not service_filtered_df.empty:
                                combined_data.append(service_filtered_df)
                        except Exception as e:
                            self.logger.warning(f"读取调用链文件失败 {file_path}: {e}")

                    if combined_data:
                        merged_data = pd.concat(combined_data, ignore_index=True)
                        time_key = f"{range_start}_{range_end}"
                        component_data[time_key] = {
                            'data': merged_data,
                            'span_count': len(merged_data),
                            'time_span': f"{range_start} to {range_end}",
                            'latency_stats': self._calculate_latency_stats(merged_data),
                            'error_spans': self._extract_error_spans(merged_data),
                            'source_files': target_files
                        }

            if component_data:
                targeted_data[component] = component_data
                self.logger.info(f"✅ {component}: {len(component_data)} 个时间段的精确调用链数据")

        return targeted_data

    async def _load_targeted_logs(self, case_uuid: str, start_time: str, end_time: str,
                                target_components: List[str], target_time_ranges: List[Dict[str, str]],
                                data_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """加载目标日志数据"""
        self.logger.info(f"📝 加载目标日志数据: {target_components}")

        targeted_data = {}

        # 获取基础日志数据
        base_logs = self._load_logs_data(case_uuid, start_time, end_time)

        for component in target_components:
            component_data = {}

            # 按时间范围分段获取数据
            for time_range in target_time_ranges:
                range_start = time_range.get('start', start_time)
                range_end = time_range.get('end', end_time)

                # 过滤指定组件和时间范围的日志数据
                filtered_data = self._filter_logs_by_component_and_time(
                    base_logs, component, range_start, range_end
                )

                if not filtered_data.empty:
                    time_key = f"{range_start}_{range_end}"
                    component_data[time_key] = {
                        'data': filtered_data,
                        'log_count': len(filtered_data),
                        'time_span': f"{range_start} to {range_end}",
                        'error_logs': self._extract_error_logs(filtered_data),
                        'log_levels': self._analyze_log_levels(filtered_data)
                    }

            if component_data:
                targeted_data[component] = component_data
                self.logger.info(f"✅ {component}: {len(component_data)} 个时间段的日志数据")

        return targeted_data

    def _get_targeted_metric_files(self, component: str, start_time: str, end_time: str) -> List[str]:
        """根据组件和时间范围获取目标指标文件"""
        files = []

        try:
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)

            # 转换为北京时间
            start_beijing_dt = start_dt + pd.Timedelta(hours=8)
            end_beijing_dt = end_dt + pd.Timedelta(hours=8)

            # 确定需要的日期范围
            dates_needed = set()
            current_dt = start_beijing_dt.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = end_beijing_dt.replace(hour=0, minute=0, second=0, microsecond=0)

            while current_dt <= end_date:
                dates_needed.add(current_dt.strftime('%Y-%m-%d'))
                current_dt += pd.Timedelta(days=1)

            for beijing_date in dates_needed:
                date_dir = Path(self.data_root_path) / beijing_date
                metric_dir = date_dir / "metric-parquet"

                if metric_dir.exists():
                    # 查找Pod级别的指标文件
                    apm_pod_dir = metric_dir / "apm" / "pod"
                    if apm_pod_dir.exists():
                        # 匹配组件名称的Pod文件
                        for parquet_file in apm_pod_dir.glob(f"pod_{component}*_{beijing_date}.parquet"):
                            files.append(str(parquet_file))

                    # 查找Service级别的指标文件
                    apm_service_dir = metric_dir / "apm" / "service"
                    if apm_service_dir.exists():
                        service_file = apm_service_dir / f"service_{component}_{beijing_date}.parquet"
                        if service_file.exists():
                            files.append(str(service_file))

            self.logger.info(f"🎯 为组件 {component} 找到 {len(files)} 个目标指标文件")
            return files

        except Exception as e:
            self.logger.error(f"获取目标指标文件失败: {e}")
            return []

    def _get_targeted_trace_files(self, component: str, start_time: str, end_time: str) -> List[str]:
        """根据组件和时间范围获取目标调用链文件"""
        files = []

        try:
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)

            # 转换为北京时间
            start_beijing_dt = start_dt + pd.Timedelta(hours=8)
            end_beijing_dt = end_dt + pd.Timedelta(hours=8)

            # 确定需要的小时范围
            start_hour = start_beijing_dt.replace(minute=0, second=0, microsecond=0)
            end_hour = end_beijing_dt.replace(minute=0, second=0, microsecond=0)

            current_hour = start_hour
            while current_hour <= end_hour:
                beijing_date = current_hour.strftime('%Y-%m-%d')
                beijing_hour = current_hour.strftime('%H')

                date_dir = Path(self.data_root_path) / beijing_date
                trace_filename = f"trace_jaeger-span_{beijing_date}_{beijing_hour}-00-00.parquet"
                trace_file = date_dir / "trace-parquet" / trace_filename

                if trace_file.exists():
                    files.append(str(trace_file))

                current_hour += pd.Timedelta(hours=1)

            self.logger.info(f"🎯 为组件 {component} 找到 {len(files)} 个目标调用链文件")
            return files

        except Exception as e:
            self.logger.error(f"获取目标调用链文件失败: {e}")
            return []

    def _get_targeted_log_files(self, component: str, start_time: str, end_time: str) -> List[str]:
        """根据组件和时间范围获取目标日志文件"""
        files = []

        try:
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)

            # 转换为北京时间
            start_beijing_dt = start_dt + pd.Timedelta(hours=8)
            end_beijing_dt = end_dt + pd.Timedelta(hours=8)

            # 确定需要的小时范围
            start_hour = start_beijing_dt.replace(minute=0, second=0, microsecond=0)
            end_hour = end_beijing_dt.replace(minute=0, second=0, microsecond=0)

            current_hour = start_hour
            while current_hour <= end_hour:
                beijing_date = current_hour.strftime('%Y-%m-%d')
                beijing_hour = current_hour.strftime('%H')

                date_dir = Path(self.data_root_path) / beijing_date
                log_filename = f"log_filebeat-server_{beijing_date}_{beijing_hour}-00-00.parquet"
                log_file = date_dir / "log-parquet" / log_filename

                if log_file.exists():
                    files.append(str(log_file))

                current_hour += pd.Timedelta(hours=1)

            self.logger.info(f"🎯 为组件 {component} 找到 {len(files)} 个目标日志文件")
            return files

        except Exception as e:
            self.logger.error(f"获取目标日志文件失败: {e}")
            return []

    def _filter_metrics_by_component_and_time(self, metrics_df: pd.DataFrame, component: str,
                                            start_time: str, end_time: str) -> pd.DataFrame:
        """按组件和时间过滤指标数据"""
        if metrics_df.empty:
            return pd.DataFrame()

        try:
            # 🔧 使用分钟边界范围过滤metric数据
            filtered_df = self._filter_metric_data_by_minute_boundaries(metrics_df, start_time, end_time, 'time')

            # 组件过滤 - 检查多个可能的服务名称字段
            service_columns = ['k8s_app', 'service', 'k8s_pod', 'pod']
            for col in service_columns:
                if col in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df[col].str.contains(component, na=False)]
                    break

            return filtered_df
        except Exception as e:
            self.logger.warning(f"指标数据过滤失败: {e}")
            return pd.DataFrame()

    def _filter_traces_by_service_and_time(self, traces_df: pd.DataFrame, service: str,
                                         start_time: str, end_time: str) -> pd.DataFrame:
        """按服务和时间过滤调用链数据"""
        if traces_df.empty:
            return pd.DataFrame()

        try:
            # 时间过滤
            filtered_df = self._filter_data_by_utc_time(traces_df, start_time, end_time, 'startTime')

            # 服务过滤 - 检查process字段中的serviceName
            if 'process' in filtered_df.columns:
                service_mask = filtered_df['process'].apply(
                    lambda x: isinstance(x, dict) and x.get('serviceName', '') == service
                )
                filtered_df = filtered_df[service_mask]

            return filtered_df
        except Exception as e:
            self.logger.warning(f"调用链数据过滤失败: {e}")
            return pd.DataFrame()

    def _filter_logs_by_component_and_time(self, logs_df: pd.DataFrame, component: str,
                                         start_time: str, end_time: str) -> pd.DataFrame:
        """按组件和时间过滤日志数据"""
        if logs_df.empty:
            return pd.DataFrame()

        try:
            # 时间过滤
            filtered_df = self._filter_data_by_utc_time(logs_df, start_time, end_time, '@timestamp')

            # 组件过滤 - 检查k8s_pod字段
            if 'k8s_pod' in filtered_df.columns:
                filtered_df = filtered_df[filtered_df['k8s_pod'].str.contains(component, na=False)]

            return filtered_df
        except Exception as e:
            self.logger.warning(f"日志数据过滤失败: {e}")
            return pd.DataFrame()

    def _extract_key_metrics(self, metrics_df: pd.DataFrame, data_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键指标"""
        key_metrics = {}

        try:
            # 提取CPU、内存等关键指标的统计信息
            metric_columns = [col for col in metrics_df.columns if any(
                keyword in col.lower() for keyword in ['cpu', 'memory', 'error', 'latency']
            )]

            for col in metric_columns:
                if metrics_df[col].dtype in ['float64', 'int64']:
                    key_metrics[col] = {
                        'mean': float(metrics_df[col].mean()),
                        'max': float(metrics_df[col].max()),
                        'min': float(metrics_df[col].min()),
                        'std': float(metrics_df[col].std())
                    }
        except Exception as e:
            self.logger.warning(f"关键指标提取失败: {e}")

        return key_metrics

    def _calculate_latency_stats(self, traces_df: pd.DataFrame) -> Dict[str, Any]:
        """计算延迟统计信息"""
        latency_stats = {}

        try:
            if 'duration' in traces_df.columns:
                durations = traces_df['duration'].astype(float)
                latency_stats = {
                    'mean_latency': float(durations.mean()),
                    'max_latency': float(durations.max()),
                    'min_latency': float(durations.min()),
                    'p95_latency': float(durations.quantile(0.95)),
                    'p99_latency': float(durations.quantile(0.99))
                }
        except Exception as e:
            self.logger.warning(f"延迟统计计算失败: {e}")

        return latency_stats

    def _extract_error_spans(self, traces_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """提取错误span"""
        error_spans = []

        try:
            # 检查tags中的错误信息
            for _, row in traces_df.iterrows():
                tags = row.get('tags', [])
                if isinstance(tags, list):
                    for tag in tags:
                        if isinstance(tag, dict) and tag.get('key') == 'error' and tag.get('value') == 'true':
                            error_spans.append({
                                'traceID': row.get('traceID'),
                                'spanID': row.get('spanID'),
                                'operationName': row.get('operationName'),
                                'duration': row.get('duration')
                            })
                            break
        except Exception as e:
            self.logger.warning(f"错误span提取失败: {e}")

        return error_spans

    def _extract_error_logs(self, logs_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """提取错误日志"""
        error_logs = []

        try:
            # 检查message字段中的错误关键词
            error_keywords = ['error', 'exception', 'failed', 'timeout', 'panic']

            if 'message' in logs_df.columns:
                for _, row in logs_df.iterrows():
                    message = str(row.get('message', '')).lower()
                    if any(keyword in message for keyword in error_keywords):
                        error_logs.append({
                            'timestamp': row.get('@timestamp'),
                            'message': row.get('message'),
                            'k8s_pod': row.get('k8s_pod'),
                            'k8s_namespace': row.get('k8s_namespace')
                        })
        except Exception as e:
            self.logger.warning(f"错误日志提取失败: {e}")

        return error_logs

    def _analyze_log_levels(self, logs_df: pd.DataFrame) -> Dict[str, int]:
        """分析日志级别分布"""
        log_levels = {}

        try:
            # 从message中推断日志级别
            levels = ['ERROR', 'WARN', 'INFO', 'DEBUG']

            if 'message' in logs_df.columns:
                for level in levels:
                    count = logs_df['message'].str.contains(level, case=False, na=False).sum()
                    log_levels[level] = int(count)
        except Exception as e:
            self.logger.warning(f"日志级别分析失败: {e}")

        return log_levels

    def _calculate_targeted_data_quality(self, targeted_data: Dict[str, Any]) -> Dict[str, float]:
        """计算目标数据的质量指标"""
        quality_metrics = {}

        try:
            total_components = len(targeted_data)
            total_time_ranges = 0
            total_records = 0

            for component, component_data in targeted_data.items():
                component_time_ranges = len(component_data)
                total_time_ranges += component_time_ranges

                for time_key, time_data in component_data.items():
                    if 'data' in time_data:
                        total_records += len(time_data['data'])

            quality_metrics = {
                'component_coverage': float(total_components),
                'time_range_coverage': float(total_time_ranges),
                'total_records': float(total_records),
                'data_completeness': 1.0 if total_records > 0 else 0.0,
                'average_records_per_component': float(total_records / max(total_components, 1))
            }
        except Exception as e:
            self.logger.warning(f"数据质量计算失败: {e}")
            quality_metrics = {'data_completeness': 0.0}

        return quality_metrics
