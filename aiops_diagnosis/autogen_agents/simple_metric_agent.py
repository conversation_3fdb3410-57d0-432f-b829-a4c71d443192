#!/usr/bin/env python3
"""
基于实际数据优化的Metric分析智能体
核心改进：
1. 基于实际数据重新校准所有阈值
2. 增强数据格式验证和type字段处理
3. 实现动态阈值学习机制
4. 删除冗余设计，简化架构
"""

import pandas as pd
import numpy as np
import re
import os
import logging
import json
import yaml
import warnings
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict
from datetime import datetime, timedelta
from pathlib import Path

# 🔧 抑制NumPy的数值计算警告
warnings.filterwarnings('ignore', category=RuntimeWarning, message='invalid value encountered in divide')
warnings.filterwarnings('ignore', category=RuntimeWarning, message='invalid value encountered in true_divide')

from .base_aiops_agent import (
    BaseAIOpsAgent, AnalysisRequest, AnalysisResponse,
    SuspiciousEntity, DataLocation, EntityInfo, AnomalyFeature, TimeRange
)
from ..utils.file_path_locator import FilePathLocator
from ..utils.unit_normalizer import UnitNormalizer



class ThresholdStrategyManager:
    """阈值策略管理器 - 支持动态切换基线阈值和传统阈值"""

    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_path = config_path

        # 阈值策略枚举
        self.STRATEGY_BASELINE = "baseline"
        self.STRATEGY_DYNAMIC = "dynamic"
        self.STRATEGY_STATISTICAL = "statistical"
        self.STRATEGY_HYBRID = "hybrid"

        # 默认配置
        self.default_config = {
            "global_strategy": self.STRATEGY_HYBRID,  # 默认使用混合策略
            "apm_strategy": self.STRATEGY_BASELINE,   # APM优先使用基线
            "infra_strategy": self.STRATEGY_DYNAMIC,  # 基础设施使用动态阈值
            "fallback_strategy": self.STRATEGY_DYNAMIC,  # 回退策略
            "enable_baseline": True,
            "enable_dynamic_fallback": True,
            "baseline_priority": True,  # 基线优先
            "log_strategy_decisions": True
        }

        # 加载配置
        self.config = self._load_config()
        self.logger.info(f"✅ 阈值策略管理器初始化完成，策略: {self.config['global_strategy']}")

    def _load_config(self) -> Dict[str, Any]:
        """加载阈值策略配置"""
        try:
            # 1. 尝试从环境变量加载
            env_strategy = os.getenv('AIOPS_THRESHOLD_STRATEGY')
            if env_strategy:
                config = self.default_config.copy()
                config['global_strategy'] = env_strategy
                self.logger.info(f"📊 从环境变量加载阈值策略: {env_strategy}")
                return config

            # 2. 尝试从配置文件加载
            if self.config_path and os.path.exists(self.config_path):
                self.logger.info(f"📁 从配置文件加载阈值策略: {self.config_path}")
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    if self.config_path.endswith('.json'):
                        file_config = json.load(f)
                    elif self.config_path.endswith(('.yml', '.yaml')):
                        file_config = yaml.safe_load(f)
                    else:
                        file_config = {}

                # 合并配置
                config = self.default_config.copy()
                config.update(file_config.get('threshold_strategy', {}))
                return config

            # 3. 使用默认配置
            self.logger.info("📊 使用默认阈值策略配置")
            return self.default_config.copy()

        except Exception as e:
            self.logger.warning(f"⚠️ 加载阈值策略配置失败，使用默认配置: {e}")
            return self.default_config.copy()

    def get_strategy_for_category(self, category: str, object_name: str = None) -> str:
        """获取指定类别的阈值策略"""
        try:
            # 1. 检查类别特定策略
            category_key = f"{category}_strategy"
            if category_key in self.config:
                strategy = self.config[category_key]
                if self.config.get('log_strategy_decisions', False):
                    self.logger.debug(f"🎯 {category}类别使用策略: {strategy}")
                return strategy

            # 2. 使用全局策略
            global_strategy = self.config.get('global_strategy', self.STRATEGY_HYBRID)
            if self.config.get('log_strategy_decisions', False):
                self.logger.debug(f"🎯 {category}类别使用全局策略: {global_strategy}")
            return global_strategy

        except Exception as e:
            self.logger.error(f"❌ 获取阈值策略失败: {e}")
            return self.STRATEGY_DYNAMIC

    def should_use_baseline(self, category: str, object_name: str = None) -> bool:
        """判断是否应该使用基线阈值"""
        if not self.config.get('enable_baseline', True):
            return False

        strategy = self.get_strategy_for_category(category, object_name)

        if strategy == self.STRATEGY_BASELINE:
            return True
        elif strategy == self.STRATEGY_HYBRID:
            # 混合策略：APM优先基线，其他使用动态
            return category == 'apm' and self.config.get('baseline_priority', True)
        else:
            return False

    def should_fallback_to_dynamic(self, category: str, object_name: str = None) -> bool:
        """判断是否应该回退到动态阈值"""
        return self.config.get('enable_dynamic_fallback', True)

    def get_fallback_strategy(self, category: str, object_name: str = None) -> str:
        """获取回退策略"""
        return self.config.get('fallback_strategy', self.STRATEGY_DYNAMIC)



class APMServiceBaselineManager:
    """APM服务基线数据管理器"""

    def __init__(self, baseline_config_path: Optional[str] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.baseline_config_path = baseline_config_path

        # 🔧 默认APM服务基线数据（基于提供的数据）
        self.default_service_baselines = {
            'adservice': {
                'rrt': 8029.88,
                'error_ratio': 2.73,
                'client_error_ratio': 2.73,
                'server_error_ratio': 0.0,
                'timeout': 3.0
            },
            'cartservice': {
                'rrt': 5475.04,
                'error_ratio': 2.16,
                'client_error_ratio': 2.16,
                'server_error_ratio': 0.0,
                'timeout': 11.0
            },
            'currencyservice': {
                'rrt': 1429.59,
                'error_ratio': 0.0,
                'client_error_ratio': 0.0,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            },
            'productcatalogservice': {
                'rrt': 7577.69,
                'error_ratio': 0.44,
                'client_error_ratio': 0.44,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            },
            'checkoutservice': {
                'rrt': 12232.23,
                'error_ratio': 4.2,
                'client_error_ratio': 4.2,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            },
            'recommendationservice': {
                'rrt': 4928.13,
                'error_ratio': 0.0,
                'client_error_ratio': 0.0,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            },
            'shippingservice': {
                'rrt': 1760.64,
                'error_ratio': 0.0,
                'client_error_ratio': 0.0,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            },
            'emailservice': {
                'rrt': 7259.25,
                'error_ratio': 0.0,
                'client_error_ratio': 0.0,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            },
            'paymentservice': {
                'rrt': 17717.8,
                'error_ratio': 0.0,
                'client_error_ratio': 0.0,
                'server_error_ratio': 0.0,
                'timeout': 0.0
            }
        }

        # 🔧 默认APM Pod基线数据（基于提供的数据）
        self.default_pod_baselines = {
            'adservice-0': {'rrt': 18926.96, 'error_ratio': 8.33, 'client_error_ratio': 8.33, 'server_error_ratio': 0.0, 'timeout': 1.0},
            'adservice-1': {'rrt': 5149.45, 'error_ratio': 3.19, 'client_error_ratio': 3.19, 'server_error_ratio': 0.0, 'timeout': 1.0},
            'adservice-2': {'rrt': 18897.44, 'error_ratio': 8.33, 'client_error_ratio': 8.33, 'server_error_ratio': 0.0, 'timeout': 1.0},
            'cartservice-0': {'rrt': 24780.0, 'error_ratio': 44.44, 'client_error_ratio': 44.44, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'cartservice-1': {'rrt': 5566.07, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 11.0},
            'cartservice-2': {'rrt': 14351.35, 'error_ratio': 25.0, 'client_error_ratio': 25.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'currencyservice-0': {'rrt': 1957.05, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'currencyservice-1': {'rrt': 1550.75, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'currencyservice-2': {'rrt': 4588.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'productcatalogservice-0': {'rrt': 14526.2, 'error_ratio': 23.08, 'client_error_ratio': 23.08, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'productcatalogservice-1': {'rrt': 6639.58, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'productcatalogservice-2': {'rrt': 8365.22, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'checkoutservice-2': {'rrt': 12232.23, 'error_ratio': 4.2, 'client_error_ratio': 4.2, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'recommendationservice-0': {'rrt': 4928.13, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'shippingservice-0': {'rrt': 1988.1, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'shippingservice-1': {'rrt': 6904.5, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'emailservice-0': {'rrt': 7259.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'emailservice-1': {'rrt': 7259.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'emailservice-2': {'rrt': 7259.25, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'paymentservice-0': {'rrt': 17717.8, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'paymentservice-1': {'rrt': 17717.8, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0},
            'paymentservice-2': {'rrt': 17717.8, 'error_ratio': 0.0, 'client_error_ratio': 0.0, 'server_error_ratio': 0.0, 'timeout': 0.0}
        }

        # 加载基线数据
        self.service_baselines = self._load_service_baselines()
        self.pod_baselines = self._load_pod_baselines()
        self.logger.info(f"✅ APM基线管理器初始化完成，加载了{len(self.service_baselines)}个服务基线和{len(self.pod_baselines)}个Pod基线")

        # 🔧 详细日志：显示每个服务的基线数据
        for service_name, baseline in self.service_baselines.items():
            self.logger.info(f"📊 服务基线: {service_name} -> rrt:{baseline['rrt']:.1f}ms, error:{baseline['error_ratio']:.2f}%, timeout:{baseline['timeout']:.1f}%")

        # 🔧 详细日志：显示部分Pod基线数据（避免日志过多）
        pod_count = 0
        for pod_name, baseline in self.pod_baselines.items():
            if pod_count < 5:  # 只显示前5个Pod的基线
                self.logger.info(f"📊 Pod基线: {pod_name} -> rrt:{baseline['rrt']:.1f}ms, error:{baseline['error_ratio']:.2f}%, timeout:{baseline['timeout']:.1f}%")
                pod_count += 1
        if len(self.pod_baselines) > 5:
            self.logger.info(f"📊 ... 还有{len(self.pod_baselines) - 5}个Pod基线数据")

    def _load_service_baselines(self) -> Dict[str, Dict[str, float]]:
        """加载服务级别基线数据"""
        try:
            if self.baseline_config_path and os.path.exists(self.baseline_config_path):
                self.logger.info(f"📁 从配置文件加载服务基线数据: {self.baseline_config_path}")
                with open(self.baseline_config_path, 'r', encoding='utf-8') as f:
                    if self.baseline_config_path.endswith('.json'):
                        config = json.load(f)
                        return config.get('services', self.default_service_baselines.copy())
                    elif self.baseline_config_path.endswith(('.yml', '.yaml')):
                        config = yaml.safe_load(f)
                        return config.get('services', self.default_service_baselines.copy())
            else:
                self.logger.info("📊 使用默认APM服务基线数据")
                return self.default_service_baselines.copy()
        except Exception as e:
            self.logger.warning(f"⚠️ 加载服务基线配置失败，使用默认数据: {e}")
            return self.default_service_baselines.copy()

    def _load_pod_baselines(self) -> Dict[str, Dict[str, float]]:
        """加载Pod级别基线数据"""
        try:
            if self.baseline_config_path and os.path.exists(self.baseline_config_path):
                self.logger.info(f"📁 从配置文件加载Pod基线数据: {self.baseline_config_path}")
                with open(self.baseline_config_path, 'r', encoding='utf-8') as f:
                    if self.baseline_config_path.endswith('.json'):
                        config = json.load(f)
                        return config.get('pods', self.default_pod_baselines.copy())
                    elif self.baseline_config_path.endswith(('.yml', '.yaml')):
                        config = yaml.safe_load(f)
                        return config.get('pods', self.default_pod_baselines.copy())
            else:
                self.logger.info("📊 使用默认APM Pod基线数据")
                return self.default_pod_baselines.copy()
        except Exception as e:
            self.logger.warning(f"⚠️ 加载Pod基线配置失败，使用默认数据: {e}")
            return self.default_pod_baselines.copy()

    def get_service_baseline(self, service_name: str) -> Optional[Dict[str, float]]:
        """获取指定服务的基线数据"""
        baseline = self.service_baselines.get(service_name)
        if baseline:
            self.logger.debug(f"📊 获取服务基线: {service_name} -> rrt:{baseline['rrt']:.1f}ms, error:{baseline['error_ratio']:.2f}%")
        else:
            self.logger.warning(f"⚠️ 未找到服务基线数据: {service_name}")
        return baseline

    def get_pod_baseline(self, pod_name: str) -> Optional[Dict[str, float]]:
        """获取指定Pod的基线数据"""
        baseline = self.pod_baselines.get(pod_name)
        if baseline:
            self.logger.debug(f"📊 获取Pod基线: {pod_name} -> rrt:{baseline['rrt']:.1f}ms, error:{baseline['error_ratio']:.2f}%")
        else:
            self.logger.debug(f"⚠️ 未找到Pod基线数据: {pod_name}")
        return baseline

    def get_baseline_for_object(self, object_name: str, object_type: str = 'unknown') -> Optional[Dict[str, float]]:
        """智能获取对象的基线数据，优先使用Pod级别，回退到服务级别"""
        # 1. 优先尝试Pod级别基线
        pod_baseline = self.get_pod_baseline(object_name)
        if pod_baseline:
            self.logger.info(f"🎯 使用Pod级别基线: {object_name}")
            return pod_baseline

        # 2. 回退到服务级别基线
        service_name = self._extract_service_name_from_pod(object_name)
        if service_name:
            service_baseline = self.get_service_baseline(service_name)
            if service_baseline:
                self.logger.info(f"🔄 回退到服务级别基线: {object_name} -> {service_name}")
                return service_baseline

        # 3. 记录回退情况
        self.logger.warning(f"❌ 未找到基线数据，将回退到动态阈值: {object_name}")
        self._log_baseline_fallback(object_name, object_type, service_name)
        return None

    def _log_baseline_fallback(self, object_name: str, object_type: str, service_name: Optional[str]):
        """记录基线回退情况的详细日志"""
        try:
            fallback_info = {
                'object_name': object_name,
                'object_type': object_type,
                'service_name': service_name,
                'available_pods': len(self.pod_baselines),
                'available_services': len(self.service_baselines),
                'timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"📊 基线回退详情: {fallback_info}")

            # 建议可能的匹配
            if service_name:
                similar_services = [s for s in self.service_baselines.keys() if service_name.lower() in s.lower() or s.lower() in service_name.lower()]
                if similar_services:
                    self.logger.info(f"💡 可能的服务匹配: {similar_services}")

            similar_pods = [p for p in self.pod_baselines.keys() if object_name.lower() in p.lower() or p.lower() in object_name.lower()]
            if similar_pods:
                self.logger.info(f"💡 可能的Pod匹配: {similar_pods[:3]}")  # 只显示前3个

        except Exception as e:
            self.logger.debug(f"记录基线回退日志失败: {e}")

    def _extract_service_name_from_pod(self, pod_name: str) -> Optional[str]:
        """从Pod名称提取服务名称"""
        try:
            if '-' in pod_name:
                # 移除最后的数字部分：adservice-0 -> adservice
                parts = pod_name.split('-')
                if len(parts) >= 2 and parts[-1].isdigit():
                    return '-'.join(parts[:-1])
                else:
                    return parts[0]
            return pod_name
        except:
            return None

    def calculate_thresholds_for_object(self, object_name: str, object_type: str = 'unknown',
                                      warning_tolerance: Dict[str, float] = None,
                                      critical_tolerance: Dict[str, float] = None) -> Dict[str, Dict[str, float]]:
        """计算对象的个性化阈值（支持Pod和服务级别）"""
        baseline = self.get_baseline_for_object(object_name, object_type)
        if not baseline:
            return {}

        return self._calculate_thresholds_from_baseline(baseline, object_name, warning_tolerance, critical_tolerance)

    def calculate_service_thresholds(self, service_name: str,
                                   warning_tolerance: Dict[str, float] = None,
                                   critical_tolerance: Dict[str, float] = None) -> Dict[str, Dict[str, float]]:
        """计算服务的个性化阈值"""
        baseline = self.get_service_baseline(service_name)
        if not baseline:
            return {}

        return self._calculate_thresholds_from_baseline(baseline, service_name, warning_tolerance, critical_tolerance)

    def _calculate_thresholds_from_baseline(self, baseline: Dict[str, float], object_name: str,
                                          warning_tolerance: Dict[str, float] = None,
                                          critical_tolerance: Dict[str, float] = None) -> Dict[str, Dict[str, float]]:
        """从基线数据计算阈值"""

        # 默认容忍度配置
        default_warning = {
            'rrt_factor': 1.3,      # 响应时间+30%
            'error_add': 1.0,       # 错误率+1%
            'timeout_add': 2.0,     # 超时率+2%
            'min_error': 0.5,       # 最小错误率阈值0.5%
            'min_timeout': 1.0      # 最小超时率阈值1.0%
        }

        default_critical = {
            'rrt_factor': 1.5,      # 响应时间+50%
            'error_add': 2.0,       # 错误率+2%
            'timeout_add': 5.0,     # 超时率+5%
            'min_error': 1.0,       # 最小错误率阈值1.0%
            'min_timeout': 3.0      # 最小超时率阈值3.0%
        }

        warning_config = warning_tolerance or default_warning
        critical_config = critical_tolerance or default_critical

        # 计算阈值
        thresholds = {
            'warning': {
                'rrt': baseline['rrt'] * warning_config['rrt_factor'],
                'error_ratio': max(baseline['error_ratio'] + warning_config['error_add'],
                                 warning_config['min_error']),
                'client_error_ratio': max(baseline['client_error_ratio'] + warning_config['error_add'],
                                        warning_config['min_error']),
                'server_error_ratio': max(baseline['server_error_ratio'] + warning_config['error_add'],
                                        warning_config['min_error']),
                'timeout': max(baseline['timeout'] + warning_config['timeout_add'],
                             warning_config['min_timeout'])
            },
            'critical': {
                'rrt': baseline['rrt'] * critical_config['rrt_factor'],
                'error_ratio': max(baseline['error_ratio'] + critical_config['error_add'],
                                 critical_config['min_error']),
                'client_error_ratio': max(baseline['client_error_ratio'] + critical_config['error_add'],
                                        critical_config['min_error']),
                'server_error_ratio': max(baseline['server_error_ratio'] + critical_config['error_add'],
                                        critical_config['min_error']),
                'timeout': max(baseline['timeout'] + critical_config['timeout_add'],
                             critical_config['min_timeout'])
            }
        }

        self.logger.info(f"🎯 计算对象阈值: {object_name}")
        self.logger.info(f"   📊 基线数据: rrt={baseline['rrt']:.1f}ms, error={baseline['error_ratio']:.2f}%, timeout={baseline['timeout']:.1f}%")
        self.logger.info(f"   ⚠️ 警告阈值: rrt={thresholds['warning']['rrt']:.1f}ms, error={thresholds['warning']['error_ratio']:.2f}%, timeout={thresholds['warning']['timeout']:.1f}%")
        self.logger.info(f"   🚨 严重阈值: rrt={thresholds['critical']['rrt']:.1f}ms, error={thresholds['critical']['error_ratio']:.2f}%, timeout={thresholds['critical']['timeout']:.1f}%")

        return thresholds

    def is_anomaly_for_object(self, object_name: str, metric_name: str, value: float, object_type: str = 'unknown') -> Tuple[bool, str, float]:
        """判断对象指标值是否异常（支持Pod和服务级别）"""
        thresholds = self.calculate_thresholds_for_object(object_name, object_type)
        if not thresholds:
            return False, 'normal', 0.0

        warning_threshold = thresholds['warning'].get(metric_name)
        critical_threshold = thresholds['critical'].get(metric_name)

        if warning_threshold is None or critical_threshold is None:
            return False, 'normal', 0.0

        if value >= critical_threshold:
            deviation = value / critical_threshold
            self.logger.info(f"🚨 严重异常: {object_name}.{metric_name}={value:.2f} >= {critical_threshold:.2f} (偏差:{deviation:.2f}x)")
            return True, 'critical', deviation
        elif value >= warning_threshold:
            deviation = value / warning_threshold
            self.logger.info(f"⚠️ 警告异常: {object_name}.{metric_name}={value:.2f} >= {warning_threshold:.2f} (偏差:{deviation:.2f}x)")
            return True, 'warning', deviation
        else:
            return False, 'normal', 0.0

    def is_anomaly(self, service_name: str, metric_name: str, value: float) -> Tuple[bool, str, float]:
        """判断指标值是否异常（向后兼容方法）"""
        return self.is_anomaly_for_object(service_name, metric_name, value, 'service')


class OptimizedThresholdManager:
    """基于实际数据优化的阈值管理器"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self.logger = logging.getLogger(self.__class__.__name__)
        self.learned_thresholds = {}  # 动态学习的阈值

        # 🔧 基于全维度分析结果的精确阈值配置
        self.default_thresholds = {
            'apm': {
                # 🎯 基于异常敏感性分析：CartService CV=10.78最敏感，Frontend CV=4.47
                'rrt': 30000,                   # 响应时间 > 30ms (基于ProductCatalog正常6-7ms，提前检测)
                'rrt_max': 50000,               # 最大响应时间 > 50ms (更严格的阈值)

                # 🎯 基于Frontend error_ratio CV=3.80高敏感性
                'error_ratio': 0.005,           # 错误率 > 0.5% (更敏感的检测)
                'client_error_ratio': 0.005,    # 客户端错误率 > 0.5%
                'server_error_ratio': 0.005,    # 服务端错误率 > 0.5%

                # 🎯 基于实际数据分析优化
                'request': 50,                  # 请求数 < 50时异常 (更敏感)
                'response': 50,                 # 响应数 < 50时异常
                'timeout': 0.5,                 # 超时数 > 0.5 (更严格)

                # 🎯 新增：基于分析发现的关键指标
                'cartservice_rrt_multiplier': 2.0,  # CartService RRT异常倍数 (CV=10.78)
                'frontend_error_threshold': 0.003,  # Frontend专用错误率阈值
            },
            'infra': {
                # 🔧 修复：Node级别指标 - 使用百分比格式匹配实际数据
                'node_cpu_usage_rate': 85.0,        # Node CPU使用率 > 85% (百分比格式)
                'node_memory_usage_rate': 90.0,     # Node内存使用率 > 90% (百分比格式)
                'node_memory_MemAvailable_bytes': 1*1024*1024*1024,  # 可用内存 < 1GB告警
                'node_filesystem_usage_rate': 95.0, # Node磁盘使用率 > 95% (百分比格式)
                'node_filesystem_free_bytes': 1*1024*1024*1024,     # 剩余空间 < 1GB告警
                'node_network_receive_bytes_total': 1000*1024*1024, # 网络接收 > 1GB (提高阈值)
                'node_network_transmit_bytes_total': 1000*1024*1024,# 网络发送 > 1GB (提高阈值)
                'node_network_receive_packets_total': 1000000,      # 网络接收包数 > 100万 (提高阈值)
                'node_network_transmit_packets_total': 1000000,     # 网络发送包数 > 100万 (提高阈值)
                'node_disk_written_bytes_total': 5*1024*1024*1024,  # 磁盘写入 > 5GB (提高阈值)
                'node_disk_read_bytes_total': 5*1024*1024*1024,     # 磁盘读取 > 5GB (提高阈值)
                'node_disk_write_time_seconds_total': 0.5,          # 磁盘写入时间 > 0.5秒 (基于P99值)
                'node_disk_read_time_seconds_total': 0.5,           # 磁盘读取时间 > 0.5秒 (基于P99值)
                'node_sockstat_TCP_inuse': 50000,                   # TCP连接数 > 5万 (提高阈值)

                # 🔧 修复：Pod级别指标 - 基于实际运行情况调整阈值
                # pod_cpu_usage: 提高阈值，避免正常负载被误报
                'pod_cpu_usage': 5.0,               # Pod CPU使用率 > 5% (更合理的阈值)

                # pod_memory_working_set_bytes: 提高阈值
                'pod_memory_working_set_bytes': 200*1024*1024,      # Pod内存 > 200MB (更合理)

                # 🔧 修复：网络指标阈值过低，67B-234B这种微小流量不应该报警
                'pod_network_receive_bytes': 100*1024*1024,         # Pod网络接收 > 100MB (大幅提高)
                'pod_network_transmit_bytes': 100*1024*1024,        # Pod网络发送 > 100MB (大幅提高)
                'pod_network_receive_packets': 100000,              # Pod网络接收包数 > 10万 (大幅提高)
                'pod_network_transmit_packets': 100000,             # Pod网络发送包数 > 10万 (大幅提高)

                # 文件系统I/O阈值：只有redis-cart-0的1.11MB写入是真正的异常
                'pod_fs_writes_bytes': 100*1024*1024,               # Pod文件系统写入 > 100MB (提高阈值)
                'pod_fs_reads_bytes': 100*1024*1024,                # Pod文件系统读取 > 100MB (提高阈值)
                'pod_processes': 50,                 # Pod进程数 > 50 (更合理的阈值)

                # 🎯 新增：Pod实例变更检测阈值
                'pod_restart_indicator': 1,         # Pod重启指示器
                'pod_data_gap_seconds': 300,        # Pod数据间隔 > 5分钟异常
                'pod_performance_deviation': 2.0,   # Pod性能偏差倍数 > 2x
            },
            'tidb': {
                # 🎯 基于TiDB_qps CV=2.02敏感性分析：实际范围0-84.47，平均11.35
                'qps': 80.0,                    # QPS > 80（接近实际最大值84.47，更精确）
                'duration_99th': 0.08,          # 99分位延迟 > 80ms (更严格)
                'duration_95th': 0.04,          # 95分位延迟 > 40ms (更严格)
                'duration_avg': 0.008,          # 平均延迟 > 8ms (更严格)
                'failed_query_ops': 0.5,        # 失败查询 > 0.5 (更敏感)
                'transaction_retry': 3,         # 事务重试 > 3 (更严格)
                'connection_count': 15,         # 连接数 > 15 (更敏感)
                'memory_usage': 3*1024*1024*1024,  # 内存使用 > 3GB (更严格)
                'block_cache_size': 1.5*1024*1024*1024,  # Block Cache > 1.5GB (更严格)
                'cpu_usage': 0.4,               # CPU使用率 > 40% (更严格)
                'top_sql_cpu': 0.25,            # Top SQL CPU > 25% (更严格)

                # 🔧 修复：server_is_up是二进制状态指标，0=下线，1=正常
                'server_is_up': 0.5,            # 服务状态 < 0.5时异常（即只有0时异常）
                'slow_query': 5,                # 慢查询 > 5 (更敏感)
                'uptime': 86400,                # 运行时间 < 1天时可能异常重启
            },
            'other': {
                'pd': {
                    # 资源维度
                    'cpu_usage': 0.8,               # PD CPU使用率 > 80%（小数格式）
                    'memory_usage': 2*1024*1024*1024, # PD内存 > 2GB

                    # 存储维度
                    'storage_size': 10*1024*1024*1024,       # 存储大小 > 10GB
                    'storage_capacity': 50*1024*1024*1024,   # 存储容量 < 50GB告警
                    'storage_used_ratio': 0.9,               # 存储使用率 > 90%

                    # 🔧 基于实际数据修正：PD Region Health阈值
                    # 实际数据显示：miss-peer-region-count: 200-203, undersized: 195-198, empty: 188-191
                    # 这些可能是正常的集群状态，需要设置更高的阈值
                    'region_count': 2000,           # Region总数 > 2000
                    'abnormal_region_count': 50,    # 异常Region > 50

                    # Region Health按type分类的阈值（基于实际数据调整）
                    'miss-peer-region-count': 250,   # 缺失副本Region > 250（实际最大203）
                    'undersized-region-count': 250,  # 副本不足Region > 250（实际最大198）
                    'empty-region-count': 250,       # 空Region > 250（实际最大191）
                    'down-peer-region-count': 10,    # 下线副本Region > 10
                    'pending-peer-region-count': 10, # 待处理副本Region > 10
                    'offline-peer-region-count': 10, # 离线副本Region > 10
                    'extra-peer-region-count': 10,   # 多余副本Region > 10
                    'oversized-region-count': 10,    # 副本过多Region > 10
                    'learner-peer-region-count': 10, # 学习者副本Region > 10
                    'witness-leader-region-count': 10, # 见证者Leader Region > 10

                    # 存储节点维度 - 🔧 修复：状态类指标使用离散阈值
                    'store_up_count': 1.9,           # 正常节点 < 1.9（即<2个时异常）
                    'store_down_count': 0.1,         # 下线存储节点 > 0.1（即≥1个时异常）
                    'store_unhealth_count': 0.1,     # 不健康存储节点 > 0.1
                    'store_slow_count': 0.1,         # 慢存储节点 > 0.1
                    'store_low_space_count': 0.1,    # 低空间存储节点 > 0.1

                    # 角色维度 - 🔧 修复：状态类指标使用离散阈值
                    'leader_count': 0.9,             # Leader数量 < 0.9（即<1个时异常）
                    'learner_count': 20,             # Learner数量 > 20
                    'witness_count': 10,             # Witness数量 > 10
                    'leader_primary': 0.9,           # Leader主节点 < 0.9（即<1时异常）
                    'region_health': 10,             # Region健康度 > 10时异常
                },
                'tikv': {
                    # 资源维度
                    'cpu_usage': 0.8,               # TiKV CPU使用率 > 80%（小数格式）
                    'memory_usage': 4*1024*1024*1024, # TiKV内存 > 4GB

                    # 存储维度
                    'available_size': 10*1024*1024*1024,    # 可用空间 < 10GB告警
                    'capacity_size': 200*1024*1024*1024,    # 总容量 > 200GB异常
                    'store_size': 50*1024*1024*1024,        # 存储大小 > 50GB异常

                    # 🔧 基于实际数据：TiKV QPS最大84.98，平均1.26
                    # 高QPS操作主要是'get'和'acquire_pessimistic_lock'
                    'qps': 50,                       # 总QPS > 50
                    'grpc_qps': 50,                  # gRPC QPS > 50
                    'io_util': 0.8,                  # IO使用率 > 80%
                    'read_mbps': 5*1024*1024*1024,   # 读取速度 > 5GB/s
                    'write_wal_mbps': 100*1024*1024, # WAL写入速度 > 100MB/s

                    # 🎯 基于异常敏感性分析的TiKV操作类型阈值优化
                    # 分析结果：acquire_pessimistic_lock CV=6.75(极高), prewrite CV=3.67(高), scan_lock CV=3.02(高)
                    'get': 80,                       # Get操作QPS > 80 (正常平均51.41，最大84.98)
                    'acquire_pessimistic_lock': 2,   # 悲观锁获取QPS > 2 (CV=6.75极高敏感，正常平均0.04)
                    'batch_get': 1.5,                # BatchGet操作QPS > 1.5 (正常平均0.77，最大1.51)
                    'prewrite': 1.5,                 # Prewrite操作QPS > 1.5 (CV=3.67高敏感，正常平均0.06)
                    'commit': 0.8,                   # Commit操作QPS > 0.8 (正常平均0.04，最大0.82)
                    'scan_lock': 1.5,                # ScanLock操作QPS > 1.5 (CV=3.00高敏感，正常平均0.45)
                    'key_mvcc': 0.06,                # KeyMVCC操作QPS > 0.06 (正常平均0.04，最大0.07)

                    # Raft维度（数据单位是秒）
                    'raft_propose_wait': 0.1,        # Raft提议等待 > 100ms
                    'raft_apply_wait': 0.05,         # Raft应用等待 > 50ms

                    # 集群维度
                    'region_pending': 200000,        # 待处理Region > 20万
                    'snapshot_apply_count': 20,      # 快照应用数 > 20
                    'server_is_up': 0.5,             # 服务状态 < 0.5时异常（即只有0时异常）

                    # 存储引擎维度
                    'rocksdb_write_stall': 0.5,      # RocksDB写入停顿 > 0.5
                    'threadpool_readpool_cpu': 0.8,  # 读线程池CPU > 80%
                    'uptime': 86400,                  # 运行时间 < 1天时可能异常重启
                }
            }
        }

        # 加载配置文件中的阈值
        self.custom_thresholds = self._load_threshold_config()

    def learn_threshold_from_data(self, df: pd.DataFrame, metric_name: str,
                                category: str, percentile: float = 0.95) -> float:
        """从历史数据学习动态阈值 - 增强版本，避免不合理阈值"""
        try:
            if df.empty or metric_name not in df.columns:
                return self.get_threshold(category, metric_name)

            # 🔧 对于状态类指标，禁用动态学习（特别是二进制状态指标）
            status_metrics = {
                'uptime', 'server_is_up', 'store_up_count', 'leader_primary',
                'leader_count', 'witness_count', 'learner_count', 'store_down_count',
                'store_unhealth_count', 'store_slow_count', 'store_low_space_count',
                'is_up', 'primary'  # 🔧 新增二进制状态指标
            }

            if any(status_metric in metric_name for status_metric in status_metrics):
                self.logger.debug(f"⚠️ 跳过动态学习: {metric_name} (状态类指标，固定0/1值)")
                return self.get_threshold(category, metric_name)

            # 获取有效数据
            valid_data = df[df[metric_name].notna() & (df[metric_name] > 0)]
            if len(valid_data) < 10:  # 数据量太少，使用默认阈值
                return self.get_threshold(category, metric_name)

            # 🔧 检查数据是否异常（可能在故障期间学习）
            data_std = valid_data[metric_name].std()
            data_mean = valid_data[metric_name].mean()
            cv = data_std / data_mean if data_mean > 0 else float('inf')

            # 如果变异系数过大，说明数据不稳定，不适合学习
            if cv > 2.0:  # 变异系数 > 200%
                self.logger.warning(f"⚠️ 数据变异过大，跳过学习: {metric_name} (CV={cv:.2f})")
                return self.get_threshold(category, metric_name)

            # 计算动态阈值
            learned_threshold = valid_data[metric_name].quantile(percentile)

            # 🔧 增强的阈值合理性检查
            default_threshold = self.get_threshold(category, metric_name)

            # 防止学习到无穷大或过大的值
            if not np.isfinite(learned_threshold) or learned_threshold > default_threshold * 10:
                self.logger.warning(f"⚠️ 学习阈值不合理，使用默认值: {metric_name} "
                                  f"(learned={learned_threshold}, default={default_threshold})")
                return default_threshold

            # 应用最小阈值保护
            min_threshold = default_threshold * 0.1
            max_threshold = default_threshold * 3  # 最大不超过默认值的3倍

            final_threshold = max(min_threshold, min(learned_threshold, max_threshold))

            # 缓存学习到的阈值
            cache_key = f"{category}_{metric_name}"
            self.learned_thresholds[cache_key] = {
                'threshold': final_threshold,
                'data_points': len(valid_data),
                'percentile': percentile,
                'cv': cv,
                'learned_at': datetime.now().isoformat()
            }

            self.logger.info(f"🧠 学习阈值: {cache_key} = {final_threshold:.4f} "
                           f"(基于{len(valid_data)}个数据点, CV={cv:.2f})")

            return final_threshold

        except Exception as e:
            self.logger.error(f"动态阈值学习失败 {category}.{metric_name}: {e}")
            return self.get_threshold(category, metric_name)

    def get_type_specific_threshold(self, category: str, metric_name: str,
                                  type_value: str = None) -> float:
        """获取type字段特定的阈值（用于TiKV/PD的分类处理）"""
        try:
            # 对于有type字段的指标，使用type特定的阈值
            if type_value and category == 'other':
                # TiKV操作类型特定阈值
                if 'tikv' in metric_name and type_value in self.default_thresholds['other']['tikv']:
                    return self.default_thresholds['other']['tikv'][type_value]

                # PD Region Health类型特定阈值
                if 'pd' in metric_name and type_value in self.default_thresholds['other']['pd']:
                    return self.default_thresholds['other']['pd'][type_value]

            # 回退到普通阈值
            return self.get_threshold(category, metric_name)

        except Exception as e:
            self.logger.error(f"获取type特定阈值失败: {e}")
            return self.get_threshold(category, metric_name)

    def is_status_metric(self, metric_name: str) -> bool:
        """判断是否为状态类指标"""
        status_indicators = [
            'is_up', 'up_count', 'down_count', 'count', 'primary',
            'leader_count', 'witness_count', 'learner_count',
            'unhealth_count', 'slow_count', 'low_space_count'
        ]
        return any(indicator in metric_name for indicator in status_indicators)

    def is_binary_status_metric(self, metric_name: str) -> bool:
        """判断是否为二进制状态指标（只有0/1两个值）"""
        binary_indicators = ['is_up', 'server_is_up', 'primary']
        return any(indicator in metric_name for indicator in binary_indicators)

    def detect_status_anomaly(self, value: float, threshold: float, metric_name: str) -> bool:
        """状态类指标的专门异常检测逻辑"""
        # 🔧 修复：二进制状态指标专门处理
        if self.is_binary_status_metric(metric_name):
            # 二进制状态指标：只有严格的0或1才是有效值
            if 'is_up' in metric_name or 'server_is_up' in metric_name:
                # 只有严格等于1.0才是正常，其他值（包括0, 0.5, 1.1等）都是异常
                return not (abs(value - 1.0) < 0.001)  # 使用浮点数比较容差
            elif 'primary' in metric_name:
                return not (abs(value - 1.0) < 0.001)  # Primary状态也是二进制
            else:
                return value < threshold
        elif self.is_status_metric(metric_name):
            # 对于其他状态类指标，使用精确比较
            if 'up_count' in metric_name or 'leader_count' in metric_name:
                # 计数类：小于阈值为异常
                return value < threshold
            elif 'down_count' in metric_name or 'unhealth_count' in metric_name:
                # 异常计数类：大于阈值为异常
                return value > threshold
            else:
                # 其他状态指标：使用标准比较
                return value > threshold
        elif 'uptime' in metric_name:
            # uptime特殊处理：运行时间过短为异常
            return value < threshold
        else:
            # 非状态类指标：使用标准比较
            return value > threshold

    def detect_pod_instance_changes(self, df: pd.DataFrame, object_name: str) -> Dict[str, Any]:
        """🎯 基于分析结果的Pod实例变更检测"""
        changes = {
            'has_deletion_marker': False,
            'has_time_gaps': False,
            'has_performance_changes': False,
            'restart_count': 0,
            'gap_count': 0,
            'performance_change_ratio': 1.0,
            'anomaly_score': 0.0
        }

        try:
            if df.empty or 'time' not in df.columns:
                return changes

            # 1. 检测删除标记 (基于分析发现的(deleted)模式)
            if '(deleted)' in object_name:
                changes['has_deletion_marker'] = True
                changes['anomaly_score'] += 3.0

            # 2. 检测时间间隔异常 (基于分析发现的18个间隔异常Pod)
            if len(df) > 1:
                df_sorted = df.sort_values('time')
                time_diffs = pd.to_datetime(df_sorted['time']).diff().dt.total_seconds()
                large_gaps = time_diffs[time_diffs > 300]  # 超过5分钟

                if len(large_gaps) > 0:
                    changes['has_time_gaps'] = True
                    changes['gap_count'] = len(large_gaps)
                    changes['anomaly_score'] += min(len(large_gaps) * 0.5, 2.0)

            # 3. 检测性能突变 (基于分析发现的24个性能变化Pod)
            performance_metrics = ['rrt', 'pod_cpu_usage', 'pod_memory_working_set_bytes']
            for metric in performance_metrics:
                if metric in df.columns:
                    values = df[metric].dropna()
                    if len(values) > 5:
                        # 计算前后半段的差异
                        mid_point = len(values) // 2
                        first_half = values.iloc[:mid_point].mean()
                        second_half = values.iloc[mid_point:].mean()

                        if first_half > 0:
                            change_ratio = second_half / first_half
                            if change_ratio > 2.0 or change_ratio < 0.5:
                                changes['has_performance_changes'] = True
                                changes['performance_change_ratio'] = max(changes['performance_change_ratio'],
                                                                        max(change_ratio, 1/change_ratio))
                                changes['anomaly_score'] += 1.0

            # 4. 综合评分
            if changes['anomaly_score'] > 2.0:
                changes['severity'] = 'high'
            elif changes['anomaly_score'] > 1.0:
                changes['severity'] = 'medium'
            else:
                changes['severity'] = 'low'

        except Exception as e:
            self.logger.error(f"Pod实例变更检测失败: {e}")

        return changes

    def analyze_apm_business_anomalies(self, df: pd.DataFrame, service_name: str) -> Dict[str, Any]:
        """🎯 基于APM业务语义的异常检测"""
        anomalies = {
            'packet_loss_anomalies': [],
            'load_latency_anomalies': [],
            'error_spike_anomalies': [],
            'timeout_anomalies': [],
            'consistency_anomalies': [],
            'overall_severity': 'normal'
        }

        try:
            if df.empty:
                return anomalies

            # 1. 🚨 丢包检测 (Request vs Response)
            if 'request' in df.columns and 'response' in df.columns:
                req_data = df['request'].dropna()
                resp_data = df['response'].dropna()

                packet_loss_events = []
                for i in range(min(len(req_data), len(resp_data))):
                    req = req_data.iloc[i]
                    resp = resp_data.iloc[i]
                    if req > 0:
                        loss_ratio = max(0, (req - resp) / req)
                        if loss_ratio > 0.01:  # >1%丢包
                            packet_loss_events.append({
                                'index': i,
                                'request': req,
                                'response': resp,
                                'loss_ratio': loss_ratio,
                                'severity': 'critical' if loss_ratio > 0.1 else 'high' if loss_ratio > 0.05 else 'medium'
                            })

                if packet_loss_events:
                    max_loss = max(event['loss_ratio'] for event in packet_loss_events)
                    anomalies['packet_loss_anomalies'] = {
                        'events': packet_loss_events[:5],  # 最多保留5个事件
                        'max_loss_ratio': max_loss,
                        'event_count': len(packet_loss_events),
                        'description': f'检测到{len(packet_loss_events)}次丢包事件，最大丢包率{max_loss:.2%}'
                    }

            # 2. ⚡ 负载-延迟异常关联检测
            if 'rrt' in df.columns and 'request' in df.columns:
                rrt_data = df['rrt'].dropna()
                req_data = df['request'].dropna()

                if len(rrt_data) > 10 and len(req_data) > 10:
                    min_len = min(len(rrt_data), len(req_data))

                    # 计算高负载时的延迟增长
                    high_load_threshold = req_data.quantile(0.8)
                    high_load_mask = req_data > high_load_threshold

                    if high_load_mask.sum() > 0:
                        high_load_rrt = rrt_data[high_load_mask[:len(rrt_data)]]
                        normal_rrt = rrt_data[~high_load_mask[:len(rrt_data)]]

                        if len(high_load_rrt) > 0 and len(normal_rrt) > 0:
                            rrt_increase_ratio = high_load_rrt.mean() / normal_rrt.mean()

                            # 检测负载敏感性异常
                            if rrt_increase_ratio > 3.0:  # 高负载时延迟增长>3倍
                                anomalies['load_latency_anomalies'] = {
                                    'rrt_increase_ratio': rrt_increase_ratio,
                                    'high_load_threshold': high_load_threshold,
                                    'high_load_avg_rrt': high_load_rrt.mean(),
                                    'normal_avg_rrt': normal_rrt.mean(),
                                    'severity': 'critical' if rrt_increase_ratio > 10 else 'high',
                                    'description': f'高负载时延迟增长{rrt_increase_ratio:.1f}倍，存在性能瓶颈'
                                }

            # 3. 🔥 错误率突增检测
            if 'error_ratio' in df.columns:
                error_data = df['error_ratio'].dropna()

                error_spikes = []
                for i in range(1, len(error_data)):
                    prev_error = error_data.iloc[i-1]
                    curr_error = error_data.iloc[i]

                    # 检测错误率突增：从<1%跳到>5%，或者增长>10倍
                    if (prev_error < 0.01 and curr_error > 0.05) or (prev_error > 0 and curr_error / prev_error > 10):
                        error_spikes.append({
                            'index': i,
                            'prev_error': prev_error,
                            'curr_error': curr_error,
                            'increase_ratio': curr_error / prev_error if prev_error > 0 else float('inf'),
                            'severity': 'critical' if curr_error > 0.2 else 'high'
                        })

                if error_spikes:
                    max_error = max(spike['curr_error'] for spike in error_spikes)
                    anomalies['error_spike_anomalies'] = {
                        'spikes': error_spikes[:3],  # 最多保留3个突增事件
                        'max_error_ratio': max_error,
                        'spike_count': len(error_spikes),
                        'description': f'检测到{len(error_spikes)}次错误率突增，最高错误率{max_error:.2%}'
                    }

            # 4. ⏱️ 超时异常检测
            if 'timeout' in df.columns:
                timeout_data = df['timeout'].dropna()
                if len(timeout_data) > 0:
                    total_timeouts = timeout_data.sum()
                    max_timeout = timeout_data.max()

                    if total_timeouts > 0:
                        anomalies['timeout_anomalies'] = {
                            'total_timeouts': total_timeouts,
                            'max_timeout_per_period': max_timeout,
                            'timeout_periods': (timeout_data > 0).sum(),
                            'severity': 'high' if total_timeouts > 10 else 'medium',
                            'description': f'检测到{total_timeouts}次超时，涉及{(timeout_data > 0).sum()}个时间段'
                        }

            # 5. 📊 数据一致性检测
            consistency_issues = []

            # 检测异常的错误率值（>100%）
            if 'error_ratio' in df.columns:
                error_data = df['error_ratio'].dropna()
                invalid_errors = error_data[error_data > 1.0]
                if len(invalid_errors) > 0:
                    consistency_issues.append({
                        'type': 'invalid_error_ratio',
                        'count': len(invalid_errors),
                        'max_value': invalid_errors.max(),
                        'description': f'检测到{len(invalid_errors)}个异常错误率值(>100%)'
                    })

            # 检测Response > Request的异常情况
            if 'request' in df.columns and 'response' in df.columns:
                req_data = df['request'].dropna()
                resp_data = df['response'].dropna()

                invalid_responses = 0
                for i in range(min(len(req_data), len(resp_data))):
                    if resp_data.iloc[i] > req_data.iloc[i] * 1.1:  # Response比Request多10%以上
                        invalid_responses += 1

                if invalid_responses > 0:
                    consistency_issues.append({
                        'type': 'response_exceeds_request',
                        'count': invalid_responses,
                        'description': f'检测到{invalid_responses}个Response>Request的异常情况'
                    })

            if consistency_issues:
                anomalies['consistency_anomalies'] = consistency_issues

            # 6. 综合严重程度评估
            severity_score = 0
            if anomalies['packet_loss_anomalies']:
                severity_score += 3 if anomalies['packet_loss_anomalies']['max_loss_ratio'] > 0.1 else 2
            if anomalies['load_latency_anomalies']:
                severity_score += 3 if anomalies['load_latency_anomalies']['rrt_increase_ratio'] > 10 else 2
            if anomalies['error_spike_anomalies']:
                severity_score += 3 if anomalies['error_spike_anomalies']['max_error_ratio'] > 0.2 else 2
            if anomalies['timeout_anomalies']:
                severity_score += 2
            if anomalies['consistency_anomalies']:
                severity_score += 1

            if severity_score >= 6:
                anomalies['overall_severity'] = 'critical'
            elif severity_score >= 3:
                anomalies['overall_severity'] = 'high'
            elif severity_score >= 1:
                anomalies['overall_severity'] = 'medium'

            anomalies['severity_score'] = severity_score

        except Exception as e:
            self.logger.error(f"APM业务异常分析失败: {e}")

        return anomalies

    def _load_threshold_config(self) -> Dict[str, Any]:
        """从配置文件加载自定义阈值"""
        if not self.config_path or not os.path.exists(self.config_path):
            return {}

        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.endswith('.json'):
                    return json.load(f)
                elif self.config_path.endswith(('.yml', '.yaml')):
                    return yaml.safe_load(f)
                else:
                    self.logger.warning(f"不支持的配置文件格式: {self.config_path}")
                    return {}
        except Exception as e:
            self.logger.error(f"加载阈值配置文件失败 {self.config_path}: {e}")
            return {}

    def get_threshold(self, category: str, metric_name: str, object_name: str = None) -> float:
        """获取指标阈值 - 支持个性化配置"""
        try:
            # 1. 优先使用对象级别的个性化阈值
            if object_name and self.custom_thresholds:
                object_config = self.custom_thresholds.get('objects', {}).get(object_name, {})
                if metric_name in object_config:
                    threshold = object_config[metric_name]
                    self.logger.debug(f"使用对象级阈值: {object_name}.{metric_name} = {threshold}")
                    return threshold

            # 2. 使用类别级别的自定义阈值
            if self.custom_thresholds and category in self.custom_thresholds:
                category_config = self.custom_thresholds[category]
                if metric_name in category_config:
                    threshold = category_config[metric_name]
                    self.logger.debug(f"使用自定义阈值: {category}.{metric_name} = {threshold}")
                    return threshold

            # 3. 使用默认阈值
            if category in self.default_thresholds:
                category_config = self.default_thresholds[category]
                if metric_name in category_config:
                    threshold = category_config[metric_name]
                    self.logger.debug(f"使用默认阈值: {category}.{metric_name} = {threshold}")
                    return threshold

                # 对于other类别，需要进一步查找子类别
                if category == 'other':
                    for sub_category, sub_config in category_config.items():
                        if metric_name in sub_config:
                            threshold = sub_config[metric_name]
                            self.logger.debug(f"使用默认阈值: {category}.{sub_category}.{metric_name} = {threshold}")
                            return threshold

            # 4. 返回通用默认值
            default_value = self._get_generic_default(metric_name)
            self.logger.warning(f"未找到阈值配置，使用通用默认值: {metric_name} = {default_value}")
            return default_value

        except Exception as e:
            self.logger.error(f"获取阈值失败 {category}.{metric_name}: {e}")
            return self._get_generic_default(metric_name)

    def get_intelligent_threshold(self, category: str, metric_name: str,
                                object_name: str = None, historical_data: pd.Series = None,
                                type_value: str = None) -> Tuple[float, str, Dict[str, Any]]:
        """获取智能阈值 - 优先使用配置阈值，谨慎使用动态学习"""
        try:
            # 1. 优先使用type特定阈值
            if type_value:
                threshold = self.get_type_specific_threshold(category, metric_name, type_value)
                metadata = {
                    'source': 'type_specific',
                    'strategy': 'fixed',
                    'type_value': type_value,
                    'confidence': 0.9,
                    'data_points': 0
                }
                return threshold, 'type_specific', metadata

            # 2. 优先使用默认配置阈值
            threshold = self.get_threshold(category, metric_name, object_name)

            # 3. 仅在特定条件下使用动态学习作为参考
            if (historical_data is not None and len(historical_data) > 20 and
                threshold == self._get_generic_default(metric_name)):  # 只有在使用通用默认值时才考虑学习

                try:
                    df_temp = pd.DataFrame({metric_name: historical_data})
                    learned_threshold = self.learn_threshold_from_data(df_temp, metric_name, category)

                    # 如果学习阈值更合理，则使用学习阈值
                    if (learned_threshold != threshold and
                        0.1 * threshold <= learned_threshold <= 5 * threshold):  # 在合理范围内

                        metadata = {
                            'source': 'learned',
                            'strategy': 'dynamic',
                            'confidence': 0.7,
                            'data_points': len(historical_data),
                            'original_threshold': threshold
                        }
                        return learned_threshold, 'learned', metadata
                except Exception as e:
                    self.logger.debug(f"动态学习失败，使用默认阈值: {e}")

            # 4. 使用默认阈值
            metadata = {
                'source': 'default',
                'strategy': 'fixed',
                'confidence': 0.8,
                'data_points': 0
            }
            return threshold, 'default', metadata

        except Exception as e:
            self.logger.warning(f"智能阈值获取失败 {category}.{metric_name}: {e}")
            threshold = self.get_threshold(category, metric_name, object_name)
            metadata = {
                'source': 'fallback',
                'strategy': 'fixed',
                'confidence': 0.5,
                'data_points': 0
            }
            return threshold, 'fallback', metadata

    def _get_generic_default(self, metric_name: str) -> float:
        """获取通用默认阈值"""
        # 🔧 修复：某些指标不应该有阈值或需要特殊处理
        if 'uptime' in metric_name:
            return 86400  # uptime < 1天时可能异常重启
        elif 'server_is_up' in metric_name:
            return 0.5  # 服务状态，期望值为1，低于0.5认为异常
        elif 'error' in metric_name or 'ratio' in metric_name:
            return 0.01  # 1%
        elif 'cpu' in metric_name and 'usage' in metric_name:
            return 80.0  # 80%

        # 🔧 修复：内存相关指标的合理阈值
        elif 'memory' in metric_name:
            if 'MemTotal' in metric_name or 'MemAvailable' in metric_name:
                if 'MemTotal' in metric_name:
                    return 64*1024*1024*1024  # 总内存 > 64GB异常
                else:
                    return 1*1024*1024*1024   # 可用内存 < 1GB告警
            elif 'usage' in metric_name:
                return 85.0  # 内存使用率 > 85%
            else:
                return 2*1024*1024*1024  # 其他内存指标 > 2GB

        # 🔧 修复：文件系统相关指标的合理阈值
        elif 'filesystem' in metric_name:
            if 'size' in metric_name:
                return 100*1024*1024*1024  # 文件系统大小 > 100GB异常
            elif 'free' in metric_name:
                return 1*1024*1024*1024    # 剩余空间 < 1GB告警
            else:
                return 90.0  # 文件系统使用率 > 90%

        # 🔧 修复：网络相关指标的合理阈值
        elif 'network' in metric_name and 'bytes' in metric_name:
            return 10*1024*1024  # 网络流量 > 10MB
        elif 'network' in metric_name and 'packets' in metric_name:
            return 10000  # 网络包数 > 10K

        # 🔧 修复：磁盘I/O相关指标的合理阈值
        elif 'disk' in metric_name and 'bytes' in metric_name:
            return 100*1024*1024  # 磁盘I/O > 100MB
        elif 'disk' in metric_name and 'time' in metric_name:
            return 10.0  # 磁盘I/O时间 > 10秒

        elif 'cache' in metric_name and 'size' in metric_name:
            return 1*1024*1024*1024  # 1GB字节值
        elif 'rrt' in metric_name or 'duration' in metric_name:
            return 1000000  # 1秒 = 1,000,000微秒
        elif 'qps' in metric_name:
            return 10000  # 🔧 修复：QPS阈值提高到1万

        # 🔧 修复：请求/响应类指标阈值过低问题
        elif 'request' in metric_name or 'response' in metric_name:
            return 10000.0  # 请求/响应数量阈值提高到1万

        # 🔧 修复：文件系统I/O类指标阈值 - 调整为更合理的值
        elif 'fs_writes_bytes' in metric_name or 'fs_reads_bytes' in metric_name:
            return 100*1024  # 文件系统读写字节阈值调整为100KB (更敏感)
        elif 'fs_writes' in metric_name or 'fs_reads' in metric_name:
            return 50*1024  # 文件系统读写阈值调整为50KB (更敏感)

        # 🔧 修复：进程相关指标阈值过低问题
        elif 'processes' in metric_name or 'process' in metric_name:
            return 1000  # 进程数量阈值提高到1000

        # 🔧 修复：TCP连接相关指标阈值过低问题
        elif 'tcp' in metric_name and 'inuse' in metric_name:
            return 10000  # TCP连接数阈值提高到1万

        else:
            return 5000.0  # 🔧 修复：通用默认值从100提高到5000

    def calculate_dynamic_threshold(self, data: pd.Series, metric_name: str,
                                  method: str = 'percentile') -> float:
        """简化的动态阈值计算"""
        try:
            if data.empty or len(data) < 5:
                return self._get_generic_default(metric_name)

            clean_data = data.dropna()
            if len(clean_data) < 3:
                return self._get_generic_default(metric_name)

            # 使用95分位数作为阈值
            threshold = clean_data.quantile(0.95)

            # 最小阈值保护
            min_threshold = self._get_generic_default(metric_name) * 0.1
            return max(threshold, min_threshold)

        except Exception as e:
            self.logger.error(f"计算动态阈值失败 {metric_name}: {e}")
            return self._get_generic_default(metric_name)




class SimpleMetricAgent(BaseAIOpsAgent):
    """优化的Metric分析智能体 - 基于实际数据校准阈值"""

    def __init__(self, enable_llm: bool = True, data_root_path: str = "/data/phaseone_data",
                 threshold_config_path: str = None, baseline_config_path: str = None,
                 strategy_config_path: str = None):
        super().__init__("Optimized metric analysis agent", enable_llm)

        self.data_root = data_root_path
        self.file_locator = FilePathLocator(data_root_path)

        # 🔧 新增：阈值策略管理器
        self.strategy_manager = ThresholdStrategyManager(strategy_config_path)

        # 🔧 使用优化的阈值管理器
        self.threshold_manager = OptimizedThresholdManager(threshold_config_path)

        # 🔧 新增：APM服务基线管理器
        self.baseline_manager = APMServiceBaselineManager(baseline_config_path)

        # 数据格式验证器 - 暂时注释掉，因为DataFormatValidator类未定义
        # self.data_validator = DataFormatValidator()

        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("✅ 初始化OptimizedMetricAgent，集成阈值策略管理、基线管理和动态阈值")

        # 错误处理配置
        self.max_retries = 3
        self.timeout_seconds = 30

    def _safe_read_parquet(self, file_path: str, max_retries: int = 3) -> Optional[pd.DataFrame]:
        """安全读取parquet文件，带重试机制"""
        for attempt in range(max_retries):
            try:
                if not os.path.exists(file_path):
                    self.logger.warning(f"⚠️ 文件不存在: {file_path}")
                    return None

                # 检查文件大小
                file_size = os.path.getsize(file_path)
                if file_size == 0:
                    self.logger.warning(f"⚠️ 文件为空: {file_path}")
                    return None

                # 读取文件
                df = pd.read_parquet(file_path)

                # 验证数据格式
                if df.empty:
                    self.logger.warning(f"⚠️ 文件内容为空: {file_path}")
                    return None

                # 检查必要字段
                required_fields = ['time']
                missing_fields = [field for field in required_fields if field not in df.columns]
                if missing_fields:
                    self.logger.warning(f"⚠️ 缺少必要字段 {missing_fields}: {file_path}")
                    # 不返回None，因为某些文件可能确实没有time字段

                self.logger.debug(f"✅ 成功读取文件: {file_path} ({len(df)} 行)")
                return df

            except pd.errors.ParserError as e:
                self.logger.error(f"❌ 解析parquet文件失败 (尝试 {attempt+1}/{max_retries}): {file_path} - {e}")
                if attempt == max_retries - 1:
                    return None

            except MemoryError as e:
                self.logger.error(f"❌ 内存不足，无法读取文件: {file_path} - {e}")
                return None

            except Exception as e:
                self.logger.error(f"❌ 读取文件失败 (尝试 {attempt+1}/{max_retries}): {file_path} - {e}")
                if attempt == max_retries - 1:
                    return None

        return None

    def _validate_dataframe(self, df: pd.DataFrame, expected_columns: List[str] = None) -> bool:
        """验证DataFrame的数据质量"""
        try:
            if df is None or df.empty:
                return False

            # 检查数据类型
            if not isinstance(df, pd.DataFrame):
                self.logger.error(f"❌ 数据类型错误，期望DataFrame，实际: {type(df)}")
                return False

            # 检查期望的列
            if expected_columns:
                missing_columns = [col for col in expected_columns if col not in df.columns]
                if missing_columns:
                    self.logger.warning(f"⚠️ 缺少期望的列: {missing_columns}")
                    # 不返回False，因为某些列可能是可选的

            # 检查数据量
            if len(df) == 0:
                self.logger.warning("⚠️ DataFrame为空")
                return False

            # 检查是否全部为NaN
            if df.isnull().all().all():
                self.logger.warning("⚠️ DataFrame全部为NaN值")
                return False

            return True

        except Exception as e:
            self.logger.error(f"❌ 验证DataFrame失败: {e}")
            return False

    def create_suspicious_entity(self, category: str, object_type: str, object_name: str,
                               metric_name: str, anomaly: Dict[str, Any],
                               df: pd.DataFrame, request: AnalysisRequest,
                               type_value: str = None) -> Optional[SuspiciousEntity]:
        """增强版可疑实体创建接口 - 支持type字段分类处理"""
        try:
            # 1. 增强的原始数据采样策略
            raw_data_evidence = self._extract_enhanced_evidence(df, metric_name, anomaly, category, object_name)

            # 2. 时间序列分析
            time_series_analysis = self._analyze_time_series(df, metric_name, anomaly)

            # 3. 业务影响评估
            business_impact = self._assess_business_impact(category, object_type, object_name, metric_name, anomaly)

            # 4. 历史对比分析
            historical_comparison = self._compare_with_historical_data(df, metric_name, anomaly)

            # 5. 🎯 Pod实例变更检测 (基于分析结果新增)
            pod_changes = {}
            if object_type == 'pod' or 'pod' in object_name.lower():
                pod_changes = self.detect_pod_instance_changes(df, object_name)
                self.logger.info(f"Pod变更检测结果: {pod_changes}")

            # 🎯 新增：APM业务语义异常分析
            apm_business_anomalies = {}
            self.logger.info(f"🔍 APM业务异常分析调用检查: category={category}, object_name={object_name}, df_shape={df.shape if df is not None else 'None'}")

            if category == 'apm':
                service_name = object_name.split('-')[0] if '-' in object_name else object_name
                self.logger.info(f"🎯 开始APM业务异常分析: service_name={service_name}, df_columns={list(df.columns) if df is not None else 'None'}")
                apm_business_anomalies = self.analyze_apm_business_anomalies(df, service_name)
                self.logger.info(f"🎯 APM业务异常分析结果: severity={apm_business_anomalies.get('overall_severity', 'normal')}, score={apm_business_anomalies.get('severity_score', 0)}, types={list(apm_business_anomalies.keys())}")
            else:
                self.logger.info(f"⚠️ 跳过APM业务异常分析: category={category} != 'apm'")

            # 6. 差异化严重程度计算 (集成Pod变更和APM业务异常信息)
            enhanced_severity = self._calculate_enhanced_severity(category, metric_name, anomaly, time_series_analysis, pod_changes, apm_business_anomalies)

            # 7. 确定实体信息
            entity_info = self._determine_entity_info(category, object_type, object_name)

            # 8. 计算置信度 (考虑Pod变更和APM业务异常因素)
            confidence = self._calculate_confidence(category, enhanced_severity, time_series_analysis, len(raw_data_evidence), pod_changes, apm_business_anomalies)

            entity = SuspiciousEntity(
                entity=entity_info,
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"{metric_name}_{category}_anomaly",
                    confidence=confidence,
                    metric_value=anomaly['value'],
                    threshold=anomaly['threshold'],
                    metric_type=metric_name,
                    severity=enhanced_severity['level']
                ),
                data_location=DataLocation(
                    file_type=category,
                    root_dir="",
                    file_paths=[],
                    file_matching_rule=f"{category}_{object_type}_{metric_name}"
                ),
                confidence=confidence,
                raw_data_records=raw_data_evidence,
                data_summary={
                    # 基础分类信息 - 供ReasoningAgent分类使用
                    "metric_category": category,  # apm/infra/other (TiDB归入infra)
                    "metric_subcategory": self._get_metric_subcategory(category, metric_name),
                    "object_type": object_type,
                    "object_name": object_name,
                    "metric_name": metric_name,
                    "metric_display_name": self._get_metric_display_name(metric_name),
                    "metric_unit": self._get_metric_unit(metric_name),

                    # 🔧 新增：type字段分类信息
                    "type_value": type_value,  # TiKV/PD的操作类型或异常类型
                    "has_type_classification": type_value is not None,
                    "type_specific_threshold": anomaly.get('type_specific_threshold', False),

                    # 异常数值信息
                    "current_value": anomaly['value'],
                    "threshold": anomaly['threshold'],
                    "deviation_ratio": anomaly['value'] / anomaly['threshold'] if anomaly['threshold'] > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": anomaly['severity'],

                    # 业务上下文信息
                    "service_criticality": self._get_service_criticality(object_name),
                    "metric_impact_type": self._get_metric_impact_type(category, metric_name),
                    "affected_layer": self._get_affected_layer(category),

                    # 统计分析信息
                    "statistical_summary": {
                        "p95_value": anomaly.get('p95_value', anomaly['value']),
                        "max_value": anomaly.get('max_value', anomaly['value']),
                        "mean_value": anomaly.get('mean_value', 0),
                        "std_value": anomaly.get('std_value', 0),
                        "total_data_points": len(df),
                        "anomaly_data_points": anomaly.get('anomaly_count', 0)
                    },

                    # 🔧 新增：时间分析信息
                    "temporal_analysis": self._analyze_temporal_patterns(df, metric_name, anomaly),

                    # 🎯 新增：基于分析结果的Pod变更检测信息
                    "pod_instance_changes": pod_changes if pod_changes else {},
                    "has_pod_anomalies": bool(pod_changes.get('anomaly_score', 0) > 1.0),

                    # 🎯 新增：基于异常敏感性分析的指标特征
                    "metric_sensitivity": self._get_metric_sensitivity(category, metric_name, type_value),
                    "anomaly_detection_confidence": self._calculate_anomaly_detection_confidence(category, metric_name, anomaly),

                    # 🎯 新增：基于分析结果的优化建议
                    "optimization_suggestions": self._generate_optimization_suggestions(category, metric_name, anomaly, pod_changes),

                    # 🎯 新增：APM业务语义异常分析结果
                    "apm_business_anomalies": apm_business_anomalies if apm_business_anomalies else {},
                    "has_packet_loss": bool(apm_business_anomalies.get('packet_loss_anomalies')),
                    "has_load_latency_issues": bool(apm_business_anomalies.get('load_latency_anomalies')),
                    "has_error_spikes": bool(apm_business_anomalies.get('error_spike_anomalies')),
                    "apm_severity_score": apm_business_anomalies.get('severity_score', 0)
                }
            )

            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建{category}可疑实体失败: {e}")
            return None

    def _get_metric_sensitivity(self, category: str, metric_name: str, type_value: str = None) -> Dict[str, Any]:
        """🎯 基于分析结果获取指标敏感性信息"""
        # 基于全维度分析的敏感性数据
        sensitivity_data = {
            # APM高敏感性指标 (基于CV分析)
            'apm': {
                'rrt': {'cv': 4.47, 'sensitivity': 'high', 'rank': 1},
                'error_ratio': {'cv': 3.80, 'sensitivity': 'high', 'rank': 2},
                'request': {'cv': 1.2, 'sensitivity': 'medium', 'rank': 3},
                'response': {'cv': 1.2, 'sensitivity': 'medium', 'rank': 3}
            },
            # Infra高敏感性指标
            'infra': {
                'pod_fs_writes_bytes': {'cv': 17.80, 'sensitivity': 'extreme', 'rank': 1},
                'pod_cpu_usage': {'cv': 4.51, 'sensitivity': 'high', 'rank': 2},
                'pod_memory_working_set_bytes': {'cv': 3.33, 'sensitivity': 'high', 'rank': 3},
                'pod_network_transmit_bytes': {'cv': 2.81, 'sensitivity': 'high', 'rank': 4},
                'pod_network_receive_bytes': {'cv': 2.50, 'sensitivity': 'high', 'rank': 5}
            },
            # TiKV操作类型敏感性
            'tikv_operations': {
                'acquire_pessimistic_lock': {'cv': 6.75, 'sensitivity': 'extreme', 'rank': 1},
                'prewrite': {'cv': 3.67, 'sensitivity': 'high', 'rank': 2},
                'scan_lock': {'cv': 3.02, 'sensitivity': 'high', 'rank': 3}
            }
        }

        # 获取指标敏感性
        if category in sensitivity_data:
            metric_info = sensitivity_data[category].get(metric_name, {})
        elif type_value and type_value in sensitivity_data.get('tikv_operations', {}):
            metric_info = sensitivity_data['tikv_operations'][type_value]
        else:
            metric_info = {'cv': 1.0, 'sensitivity': 'low', 'rank': 10}

        return {
            'coefficient_of_variation': metric_info.get('cv', 1.0),
            'sensitivity_level': metric_info.get('sensitivity', 'low'),
            'sensitivity_rank': metric_info.get('rank', 10),
            'is_high_sensitivity': metric_info.get('sensitivity') in ['high', 'extreme']
        }

    def _calculate_anomaly_detection_confidence(self, category: str, metric_name: str, anomaly: Dict[str, Any]) -> float:
        """🎯 基于敏感性分析计算异常检测置信度"""
        base_confidence = 0.5

        # 基于指标敏感性调整
        sensitivity = self._get_metric_sensitivity(category, metric_name)
        if sensitivity['sensitivity_level'] == 'extreme':
            base_confidence += 0.3
        elif sensitivity['sensitivity_level'] == 'high':
            base_confidence += 0.2
        elif sensitivity['sensitivity_level'] == 'medium':
            base_confidence += 0.1

        # 基于偏差程度调整
        deviation_ratio = anomaly['value'] / anomaly['threshold'] if anomaly['threshold'] > 0 else 1
        if deviation_ratio > 10:
            base_confidence += 0.2
        elif deviation_ratio > 5:
            base_confidence += 0.15
        elif deviation_ratio > 2:
            base_confidence += 0.1

        return min(base_confidence, 1.0)

    def _generate_optimization_suggestions(self, category: str, metric_name: str,
                                         anomaly: Dict[str, Any], pod_changes: Dict[str, Any]) -> List[str]:
        """🎯 基于分析结果生成优化建议"""
        suggestions = []

        # APM层建议
        if category == 'apm':
            if metric_name == 'rrt':
                suggestions.append("检查服务依赖链路，重点关注ProductCatalogService")
                suggestions.append("分析是否存在数据库连接池问题")
            elif metric_name == 'error_ratio':
                suggestions.append("检查服务健康检查配置")
                suggestions.append("分析错误日志确定具体错误类型")

        # Pod变更相关建议
        if pod_changes.get('has_deletion_marker'):
            suggestions.append("检测到Pod重启，建议检查Pod资源限制和健康检查")
        if pod_changes.get('has_time_gaps'):
            suggestions.append("检测到数据中断，建议检查网络连接和监控代理")
        if pod_changes.get('has_performance_changes'):
            suggestions.append("检测到性能突变，建议检查资源分配和负载变化")

        # 基于敏感性的建议
        sensitivity = self._get_metric_sensitivity(category, metric_name)
        if sensitivity['sensitivity_level'] == 'extreme':
            suggestions.append("该指标极度敏感，建议立即深入调查")

        return suggestions

    def _calculate_confidence(self, category: str, enhanced_severity: Dict[str, Any],
                            time_series_analysis: Dict[str, Any], data_points: int,
                            pod_changes: Dict[str, Any] = None,
                            apm_business_anomalies: Dict[str, Any] = None) -> float:
        """🎯 基于多因素的置信度计算"""
        base_confidence = 0.5

        # 1. 基于严重程度
        severity_score = enhanced_severity.get('score', 0.5)
        base_confidence += severity_score * 0.3

        # 2. 基于数据点数量
        if data_points > 100:
            base_confidence += 0.1
        elif data_points > 50:
            base_confidence += 0.05

        # 3. 基于时间序列分析
        if time_series_analysis.get('trend_direction') == 'increasing':
            base_confidence += 0.1

        # 4. 基于Pod变更检测
        if pod_changes:
            pod_anomaly_score = pod_changes.get('anomaly_score', 0)
            if pod_anomaly_score > 2.0:
                base_confidence += 0.15
            elif pod_anomaly_score > 1.0:
                base_confidence += 0.1

        # 5. 基于APM业务异常
        if apm_business_anomalies:
            apm_confidence_boost = 0.0

            # 丢包异常提升置信度
            if apm_business_anomalies.get('packet_loss_anomalies'):
                loss_ratio = apm_business_anomalies['packet_loss_anomalies']['max_loss_ratio']
                if loss_ratio > 0.1:
                    apm_confidence_boost += 0.2  # 严重丢包，高置信度
                elif loss_ratio > 0.05:
                    amp_confidence_boost += 0.15
                else:
                    apm_confidence_boost += 0.1

            # 负载-延迟异常提升置信度
            if apm_business_anomalies.get('load_latency_anomalies'):
                rrt_ratio = apm_business_anomalies['load_latency_anomalies']['rrt_increase_ratio']
                if rrt_ratio > 10:
                    apm_confidence_boost += 0.2  # 严重性能瓶颈，高置信度
                elif rrt_ratio > 5:
                    apm_confidence_boost += 0.15
                else:
                    apm_confidence_boost += 0.1

            # 错误率突增提升置信度
            if apm_business_anomalies.get('error_spike_anomalies'):
                apm_confidence_boost += 0.15  # 错误突增是明确的异常信号

            # 超时和一致性异常
            if apm_business_anomalies.get('timeout_anomalies'):
                apm_confidence_boost += 0.1
            if apm_business_anomalies.get('consistency_anomalies'):
                apm_confidence_boost += 0.05

            base_confidence += amp_confidence_boost

        # 6. 基于指标敏感性
        sensitivity = self._get_metric_sensitivity(category, 'unknown')  # 简化版本
        if sensitivity.get('sensitivity_level') == 'extreme':
            base_confidence += 0.1
        elif sensitivity.get('sensitivity_level') == 'high':
            base_confidence += 0.05

        return min(base_confidence, 1.0)

    def _get_metric_subcategory(self, category: str, metric_name: str) -> str:
        """获取metric子分类，供ReasoningAgent进行细分分析"""
        if category == 'apm':
            if metric_name in ['error_ratio', 'client_error_ratio', 'server_error_ratio', 'error', 'client_error', 'server_error']:
                return 'error_metrics'
            elif metric_name in ['rrt', 'rrt_max']:
                return 'latency_metrics'
            elif metric_name in ['request', 'response']:
                return 'throughput_metrics'
            elif metric_name == 'timeout':
                return 'timeout_metrics'
            else:
                return 'other_apm_metrics'

        elif category == 'infra':
            if 'cpu' in metric_name:
                return 'cpu_metrics'
            elif 'memory' in metric_name:
                return 'memory_metrics'
            elif 'disk' in metric_name or 'fs' in metric_name or 'filesystem' in metric_name:
                return 'storage_metrics'
            elif 'network' in metric_name:
                return 'network_metrics'
            elif 'process' in metric_name:
                return 'process_metrics'
            else:
                return 'other_infra_metrics'

        elif category == 'tidb':
            if metric_name in ['qps', 'failed_query_ops', 'slow_query']:
                return 'query_performance_metrics'
            elif metric_name in ['duration_99th', 'duration_95th', 'duration_avg']:
                return 'query_latency_metrics'
            elif metric_name == 'connection_count':
                return 'connection_metrics'
            elif metric_name == 'block_cache_size':
                return 'cache_metrics'
            else:
                return 'other_tidb_metrics'

        elif category == 'other':
            return f'{metric_name}_metrics'

        return 'unknown_metrics'

    def _get_metric_display_name(self, metric_name: str) -> str:
        """获取metric的中文显示名称"""
        display_names = {
            # APM指标
            'request': '请求数量',
            'response': '响应数量',
            'rrt': '平均响应时间',
            'rrt_max': '最大响应时间',
            'error': '异常数量',
            'client_error': '客户端异常',
            'server_error': '服务端异常',
            'timeout': '超时数量',
            'error_ratio': '异常比例',
            'client_error_ratio': '客户端异常比例',
            'server_error_ratio': '服务端异常比例',

            # Infra指标
            'pod_cpu_usage': 'Pod CPU使用率',
            'pod_memory_working_set_bytes': 'Pod内存使用量',
            'pod_processes': 'Pod进程数',
            'pod_fs_writes_bytes': 'Pod文件系统写入字节',
            'pod_fs_reads_bytes': 'Pod文件系统读取字节',
            'pod_network_receive_bytes': 'Pod网络接收字节',
            'pod_network_transmit_bytes': 'Pod网络发送字节',
            'node_cpu_usage_rate': 'Node CPU使用率',
            'node_memory_usage_rate': 'Node内存使用率',
            'node_filesystem_usage_rate': 'Node磁盘使用率',
            'node_memory_MemAvailable_bytes': 'Node可用内存',
            'node_disk_read_bytes_total': 'Node磁盘读取字节',
            'node_disk_written_bytes_total': 'Node磁盘写入字节',

            # TiDB指标
            'connection_count': '连接数',
            'failed_query_ops': '失败查询数',
            'duration_99th': '99分位查询延迟',
            'duration_95th': '95分位查询延迟',
            'duration_avg': '平均查询延迟',
            'qps': '每秒查询数',
            'slow_query': '慢查询数',
            'block_cache_size': 'Block Cache大小',

            # Other指标
            'abnormal_region_count': '异常Region数量',
            'leader_count': 'Leader数量',
            'available_size': '可用存储大小'
        }
        return display_names.get(metric_name, metric_name)

    def _get_pd_region_display_name(self, anomaly: Dict[str, Any], metric_name: str) -> str:
        """🔧 新增：获取PD Region异常和TiKV操作异常的特殊显示名称"""
        if 'region_type' in anomaly:
            # PD Region异常
            region_type = anomaly['region_type']
            region_type_names = {
                'miss-peer-region-count': '缺失副本Region数量',
                'undersized-region-count': '副本不足Region数量',
                'empty-region-count': '空Region数量',
                'down-peer-region-count': '下线副本Region数量',
                'pending-peer-region-count': '待处理副本Region数量',
                'offline-peer-region-count': '离线副本Region数量',
                'extra-peer-region-count': '多余副本Region数量',
                'oversized-region-count': '副本过多Region数量',
                'learner-peer-region-count': '学习者副本Region数量',
                'witness-leader-region-count': '见证者Leader Region数量'
            }
            return region_type_names.get(region_type, region_type)
        elif 'operation_type' in anomaly:
            # TiKV操作异常
            op_type = anomaly['operation_type']
            op_type_names = {
                'kv_get': 'KV Get操作QPS',
                'kv_batch_get': 'KV BatchGet操作QPS',
                'kv_prewrite': 'KV Prewrite操作QPS',
                'kv_commit': 'KV Commit操作QPS',
                'kv_scan': 'KV Scan操作QPS',
                'batch_get': 'BatchGet操作QPS',
                'get': 'Get操作QPS',
                'scan': 'Scan操作QPS',
                'prewrite': 'Prewrite操作QPS',
                'commit': 'Commit操作QPS',
                'acquire_pessimistic_lock': '悲观锁获取QPS'
            }
            return op_type_names.get(op_type, f"{op_type}操作QPS")
        elif 'sql_operation_type' in anomaly:
            # TiDB SQL操作异常
            sql_type = anomaly['sql_operation_type']
            sql_type_names = {
                'Select': 'Select查询QPS',
                'Use': 'Use数据库QPS',
                'Insert': 'Insert插入QPS',
                'Update': 'Update更新QPS',
                'Delete': 'Delete删除QPS',
                'Begin': 'Begin事务QPS',
                'Commit': 'Commit提交QPS',
                'Rollback': 'Rollback回滚QPS',
                'Set': 'Set设置QPS',
                'Show': 'Show查看QPS',
                'Explain': 'Explain解释QPS'
            }
            return sql_type_names.get(sql_type, f"{sql_type}操作QPS")
        else:
            return self._get_metric_display_name(metric_name)

    def _get_metric_unit(self, metric_name: str) -> str:
        """获取metric的单位 - 使用UnitNormalizer统一管理"""
        return UnitNormalizer.get_standard_unit(metric_name)

    def _get_service_criticality(self, object_name: str) -> str:
        """获取服务的关键程度"""
        service_name = object_name
        if '-' in object_name:
            service_name = object_name.rsplit('-', 1)[0]

        critical_services = ['frontend', 'checkoutservice', 'paymentservice']
        important_services = ['productcatalogservice', 'cartservice', 'recommendationservice']

        if service_name in critical_services:
            return 'critical'
        elif service_name in important_services:
            return 'important'
        else:
            return 'normal'

    def _get_metric_impact_type(self, category: str, metric_name: str) -> str:
        """获取metric的影响类型"""
        if category == 'apm':
            if 'error' in metric_name:
                return 'user_experience'
            elif 'rrt' in metric_name:
                return 'performance'
            else:
                return 'availability'
        elif category == 'infra':
            if 'cpu' in metric_name or 'memory' in metric_name:
                return 'resource_capacity'
            elif 'network' in metric_name:
                return 'connectivity'
            elif 'disk' in metric_name or 'fs' in metric_name:
                return 'storage_performance'
            else:
                return 'system_stability'
        elif category in ['tidb', 'other']:
            return 'data_layer'
        else:
            return 'unknown'

    def _get_affected_layer(self, category: str) -> str:
        """获取受影响的系统层级"""
        layer_mapping = {
            'apm': 'application_layer',
            'infra': 'infrastructure_layer',
            'tidb': 'data_layer',
            'other': 'cluster_layer'
        }
        return layer_mapping.get(category, 'unknown_layer')

    def _extract_enhanced_evidence(self, df: pd.DataFrame, metric_name: str,
                                 anomaly: Dict[str, Any], category: str, object_name: str) -> List[Dict[str, Any]]:
        """增强的原始数据采样策略 - 提供更有代表性的异常数据样本"""
        evidence = []

        try:
            # 确定数据字段
            data_field = metric_name
            if metric_name not in df.columns:
                # 对于infra数据，字段名可能是动态的
                possible_fields = [metric_name, 'value', f'{metric_name}_value']
                for field in possible_fields:
                    if field in df.columns:
                        data_field = field
                        break
                else:
                    return evidence

            # 过滤有效数据
            valid_data = df[df[data_field].notna()].copy()
            if valid_data.empty:
                return evidence

            threshold = anomaly['threshold']
            anomaly_data = valid_data[valid_data[data_field] > threshold]

            if anomaly_data.empty:
                return evidence

            # 智能采样策略：
            # 1. 最高值样本
            max_record = anomaly_data.loc[anomaly_data[data_field].idxmax()]
            evidence.append(self._create_evidence_record(max_record, data_field, category, object_name, "peak_value"))

            # 2. 时间分布采样 - 选择不同时间段的代表性样本
            if len(anomaly_data) > 1:
                # 按时间排序
                if 'time' in anomaly_data.columns:
                    anomaly_data_sorted = anomaly_data.sort_values('time')

                    # 选择首次异常
                    first_anomaly = anomaly_data_sorted.iloc[0]
                    if first_anomaly.name != max_record.name:
                        evidence.append(self._create_evidence_record(first_anomaly, data_field, category, object_name, "first_occurrence"))

                    # 选择最近异常
                    if len(anomaly_data_sorted) > 2:
                        last_anomaly = anomaly_data_sorted.iloc[-1]
                        if last_anomaly.name not in [max_record.name, first_anomaly.name]:
                            evidence.append(self._create_evidence_record(last_anomaly, data_field, category, object_name, "recent_occurrence"))

            # 3. 如果样本不足，添加中位数样本
            if len(evidence) < 3 and len(anomaly_data) > len(evidence):
                median_idx = len(anomaly_data) // 2
                median_record = anomaly_data.iloc[median_idx]
                if median_record.name not in [e.get('record_id') for e in evidence]:
                    evidence.append(self._create_evidence_record(median_record, data_field, category, object_name, "median_anomaly"))

            # 限制最多5个样本
            return evidence[:5]

        except Exception as e:
            self.logger.error(f"❌ 提取增强证据失败: {e}")
            return evidence

    def _create_evidence_record(self, record: pd.Series, data_field: str,
                              category: str, object_name: str, sample_type: str) -> Dict[str, Any]:
        """创建证据记录"""
        evidence_record = {
            'record_id': record.name,
            'sample_type': sample_type,
            'metric': data_field,
            'value': record.get(data_field, 0),
            'timestamp': record.get('time', record.get('@timestamp', 'unknown')),
            'object_name': object_name,
            'category': category
        }

        # 根据类别添加特定字段
        if category == 'apm':
            evidence_record.update({
                'object_id': record.get('object_id', object_name),
                'object_type': record.get('object_type', 'unknown'),
                'request_count': record.get('request', 0),
                'response_count': record.get('response', 0)
            })
        elif category in ['infra', 'tidb', 'other']:
            evidence_record.update({
                'instance': record.get('instance', 'unknown'),
                'pod': record.get('pod', object_name),
                'namespace': record.get('namespace', 'unknown'),
                'kpi_key': record.get('kpi_key', data_field),
                'kpi_name': record.get('kpi_name', data_field)
            })

        return evidence_record

    def _analyze_time_series(self, df: pd.DataFrame, metric_name: str, anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """时间序列分析 - 分析异常的时间特征"""
        analysis = {
            "anomaly_duration": "unknown",
            "trend_direction": "unknown",
            "frequency_pattern": "unknown",
            "peak_time": "unknown",
            "anomaly_start_time": "unknown",
            "anomaly_end_time": "unknown"
        }

        try:
            # 确定数据字段
            data_field = metric_name
            if metric_name not in df.columns:
                possible_fields = [metric_name, 'value', f'{metric_name}_value']
                for field in possible_fields:
                    if field in df.columns:
                        data_field = field
                        break
                else:
                    return analysis

            # 过滤有效数据并按时间排序
            if 'time' not in df.columns:
                return analysis

            valid_data = df[df[data_field].notna()].copy()
            if valid_data.empty:
                return analysis

            valid_data['time'] = pd.to_datetime(valid_data['time'])
            valid_data = valid_data.sort_values('time')

            threshold = anomaly['threshold']
            anomaly_mask = valid_data[data_field] > threshold
            anomaly_data = valid_data[anomaly_mask]

            if anomaly_data.empty:
                return analysis

            # 1. 异常持续时间分析
            anomaly_start = anomaly_data['time'].min()
            anomaly_end = anomaly_data['time'].max()
            duration = anomaly_end - anomaly_start

            analysis.update({
                "anomaly_start_time": anomaly_start.isoformat(),
                "anomaly_end_time": anomaly_end.isoformat(),
                "anomaly_duration": str(duration),
                "anomaly_duration_minutes": duration.total_seconds() / 60
            })

            # 2. 峰值时间
            peak_idx = anomaly_data[data_field].idxmax()
            peak_time = anomaly_data.loc[peak_idx, 'time']
            analysis["peak_time"] = peak_time.isoformat()

            # 3. 趋势分析
            if len(anomaly_data) >= 3:
                # 计算趋势斜率
                time_numeric = (anomaly_data['time'] - anomaly_data['time'].min()).dt.total_seconds()
                correlation = time_numeric.corr(anomaly_data[data_field])

                if correlation > 0.3:
                    analysis["trend_direction"] = "increasing"
                elif correlation < -0.3:
                    analysis["trend_direction"] = "decreasing"
                else:
                    analysis["trend_direction"] = "stable"

                analysis["trend_correlation"] = correlation

            # 4. 频率模式分析
            total_points = len(valid_data)
            anomaly_points = len(anomaly_data)
            anomaly_ratio = anomaly_points / total_points

            if anomaly_ratio > 0.8:
                analysis["frequency_pattern"] = "continuous"
            elif anomaly_ratio > 0.3:
                analysis["frequency_pattern"] = "frequent"
            elif anomaly_ratio > 0.1:
                analysis["frequency_pattern"] = "intermittent"
            else:
                analysis["frequency_pattern"] = "spike"

            analysis["anomaly_ratio"] = anomaly_ratio

            # 5. 波动性分析
            if len(anomaly_data) > 1:
                volatility = anomaly_data[data_field].std() / anomaly_data[data_field].mean()
                analysis["volatility"] = volatility

                if volatility > 0.5:
                    analysis["volatility_level"] = "high"
                elif volatility > 0.2:
                    analysis["volatility_level"] = "medium"
                else:
                    analysis["volatility_level"] = "low"

            return analysis

        except Exception as e:
            self.logger.error(f"❌ 时间序列分析失败: {e}")
            return analysis

    def _assess_business_impact(self, category: str, object_type: str, object_name: str,
                              metric_name: str, anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """业务影响评估 - 基于服务重要性和指标类型评估业务影响"""
        impact = {
            "impact_level": "medium",
            "affected_services": [],
            "user_facing": False,
            "sla_impact": "unknown",
            "description": ""
        }

        try:
            # 1. 服务重要性评估
            critical_services = ['frontend', 'checkoutservice', 'paymentservice']
            important_services = ['productcatalogservice', 'cartservice', 'recommendationservice']

            service_name = object_name
            if object_type == 'pod' and '-' in object_name:
                service_name = object_name.rsplit('-', 1)[0]

            # 2. 指标类型影响评估
            if category == 'apm':
                if metric_name in ['error_ratio', 'client_error_ratio', 'server_error_ratio']:
                    if service_name in critical_services:
                        impact["impact_level"] = "critical"
                        impact["user_facing"] = True
                        impact["description"] = f"关键服务{service_name}出现错误率异常，直接影响用户体验"
                    else:
                        impact["impact_level"] = "high"
                        impact["description"] = f"服务{service_name}错误率异常，可能影响业务功能"

                elif metric_name in ['rrt', 'rrt_max']:
                    if service_name in critical_services:
                        impact["impact_level"] = "high"
                        impact["user_facing"] = True
                        impact["description"] = f"关键服务{service_name}响应时间异常，影响用户体验"
                    else:
                        impact["impact_level"] = "medium"
                        impact["description"] = f"服务{service_name}响应时间异常，可能影响性能"

            elif category == 'infra':
                if metric_name in ['node_cpu_usage_rate', 'node_memory_usage_rate']:
                    impact["impact_level"] = "high"
                    impact["description"] = f"节点{object_name}资源使用率异常，可能影响多个服务"
                    impact["affected_services"] = ["multiple_services"]

                elif metric_name in ['pod_cpu_usage', 'pod_memory_working_set_bytes']:
                    if service_name in critical_services:
                        impact["impact_level"] = "high"
                        impact["description"] = f"关键服务Pod {object_name}资源异常"
                    else:
                        impact["impact_level"] = "medium"
                        impact["description"] = f"Pod {object_name}资源使用异常"

            elif category == 'tidb':
                impact["impact_level"] = "critical"
                impact["user_facing"] = True
                impact["description"] = f"数据库{metric_name}异常，可能影响所有依赖数据库的服务"
                impact["affected_services"] = ["productcatalogservice", "adservice"]

            elif category == 'other':
                if object_type == 'pd':
                    impact["impact_level"] = "critical"
                    impact["description"] = f"PD组件{metric_name}异常，可能影响整个TiDB集群"
                elif object_type == 'tikv':
                    impact["impact_level"] = "high"
                    impact["description"] = f"TiKV组件{metric_name}异常，可能影响数据存储"

            # 3. SLA影响评估
            deviation_ratio = anomaly['value'] / anomaly['threshold'] if anomaly['threshold'] > 0 else 1
            if deviation_ratio > 3:
                impact["sla_impact"] = "severe_breach"
            elif deviation_ratio > 2:
                impact["sla_impact"] = "moderate_breach"
            elif deviation_ratio > 1.5:
                impact["sla_impact"] = "minor_breach"
            else:
                impact["sla_impact"] = "within_tolerance"

            return impact

        except Exception as e:
            self.logger.error(f"❌ 业务影响评估失败: {e}")
            return impact

    def _compare_with_historical_data(self, df: pd.DataFrame, metric_name: str,
                                    anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """历史对比分析 - 与历史数据进行对比"""
        comparison = {
            "baseline_value": "unknown",
            "percentile_rank": "unknown",
            "deviation_from_baseline": "unknown",
            "historical_context": "insufficient_data"
        }

        try:
            # 确定数据字段
            data_field = metric_name
            if metric_name not in df.columns:
                possible_fields = [metric_name, 'value', f'{metric_name}_value']
                for field in possible_fields:
                    if field in df.columns:
                        data_field = field
                        break
                else:
                    return comparison

            valid_data = df[df[data_field].notna()]
            if len(valid_data) < 10:
                return comparison

            current_value = anomaly['value']

            # 1. 基线值计算（排除异常值的均值）
            threshold = anomaly['threshold']
            normal_data = valid_data[valid_data[data_field] <= threshold]

            if len(normal_data) > 0:
                baseline = normal_data[data_field].mean()
                comparison["baseline_value"] = baseline
                comparison["deviation_from_baseline"] = ((current_value - baseline) / baseline * 100) if baseline > 0 else 0

            # 2. 分位数排名
            percentile_rank = (valid_data[data_field] < current_value).sum() / len(valid_data) * 100
            comparison["percentile_rank"] = percentile_rank

            # 3. 历史上下文
            if percentile_rank > 99:
                comparison["historical_context"] = "极端异常，历史罕见"
            elif percentile_rank > 95:
                comparison["historical_context"] = "严重异常，历史少见"
            elif percentile_rank > 90:
                comparison["historical_context"] = "明显异常，偶尔出现"
            elif percentile_rank > 75:
                comparison["historical_context"] = "轻微异常，时有发生"
            else:
                comparison["historical_context"] = "正常范围内"

            # 4. 统计信息
            comparison.update({
                "historical_mean": valid_data[data_field].mean(),
                "historical_std": valid_data[data_field].std(),
                "historical_max": valid_data[data_field].max(),
                "historical_min": valid_data[data_field].min(),
                "data_points_count": len(valid_data)
            })

            return comparison

        except Exception as e:
            self.logger.error(f"❌ 历史对比分析失败: {e}")
            return comparison

    def _calculate_enhanced_severity(self, category: str, metric_name: str, anomaly: Dict[str, Any],
                                   time_series_analysis: Dict[str, Any], pod_changes: Dict[str, Any] = None,
                                   apm_business_anomalies: Dict[str, Any] = None) -> Dict[str, Any]:
        """差异化严重程度计算 - 针对不同指标类型的专业化评估"""
        severity_info = {
            "level": "medium",
            "score": 0.5,
            "factors": [],
            "description": ""
        }

        try:
            base_score = 0.0
            factors = []

            # 1. 基础偏离程度
            deviation_ratio = anomaly['value'] / anomaly['threshold'] if anomaly['threshold'] > 0 else 1

            # 2. 根据指标类型差异化计算
            if category == 'apm':
                if metric_name in ['error_ratio', 'client_error_ratio', 'server_error_ratio']:
                    # 错误率：任何错误都很严重
                    if deviation_ratio > 10:
                        base_score = 1.0
                        factors.append("错误率严重超标")
                    elif deviation_ratio > 5:
                        base_score = 0.9
                        factors.append("错误率明显超标")
                    elif deviation_ratio > 2:
                        base_score = 0.7
                        factors.append("错误率超标")
                    else:
                        base_score = 0.5
                        factors.append("错误率轻微超标")

                elif metric_name in ['rrt', 'rrt_max']:
                    # 响应时间：影响用户体验
                    if deviation_ratio > 5:
                        base_score = 0.9
                        factors.append("响应时间严重超标")
                    elif deviation_ratio > 3:
                        base_score = 0.8
                        factors.append("响应时间明显超标")
                    elif deviation_ratio > 2:
                        base_score = 0.6
                        factors.append("响应时间超标")
                    else:
                        base_score = 0.4
                        factors.append("响应时间轻微超标")

            elif category == 'infra':
                if 'cpu' in metric_name:
                    # CPU使用率：影响性能
                    if deviation_ratio > 1.2:  # >96%
                        base_score = 0.9
                        factors.append("CPU使用率接近饱和")
                    elif deviation_ratio > 1.1:  # >88%
                        base_score = 0.7
                        factors.append("CPU使用率过高")
                    else:
                        base_score = 0.5
                        factors.append("CPU使用率超标")

                elif 'memory' in metric_name:
                    # 内存使用：可能导致OOM
                    if deviation_ratio > 1.15:  # >97.75%
                        base_score = 1.0
                        factors.append("内存使用率危险，可能OOM")
                    elif deviation_ratio > 1.1:  # >93.5%
                        base_score = 0.8
                        factors.append("内存使用率过高")
                    else:
                        base_score = 0.6
                        factors.append("内存使用率超标")

            elif category == 'tidb':
                # 数据库指标：影响所有依赖服务
                if metric_name in ['qps', 'connection_count']:
                    base_score = min(0.8 + (deviation_ratio - 1) * 0.2, 1.0)
                    factors.append(f"数据库{metric_name}超标")
                elif metric_name in ['duration_99th', 'duration_95th']:
                    base_score = min(0.7 + (deviation_ratio - 1) * 0.3, 1.0)
                    factors.append("数据库查询延迟超标")

            elif category == 'other':
                # 集群组件：影响整体稳定性
                base_score = min(0.8 + (deviation_ratio - 1) * 0.2, 1.0)
                factors.append(f"集群组件{metric_name}异常")

            # 3. 时间因素调整
            if time_series_analysis.get("frequency_pattern") == "continuous":
                base_score = min(base_score + 0.2, 1.0)
                factors.append("异常持续发生")
            elif time_series_analysis.get("frequency_pattern") == "frequent":
                base_score = min(base_score + 0.1, 1.0)
                factors.append("异常频繁发生")

            if time_series_analysis.get("trend_direction") == "increasing":
                base_score = min(base_score + 0.1, 1.0)
                factors.append("异常趋势恶化")

            # 🎯 4. 基于分析结果的Pod变更因素
            if pod_changes:
                pod_score_adjustment = 0.0
                if pod_changes.get('has_deletion_marker'):
                    pod_score_adjustment += 0.2
                    factors.append("检测到Pod重启")
                if pod_changes.get('has_time_gaps'):
                    pod_score_adjustment += 0.1
                    factors.append("检测到数据中断")
                if pod_changes.get('has_performance_changes'):
                    pod_score_adjustment += 0.15
                    factors.append("检测到性能突变")

                base_score = min(base_score + pod_score_adjustment, 1.0)

            # 🎯 5. 基于APM业务异常的调整
            if apm_business_anomalies:
                apm_score_adjustment = 0.0

                # 丢包异常
                if apm_business_anomalies.get('packet_loss_anomalies'):
                    loss_ratio = apm_business_anomalies['packet_loss_anomalies']['max_loss_ratio']
                    if loss_ratio > 0.1:  # >10%丢包
                        apm_score_adjustment += 0.3
                        factors.append(f"严重丢包({loss_ratio:.1%})")
                    elif loss_ratio > 0.05:  # >5%丢包
                        amp_score_adjustment += 0.2
                        factors.append(f"中等丢包({loss_ratio:.1%})")
                    else:
                        apm_score_adjustment += 0.1
                        factors.append(f"轻微丢包({loss_ratio:.1%})")

                # 负载-延迟异常
                if apm_business_anomalies.get('load_latency_anomalies'):
                    rrt_ratio = apm_business_anomalies['load_latency_anomalies']['rrt_increase_ratio']
                    if rrt_ratio > 10:  # >10倍延迟增长
                        apm_score_adjustment += 0.25
                        factors.append(f"严重性能瓶颈({rrt_ratio:.1f}x延迟)")
                    elif rrt_ratio > 5:  # >5倍延迟增长
                        apm_score_adjustment += 0.15
                        factors.append(f"性能瓶颈({rrt_ratio:.1f}x延迟)")
                    else:
                        apm_score_adjustment += 0.1
                        factors.append(f"负载敏感({rrt_ratio:.1f}x延迟)")

                # 错误率突增
                if apm_business_anomalies.get('error_spike_anomalies'):
                    error_ratio = apm_business_anomalies['error_spike_anomalies']['max_error_ratio']
                    if error_ratio > 0.2:  # >20%错误率
                        apm_score_adjustment += 0.3
                        factors.append(f"严重错误突增({error_ratio:.1%})")
                    elif error_ratio > 0.1:  # >10%错误率
                        apm_score_adjustment += 0.2
                        factors.append(f"错误率突增({error_ratio:.1%})")
                    else:
                        apm_score_adjustment += 0.1
                        factors.append(f"错误率异常({error_ratio:.1%})")

                # 超时异常
                if apm_business_anomalies.get('timeout_anomalies'):
                    timeout_count = apm_business_anomalies['timeout_anomalies']['total_timeouts']
                    apm_score_adjustment += 0.1
                    factors.append(f"超时异常({timeout_count}次)")

                # 数据一致性异常
                if apm_business_anomalies.get('consistency_anomalies'):
                    apm_score_adjustment += 0.05
                    factors.append("数据一致性异常")

                base_score = min(base_score + apm_score_adjustment, 1.0)

            # 🎯 6. 基于异常敏感性分析的调整
            sensitivity = self._get_metric_sensitivity(category, metric_name)
            if sensitivity['sensitivity_level'] == 'extreme':
                base_score = min(base_score + 0.2, 1.0)
                factors.append("极高敏感性指标")
            elif sensitivity['sensitivity_level'] == 'high':
                base_score = min(base_score + 0.1, 1.0)
                factors.append("高敏感性指标")

            # 7. 确定严重程度等级
            if base_score >= 0.9:
                level = "critical"
                description = "严重异常，需要立即处理"
            elif base_score >= 0.7:
                level = "high"
                description = "高级异常，需要优先处理"
            elif base_score >= 0.5:
                level = "medium"
                description = "中等异常，需要关注"
            else:
                level = "low"
                description = "轻微异常，建议监控"

            severity_info.update({
                "level": level,
                "score": base_score,
                "factors": factors,
                "description": description,
                "deviation_ratio": deviation_ratio
            })

            return severity_info

        except Exception as e:
            self.logger.error(f"❌ 增强严重程度计算失败: {e}")
            return severity_info

    async def _handle_analysis_request(self, request: AnalysisRequest, ctx) -> AnalysisResponse:
        """处理分析请求 - BaseAIOpsAgent要求的抽象方法实现"""
        return await self.analyze(request, ctx)

    async def analyze(self, request: AnalysisRequest, ctx) -> AnalysisResponse:
        """主要分析入口 - 多层级异常检测"""
        try:
            self.logger.info(f"🚀 开始增强版Metric分析: {request.case_uuid}")
            
            # 1. 加载所有类型的metric数据
            all_metrics = await self._load_all_metrics_data(request.case_uuid, request.start_time, request.end_time)
            
            total_records = sum(len(data) for category in all_metrics.values() for data in category.values())
            if total_records == 0:
                self.logger.warning("⚠️ 未找到任何metric数据")
                return self._create_empty_response(request)
            
            self.logger.info(f"📊 加载metric数据: {total_records} 条记录")
            
            # 2. 分层级检测异常
            all_suspicious_entities = []
            
            # 2.1 APM异常检测 (Service/Pod级别)
            apm_entities = await self._detect_apm_anomalies(all_metrics.get('apm', {}), request)
            all_suspicious_entities.extend(apm_entities)
            self.logger.info(f"📊 APM异常检测: 发现 {len(apm_entities)} 个异常")
            
            # 2.2 基础设施异常检测 (Node/Pod级别)
            infra_data = all_metrics.get('infra', {})
            self.logger.info(f"🔍 基础设施数据检查: {len(infra_data)} 个对象")
            if len(infra_data) > 0:
                self.logger.info(f"🔍 基础设施对象列表: {list(infra_data.keys())[:5]}")

            infra_entities = await self._detect_infra_anomalies(infra_data, request)
            all_suspicious_entities.extend(infra_entities)
            self.logger.info(f"📊 Infra异常检测: 发现 {len(infra_entities)} 个异常")

            # 🔧 启用全面的Pod断点检测 - 检测文件夹内所有符合时间范围的Pod
            self.logger.info(f"🔍 开始全面Pod断点检测，扫描文件夹内所有Pod")
            breakpoint_entities = await self._force_pod_breakpoint_detection(infra_data, request)
            all_suspicious_entities.extend(breakpoint_entities)
            self.logger.info(f"📊 Pod断点检测: 发现 {len(breakpoint_entities)} 个断点异常")
            
            # 2.3 数据库异常检测 (TiDB级别)
            tidb_entities = await self._detect_tidb_anomalies(all_metrics.get('tidb', {}), request)
            all_suspicious_entities.extend(tidb_entities)
            self.logger.info(f"📊 TiDB异常检测: 发现 {len(tidb_entities)} 个异常")
            
            # 2.4 其他组件异常检测 (PD/TiKV级别)
            other_entities = await self._detect_other_anomalies(all_metrics.get('other', {}), request)
            all_suspicious_entities.extend(other_entities)
            self.logger.info(f"📊 Other异常检测: 发现 {len(other_entities)} 个异常")

            # 🔧 2.5 TiDB集群多维度异常检测
            multidim_anomalies = self._detect_tidb_cluster_multidimensional_anomalies(request)
            # 将多维度异常转换为可疑实体
            for anomaly in multidim_anomalies:
                multidim_entity = self._create_multidim_suspicious_entity(anomaly, request)
                if multidim_entity:
                    all_suspicious_entities.append(multidim_entity)
            self.logger.info(f"📊 TiDB集群多维度异常检测: 发现 {len(multidim_anomalies)} 个异常")

            # 🔧 2.5 瞬时故障模式检测 (专门针对短时间窗口)
            time_range = TimeRange(start=request.start_time, end=request.end_time)
            instantaneous_patterns = self._detect_instantaneous_fault_patterns(time_range, all_metrics)

            # 将瞬时故障模式转换为可疑实体
            instantaneous_entities = []
            for i, pattern in enumerate(instantaneous_patterns):
                self.logger.debug(f"🔍 处理瞬时故障模式 {i+1}: type={pattern.get('type', 'unknown')}, description={pattern.get('description', 'N/A')}")
                entity = self._create_instantaneous_fault_entity(pattern, request)
                if entity:
                    instantaneous_entities.append(entity)
                    self.logger.info(f"✅ 成功创建瞬时故障实体: {entity.entity.service}:{entity.entity.pod}")
                else:
                    self.logger.warning(f"⚠️ 瞬时故障模式转换失败: {pattern}")

            all_suspicious_entities.extend(instantaneous_entities)
            self.logger.info(f"📊 瞬时故障检测: 发现 {len(instantaneous_entities)} 个瞬时故障模式")

            # 3. 生成分析响应
            results = {
                "analysis_summary": f"多层级异常检测完成: 发现 {len(all_suspicious_entities)} 个异常对象",
                "apm_anomalies": len(apm_entities),
                "infra_anomalies": len(infra_entities),
                "tidb_anomalies": len(tidb_entities),
                "other_anomalies": len(other_entities),
                "total_anomalies": len(all_suspicious_entities),
                "detection_method": "multi_level_threshold_based",
                "suspicious_entities": all_suspicious_entities,
                "standardized_output": {
                    "suspicious_entities": all_suspicious_entities,
                    "analysis_summary": f"多层级异常检测完成: 发现 {len(all_suspicious_entities)} 个异常对象"
                }
            }
            
            response = AnalysisResponse(
                case_uuid=request.case_uuid,
                start_time=request.start_time,
                end_time=request.end_time,
                data=request.data,
                analysis_type="metrics",
                results=results,
                success=True
            )
            
            # 🔧 调试：检查all_suspicious_entities中的emailservice实体
            emailservice_entities = []
            for i, entity in enumerate(all_suspicious_entities):
                try:
                    if hasattr(entity, 'entity') and hasattr(entity.entity, 'service'):
                        if 'emailservice' in str(entity.entity.service).lower():
                            emailservice_entities.append({
                                'index': i,
                                'service': entity.entity.service,
                                'pod': getattr(entity.entity, 'pod', 'unknown'),
                                'pattern': getattr(entity.anomaly_feature, 'pattern', 'unknown') if hasattr(entity, 'anomaly_feature') else 'unknown'
                            })
                except Exception as e:
                    self.logger.debug(f"检查实体{i}失败: {e}")

            self.logger.info(f"🔧 最终all_suspicious_entities中emailservice实体: {len(emailservice_entities)}个")
            for entity_info in emailservice_entities:
                self.logger.info(f"🔧 emailservice实体: index={entity_info['index']}, service={entity_info['service']}, pod={entity_info['pod']}, pattern={entity_info['pattern']}")

            self.logger.info(f"✅ 增强版Metric分析完成: 发现 {len(all_suspicious_entities)} 个异常对象")
            return response
            
        except Exception as e:
            self.logger.error(f"❌ 增强版Metric分析失败: {e}")
            return AnalysisResponse(
                case_uuid=request.case_uuid,
                start_time=request.start_time,
                end_time=request.end_time,
                data=request.data,
                analysis_type="metrics",
                results={},
                success=False,
                error=str(e)
            )

    async def _load_all_metrics_data(self, case_uuid: str, start_time: str, end_time: str) -> Dict[str, Dict[str, pd.DataFrame]]:
        """直接基于文件路径加载所有类型的metric数据 - 简化版本"""
        all_metrics = {
            'apm': {},      # APM数据 (Service/Pod级别)
            'infra': {},    # 基础设施数据 (Node/Pod级别)
            'tidb': {},     # TiDB数据库数据
            'other': {}     # 其他组件数据 (PD/TiKV)
        }

        try:
            self.logger.info(f"🔍 直接加载metrics数据文件: {case_uuid}")

            # 构建时间范围
            time_range = TimeRange(start=start_time, end=end_time)

            # 1. 加载APM数据
            apm_data = await self._load_apm_data_direct(time_range)
            all_metrics['apm'].update(apm_data)
            self.logger.info(f"📊 加载APM数据: {len(apm_data)} 个对象")

            # 2. 加载基础设施数据（Node、Pod级别，不包含TiDB）
            infra_data = await self._load_infra_data_direct(time_range)
            all_metrics['infra'].update(infra_data)
            self.logger.info(f"📊 加载Infra数据: {len(infra_data)} 个指标")

            # 🔧 修复：3. 单独加载TiDB数据库数据
            tidb_data = await self._load_tidb_data_direct(time_range)
            all_metrics['tidb'].update(tidb_data)
            self.logger.info(f"📊 加载TiDB数据: {len(tidb_data)} 个指标")

            # 4. 加载其他组件数据
            other_data = await self._load_other_data_direct(time_range)
            all_metrics['other'].update(other_data)
            self.logger.info(f"📊 加载Other数据: {len(other_data)} 个指标")

            total_objects = sum(len(category) for category in all_metrics.values())
            self.logger.info(f"✅ 总共加载 {total_objects} 个metric对象")

            return all_metrics

        except Exception as e:
            self.logger.error(f"❌ 加载metric数据失败: {e}")
            return all_metrics

    async def _load_apm_data_direct(self, time_range: TimeRange) -> Dict[str, pd.DataFrame]:
        """直接加载APM数据 - Pod和Service级别"""
        apm_data = {}

        try:
            # 1. 加载APM Pod数据
            pod_location = self.file_locator.locate_apm_pod_files(time_range)
            for file_path in pod_location.file_paths:
                df = self._safe_read_parquet(file_path)
                if df is not None and self._validate_dataframe(df):
                    # 🔧 数据单位标准化 - 确保延迟数据统一为微秒
                    df = self._standardize_apm_data_units(df)

                    # ⏰ 时间过滤 - 根据请求的时间范围过滤数据
                    df = self._filter_data_by_time_range(df, time_range)

                    # 从文件路径解析对象名称
                    file_name = Path(file_path).name
                    object_name = self._extract_object_name_from_apm_file(file_name)

                    key = f"pod_{object_name}"
                    apm_data[key] = df

                    # 添加解析信息
                    apm_data[key]['parsed_object_name'] = object_name
                    apm_data[key]['parsed_object_type'] = 'pod'
                    apm_data[key]['parsed_metric_type'] = 'apm_pod'

                    self.logger.debug(f"✅ 加载APM Pod: {object_name} ({len(df)} 条记录)")

            # 2. 加载APM Service数据
            service_location = self.file_locator.locate_apm_service_files(time_range)
            for file_path in service_location.file_paths:
                df = self._safe_read_parquet(file_path)
                if df is not None and self._validate_dataframe(df):
                    # 🔧 数据单位标准化 - 确保延迟数据统一为微秒
                    df = self._standardize_apm_data_units(df)

                    # ⏰ 时间过滤 - 根据请求的时间范围过滤数据
                    df = self._filter_data_by_time_range(df, time_range)

                    # 从文件路径解析对象名称
                    file_name = Path(file_path).name
                    object_name = self._extract_object_name_from_apm_file(file_name)

                    key = f"service_{object_name}"
                    apm_data[key] = df

                    # 添加解析信息
                    apm_data[key]['parsed_object_name'] = object_name
                    apm_data[key]['parsed_object_type'] = 'service'
                    apm_data[key]['parsed_metric_type'] = 'apm_service'

                    self.logger.debug(f"✅ 加载APM Service: {object_name} ({len(df)} 条记录)")

        except Exception as e:
            self.logger.error(f"❌ 加载APM数据失败: {e}")

        return apm_data

    async def _load_infra_data_direct(self, time_range: TimeRange) -> Dict[str, pd.DataFrame]:
        """直接加载基础设施数据 - Node/Pod/TiDB级别"""
        infra_data = {}

        try:
            # 使用FilePathLocator获取基础设施文件
            infra_location = self.file_locator.locate_infra_files(time_range)

            for file_path in infra_location.file_paths:
                file_name = Path(file_path).name

                # 🔧 修复：跳过TiDB文件，TiDB数据由_load_tidb_data_direct单独处理
                if 'tidb' in file_name:
                    continue

                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        # ⏰ 时间过滤 - 根据请求的时间范围过滤数据
                        df = self._filter_data_by_time_range(df, time_range)

                        if not df.empty:  # 时间过滤后仍有数据
                            # 从文件路径解析指标信息
                            file_name = Path(file_path).name
                            object_type, metric_name = self._extract_infra_info_from_file(file_name)

                            key = f"{object_type}_{metric_name}"
                            infra_data[key] = df

                            # 🔧 修复：添加解析信息，从数据中提取实际的对象名称
                            # 不能直接在DataFrame上设置列表，使用字典的方式存储元数据
                            infra_data[key + '_metadata'] = {
                                'object_type': object_type,
                                'metric_type': metric_name,
                                'object_names': []
                            }

                            # 从数据中提取实际的对象名称
                            if object_type == 'node' and 'kubernetes_node' in df.columns:
                                # Node数据：使用kubernetes_node列
                                actual_names = df['kubernetes_node'].dropna().unique().tolist()
                            elif object_type == 'pod' and 'pod' in df.columns:
                                # Pod数据：使用pod列
                                actual_names = df['pod'].dropna().unique().tolist()
                            else:
                                # 兜底：使用对象类型
                                actual_names = [object_type]

                            # 存储对象名称列表到元数据中
                            infra_data[key + '_metadata']['object_names'] = actual_names

                        self.logger.debug(f"✅ 加载Infra: {object_type}/{metric_name} ({len(df)} 条记录)")

                except Exception as e:
                    self.logger.warning(f"⚠️ 加载Infra文件失败 {file_path}: {e}")

        except Exception as e:
            self.logger.error(f"❌ 加载Infra数据失败: {e}")

        return infra_data

    def _standardize_apm_data_units(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化APM数据单位 - 根据DeepFlow官方文档，APM的rrt字段单位是微秒，保持不变"""
        try:
            df_copy = df.copy()

            # 根据DeepFlow官方文档：APM数据的rrt和rrt_max字段单位是微秒(μs)
            # 这是正确的单位，不需要转换！问题在于显示格式化
            latency_columns = ['rrt', 'rrt_max']

            for col in latency_columns:
                if col in df_copy.columns:
                    max_value = df_copy[col].max()
                    mean_value = df_copy[col].mean()

                    # 只记录数据范围，不做转换
                    if max_value > 0:
                        self.logger.debug(f"✅ {col}字段数据范围: 最大值={max_value:.0f}μs ({max_value/1000000:.2f}秒), 平均值={mean_value:.0f}μs")

                        # 检查是否有异常大的值（超过1小时=3600000000μs）
                        if max_value > 3600000000:
                            self.logger.warning(f"⚠️ {col}字段存在异常大的值: {max_value:.0f}μs ({max_value/3600000000:.1f}小时)")
                    else:
                        self.logger.debug(f"✅ {col}字段无数据或为零")

            # 保持原始数据不变，单位就是微秒
            return df_copy

        except Exception as e:
            self.logger.error(f"❌ 数据单位检查失败: {e}")
            return df  # 返回原始数据



    def _filter_data_by_time_range(self, df: pd.DataFrame, time_range: TimeRange) -> pd.DataFrame:
        """根据时间范围过滤数据 - 统一的时间过滤方法"""
        if df.empty or 'time' not in df.columns:
            return df

        try:
            # 🔧 对于metric数据，使用分钟边界范围过滤
            return self._filter_metric_data_by_minute_boundaries(df, time_range)

        except Exception as e:
            self.logger.error(f"❌ 时间过滤失败: {e}")
            # 如果时间过滤失败，返回原始数据
            return df

    def _filter_metric_data_by_minute_boundaries(self, df: pd.DataFrame, time_range: TimeRange) -> pd.DataFrame:
        """🔧 按分钟边界范围过滤metric数据（60秒粒度）"""
        if df.empty or 'time' not in df.columns:
            return df

        try:
            # 转换时间列为datetime类型，确保时区一致
            df['time'] = pd.to_datetime(df['time'], utc=True)

            # 转换UTC时间字符串为datetime
            start_dt = pd.to_datetime(time_range.start, utc=True)
            end_dt = pd.to_datetime(time_range.end, utc=True)

            # 映射到分钟边界范围
            mapped_start, mapped_end = self._map_to_minute_boundaries(start_dt, end_dt)

            # 使用分钟边界范围过滤数据
            mask = (df['time'] >= mapped_start) & (df['time'] <= mapped_end)
            filtered_df = df[mask].copy()

            self.logger.debug(f"🔧 分钟边界映射: {time_range.start} - {time_range.end}")
            self.logger.debug(f"🔧 映射到范围: {mapped_start} - {mapped_end}")
            self.logger.debug(f"🔧 过滤结果: {len(df)} -> {len(filtered_df)} 条记录")

            return filtered_df

        except Exception as e:
            self.logger.error(f"❌ 分钟边界过滤失败: {e}")
            # 如果分钟边界过滤失败，回退到普通时间过滤
            return self._filter_data_by_time_range_fallback(df, time_range)

    def _map_to_minute_boundaries(self, start_time: datetime, end_time: datetime) -> Tuple[datetime, datetime]:
        """将任意时间范围映射到分钟边界范围"""

        # 开始时间向下取整到分钟边界
        mapped_start = start_time.replace(second=0, microsecond=0)

        # 结束时间向上取整到分钟边界
        if end_time.second > 0 or end_time.microsecond > 0:
            mapped_end = end_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        else:
            mapped_end = end_time.replace(second=0, microsecond=0)

        return mapped_start, mapped_end

    def _detect_pod_deletion_events(self, time_range: TimeRange) -> List[Dict[str, Any]]:
        """🔧 新增：检测Pod删除事件与故障时间窗口的关联性"""
        deletion_events = []

        try:
            # 获取deleted文件
            deleted_files = self._get_deleted_pod_files(time_range)

            for deleted_file in deleted_files:
                try:
                    # 分析删除时间与故障时间的关联
                    pod_name = self._extract_pod_name_from_file(deleted_file)
                    deletion_data = self._analyze_deletion_timing(deleted_file, time_range)

                    if deletion_data:
                        deletion_events.append({
                            'type': 'pod_deletion',
                            'pod_name': pod_name,
                            'deletion_time': deletion_data.get('deletion_time'),
                            'severity': 'critical',
                            'description': f'Pod {pod_name} 在故障时间窗口内被删除',
                            'evidence': deletion_data
                        })
                        self.logger.info(f"🔍 检测到Pod删除事件: {pod_name}")

                except Exception as e:
                    self.logger.warning(f"⚠️ 分析deleted文件失败 {deleted_file}: {e}")

        except Exception as e:
            self.logger.error(f"❌ Pod删除事件检测失败: {e}")

        return deletion_events

    def _get_deleted_pod_files(self, time_range: TimeRange) -> List[str]:
        """获取deleted Pod文件列表"""
        import glob
        from pathlib import Path

        # 根据时间范围确定日期
        start_date = pd.to_datetime(time_range.start).strftime('%Y-%m-%d')

        # 搜索deleted文件
        pattern = f"/data/phaseone_data/{start_date}/metric-parquet/apm/pod/*deleted*.parquet"
        deleted_files = glob.glob(pattern)

        self.logger.debug(f"🔍 搜索deleted文件: {pattern}")
        self.logger.debug(f"🔍 找到 {len(deleted_files)} 个deleted文件")

        return deleted_files

    def _extract_pod_name_from_file(self, file_path: str) -> str:
        """从文件路径提取Pod名称"""
        import os
        filename = os.path.basename(file_path)
        # 格式: pod_productcatalogservice-2 (deleted)_2025-06-06.parquet
        if "(deleted)" in filename:
            pod_name = filename.replace("pod_", "").split(" (deleted)")[0]
            return pod_name
        return "unknown"

    def _analyze_deletion_timing(self, deleted_file: str, time_range: TimeRange) -> Optional[Dict[str, Any]]:
        """分析Pod删除时间与故障时间窗口的关联性"""
        try:
            df = pd.read_parquet(deleted_file)
            if df.empty or 'time' not in df.columns:
                return None

            df['time'] = pd.to_datetime(df['time'], utc=True)

            # 获取故障时间窗口
            start_dt = pd.to_datetime(time_range.start, utc=True)
            end_dt = pd.to_datetime(time_range.end, utc=True)

            # 使用分钟边界映射
            mapped_start, mapped_end = self._map_to_minute_boundaries(start_dt, end_dt)

            # 检查删除数据是否在时间窗口内
            deletion_data = df[(df['time'] >= mapped_start) & (df['time'] <= mapped_end)]

            if len(deletion_data) > 0:
                return {
                    'deletion_time': deletion_data['time'].iloc[-1],
                    'data_points': len(deletion_data),
                    'time_window_match': True,
                    'last_metrics': deletion_data.iloc[-1].to_dict()
                }
            else:
                # 检查是否在扩展时间窗口内
                extended_start = mapped_start - timedelta(minutes=5)
                extended_end = mapped_end + timedelta(minutes=5)
                extended_data = df[(df['time'] >= extended_start) & (df['time'] <= extended_end)]

                if len(extended_data) > 0:
                    return {
                        'deletion_time': extended_data['time'].iloc[-1],
                        'data_points': len(extended_data),
                        'time_window_match': False,
                        'proximity_to_fault': True,
                        'last_metrics': extended_data.iloc[-1].to_dict()
                    }

        except Exception as e:
            self.logger.warning(f"⚠️ 分析deletion timing失败: {e}")

        return None

    def _filter_data_by_time_range_fallback(self, df: pd.DataFrame, time_range: TimeRange) -> pd.DataFrame:
        """回退的时间过滤方法（原始逻辑）"""
        try:
            # 转换时间列为datetime类型，确保时区一致
            df['time'] = pd.to_datetime(df['time'], utc=True)

            # 转换UTC时间字符串为datetime，确保时区一致
            start_dt = pd.to_datetime(time_range.start, utc=True)
            end_dt = pd.to_datetime(time_range.end, utc=True)

            # 过滤数据
            mask = (df['time'] >= start_dt) & (df['time'] <= end_dt)
            filtered_df = df[mask].copy()

            self.logger.debug(f"⏰ 回退时间过滤: {len(df)} -> {len(filtered_df)} 条记录 "
                            f"({time_range.start} ~ {time_range.end})")

            return filtered_df

        except Exception as e:
            self.logger.error(f"❌ 回退时间过滤失败: {e}")
            # 如果时间过滤失败，返回原始数据
            return df

    async def _load_tidb_data_direct(self, time_range: TimeRange) -> Dict[str, pd.DataFrame]:
        """🔧 新增：单独加载TiDB数据库数据"""
        tidb_data = {}

        try:
            # 使用FilePathLocator获取基础设施文件，然后过滤TiDB文件
            infra_location = self.file_locator.locate_infra_files(time_range)

            for file_path in infra_location.file_paths:
                file_name = Path(file_path).name

                # 只处理TiDB相关文件
                if 'tidb' not in file_name:
                    continue

                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        # ⏰ 时间过滤 - 根据请求的时间范围过滤数据
                        df = self._filter_data_by_time_range(df, time_range)

                        if not df.empty:  # 时间过滤后仍有数据
                            # 解析TiDB指标名称: infra_tidb_memory_usage_2025-06-11.parquet -> memory_usage
                            match = re.match(r'infra_tidb_(.+)_\d{4}-\d{2}-\d{2}\.parquet', file_name)
                            if match:
                                metric_name = match.group(1)  # memory_usage, qps, duration_99th等
                                key = f"tidb_{metric_name}"
                                tidb_data[key] = df

                                self.logger.debug(f"✅ 加载TiDB: {metric_name} ({len(df)} 条记录)")

                except Exception as e:
                    self.logger.warning(f"⚠️ 加载TiDB文件失败 {file_path}: {e}")

        except Exception as e:
            self.logger.error(f"❌ 加载TiDB数据失败: {e}")

        return tidb_data

    async def _load_other_data_direct(self, time_range: TimeRange) -> Dict[str, pd.DataFrame]:
        """直接加载其他组件数据 - PD/TiKV级别"""
        other_data = {}

        try:
            # 使用FilePathLocator获取其他组件文件
            other_location = self.file_locator.locate_other_files(time_range)

            for file_path in other_location.file_paths:
                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        # ⏰ 时间过滤 - 根据请求的时间范围过滤数据
                        df = self._filter_data_by_time_range(df, time_range)

                        if not df.empty:  # 时间过滤后仍有数据
                            # 从文件路径解析组件和指标信息
                            file_name = Path(file_path).name
                            component_type, metric_name = self._extract_other_info_from_file(file_name)

                            key = f"{component_type}_{metric_name}"
                            if key not in other_data:
                                other_data[key] = df
                            else:
                                # 合并同一指标的多天数据
                                other_data[key] = pd.concat([other_data[key], df], ignore_index=True)

                            # 添加解析信息
                            other_data[key]['parsed_object_name'] = component_type
                            other_data[key]['parsed_object_type'] = component_type
                            other_data[key]['parsed_metric_type'] = metric_name

                            self.logger.debug(f"✅ 加载Other: {component_type}/{metric_name} ({len(df)} 条记录)")

                except Exception as e:
                    self.logger.warning(f"⚠️ 加载Other文件失败 {file_path}: {e}")

        except Exception as e:
            self.logger.error(f"❌ 加载Other数据失败: {e}")

        return other_data

    def _extract_object_name_from_apm_file(self, filename: str) -> str:
        """从APM文件名中提取对象名称"""
        try:
            # pod_adservice-0_2025-06-07.parquet -> adservice-0
            # service_adservice_2025-06-07.parquet -> adservice
            if filename.startswith('pod_'):
                match = re.match(r'pod_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
                if match:
                    return match.group(1)
            elif filename.startswith('service_'):
                match = re.match(r'service_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
                if match:
                    return match.group(1)

            return 'unknown'
        except Exception as e:
            self.logger.error(f"❌ 解析APM文件名失败 {filename}: {e}")
            return 'unknown'

    def _extract_infra_info_from_file(self, filename: str) -> tuple:
        """从基础设施文件名中提取对象类型和指标名称"""
        try:
            # infra_node_node_cpu_usage_rate_2025-06-07.parquet -> node, node_cpu_usage_rate
            # infra_pod_pod_cpu_usage_2025-06-07.parquet -> pod, pod_cpu_usage
            match = re.match(r'infra_(.+?)_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
            if match:
                object_type = match.group(1)  # node, pod
                metric_name = match.group(2)  # node_cpu_usage_rate, pod_cpu_usage
                return object_type, metric_name

            return 'unknown', 'unknown'
        except Exception as e:
            self.logger.error(f"❌ 解析Infra文件名失败 {filename}: {e}")
            return 'unknown', 'unknown'

    def _extract_tidb_metric_from_file(self, filename: str) -> str:
        """从TiDB文件名中提取指标名称"""
        try:
            # infra_tidb_qps_2025-06-07.parquet -> qps
            match = re.match(r'infra_tidb_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
            if match:
                return match.group(1)

            return 'unknown'
        except Exception as e:
            self.logger.error(f"❌ 解析TiDB文件名失败 {filename}: {e}")
            return 'unknown'

    def _extract_other_info_from_file(self, filename: str) -> tuple:
        """从其他组件文件名中提取组件类型和指标名称"""
        try:
            # infra_pd_leader_count_2025-06-07.parquet -> pd, leader_count
            # infra_tikv_cpu_usage_2025-06-07.parquet -> tikv, cpu_usage
            match = re.match(r'infra_(pd|tikv)_(.+)_\d{4}-\d{2}-\d{2}\.parquet', filename)
            if match:
                component_type = match.group(1)  # pd, tikv
                metric_name = match.group(2)     # leader_count, cpu_usage
                return component_type, metric_name

            return 'unknown', 'unknown'
        except Exception as e:
            self.logger.error(f"❌ 解析Other文件名失败 {filename}: {e}")
            return 'unknown', 'unknown'

    def _extract_object_and_metric_from_filename(self, filename: str) -> tuple:
        """从文件名中提取对象名称和指标名称 - 已废弃，使用新的解析方法"""
        # 这个方法已经被新的解析方法替代，保留用于兼容性
        return self._extract_object_name_from_apm_file(filename), 'legacy_metric'





    def _detect_column_anomalies_with_dynamic_threshold(self, df: pd.DataFrame, column: str,
                                                       object_name: str, metric_name: str,
                                                       category: str, file_path: str) -> List[Dict[str, Any]]:
        """使用动态阈值检测列异常 - 改进版本"""
        anomalies = []

        try:
            if column not in df.columns or df[column].empty:
                return anomalies

            # 🚀 关键修复：过滤信息性指标，这些指标不应该被当作异常检测
            informational_metrics = {
                # Node级别信息性指标
                'node_memory_MemTotal_bytes',      # 节点总内存大小（信息性）
                'node_filesystem_size_bytes',     # 磁盘总大小（信息性）
                'node_memory_MemAvailable_bytes', # 节点可用内存（应该用使用率而不是绝对值）
                'node_filesystem_free_bytes',     # 磁盘剩余空间（应该用使用率而不是绝对值）

                # Pod级别信息性指标
                'pod_memory_limit_bytes',         # Pod内存限制（信息性）
                'pod_cpu_limit_cores',            # Pod CPU限制（信息性）
                'pod_memory_working_set_bytes',   # Pod内存使用量（应该用使用率）
                'pod_memory_usage_bytes',         # Pod内存使用量（应该用使用率）

                # 🚀 注意：pod_processes不在过滤列表中，因为>1时可能表示Pod异常

                # Container级别信息性指标
                'container_spec_memory_limit_bytes', # 容器内存限制（信息性）
                'container_spec_cpu_quota',       # 容器CPU配额（信息性）
                'container_memory_working_set_bytes', # 容器内存使用量（应该用使用率）

                # 网络流量绝对值（应该用速率）
                'node_network_receive_bytes_total', # 网络接收总字节（累积值，信息性）
                'node_network_transmit_bytes_total', # 网络发送总字节（累积值，信息性）
                'pod_network_receive_bytes_total',  # Pod网络接收总字节（累积值，信息性）
                'pod_network_transmit_bytes_total', # Pod网络发送总字节（累积值，信息性）

                # 🔧 修复：磁盘时间指标过于敏感，0.01秒(P85.9)不应该报警
                'node_disk_write_time_seconds_total', # 磁盘写入时间（过于敏感，暂时禁用）
                'node_disk_read_time_seconds_total',  # 磁盘读取时间（过于敏感，暂时禁用）
            }

            # 如果是信息性指标，直接跳过异常检测
            if column in informational_metrics or metric_name in informational_metrics:
                self.logger.debug(f"⏭️ 跳过信息性指标: {column} ({metric_name})")
                return anomalies

            # 移除无效值
            valid_data = df[column].dropna()
            if len(valid_data) < 1:  # 🔧 修复：降低最小数据点要求，只要有数据就检测
                return anomalies

            # 🚀 修复：使用智能阈值管理器获取阈值
            if hasattr(self, 'get_intelligent_threshold'):
                threshold, threshold_type, threshold_metadata = self.get_intelligent_threshold(
                    category, column, object_name, valid_data
                )
            else:
                # 🚀 关键修复：使用智能阈值管理器，处理不同返回格式
                try:
                    threshold_result = self.threshold_manager.get_threshold(category, column, object_name)

                    # 🔧 处理不同的返回格式
                    if isinstance(threshold_result, tuple) and len(threshold_result) == 3:
                        # 新格式：返回(threshold, threshold_type, threshold_metadata)
                        threshold, threshold_type, threshold_metadata = threshold_result
                        self.logger.debug(f"🎯 智能阈值获取成功(新格式): {column}@{object_name} = {threshold} (来源: {threshold_type})")
                    elif isinstance(threshold_result, (int, float)):
                        # 旧格式：只返回threshold值
                        threshold = float(threshold_result)
                        threshold_type = 'optimized_threshold_manager'
                        threshold_metadata = {
                            'source': 'optimized_threshold_manager',
                            'strategy': 'intelligent',
                            'unit': 'unknown',
                            'confidence': 0.8,
                            'data_points': len(valid_data) if valid_data is not None else 0
                        }
                        self.logger.debug(f"🎯 智能阈值获取成功(旧格式): {column}@{object_name} = {threshold}")
                    else:
                        raise ValueError(f"未知的阈值返回格式: {type(threshold_result)}")

                except Exception as e:
                    self.logger.error(f"智能阈值获取失败 {column}@{object_name}: {e}")
                    # 🚀 紧急fallback到合理默认值
                    if 'memory' in column.lower():
                        if 'total' in column.lower() or 'size' in column.lower():
                            threshold = 128*1024*1024*1024  # 128GB 总内存
                        else:
                            threshold = 8*1024*1024*1024   # 8GB 使用内存
                    elif 'filesystem' in column.lower() or 'disk' in column.lower():
                        if 'size' in column.lower() or 'total' in column.lower():
                            threshold = 500*1024*1024*1024  # 500GB 磁盘大小
                        else:
                            threshold = 50*1024*1024*1024   # 50GB 可用磁盘
                    elif 'network' in column.lower() or 'bytes' in column.lower():
                        threshold = 100*1024*1024  # 100MB 网络流量
                    elif 'error' in column.lower() or 'ratio' in column.lower():
                        threshold = 0.05  # 5% 错误率
                    elif 'rrt' in column.lower() or 'response' in column.lower():
                        threshold = 8000  # 8秒 响应时间
                    elif 'cpu' in column.lower():
                        threshold = 85.0  # 85% CPU使用率
                    else:
                        threshold = 10000.0
                    threshold_type = 'emergency_fallback'
                    threshold_metadata = {
                        'source': 'emergency_fallback',
                        'strategy': 'hardcoded_reasonable',
                        'unit': 'auto_detected',
                        'confidence': 0.3,
                        'data_points': 0,
                        'error': str(e)
                    }

            self.logger.debug(f"🧠 智能阈值: {object_name}.{column} = {threshold:.4f} ({threshold_type}, 置信度: {threshold_metadata.get('confidence', 0.5):.1%})")

            # 计算异常指标
            p95 = valid_data.quantile(0.95)
            p99 = valid_data.quantile(0.99)
            mean_val = valid_data.mean()
            max_val = valid_data.max()

            # 🚀 使用智能异常判断
            component = self._determine_component_from_category(category, object_name) if hasattr(self, '_determine_component_from_category') else category

            # 🚀 增强异常检测：检测多种异常类型（高值、低值、突变等）
            detected_anomalies = self._detect_comprehensive_anomalies(
                valid_data, column, object_name, threshold, threshold_type,
                threshold_metadata, category, component
            )

            # 为每个检测到的异常创建anomaly对象
            for anomaly_info in detected_anomalies:
                anomaly = {
                    'object_name': object_name,
                    'metric_name': metric_name,
                    'column': column,
                    'value': anomaly_info['value'],
                    'threshold': anomaly_info['threshold'],
                    'threshold_type': threshold_type,
                    'anomaly_count': anomaly_info['anomaly_count'],
                    'severity': anomaly_info['severity'],
                    'category': category,
                    'file_path': file_path,
                    'p95': p95,
                    'p99': p99,
                    'mean': mean_val,
                    'data_points': len(valid_data),
                    # 🚀 智能阈值元数据
                    'threshold_metadata': threshold_metadata,
                    'threshold_explanation': self.intelligent_threshold_manager.get_threshold_explanation(
                        component, column, threshold, threshold_metadata
                    ) if hasattr(self, 'intelligent_threshold_manager') else "传统阈值",
                    # 🚀 新增：异常类型和描述
                    'anomaly_type': anomaly_info['anomaly_type'],
                    'anomaly_description': anomaly_info['description'],
                    # 🔧 新增：异常类型标识，用于ReasoningAgent区分
                    'anomaly_type_label': f"[{anomaly_info['anomaly_type']}]" if anomaly_info['anomaly_type'] != 'high_value' else ""
                }

                # 🚀 新增：pod_processes特殊处理，记录instance信息
                if column == 'pod_processes' and 'instance' in df.columns:
                    # 获取异常时刻的instance信息 - 根据异常类型调整
                    if anomaly_info['anomaly_type'] == 'high_value':
                        anomalous_indices = valid_data[valid_data > anomaly_info['threshold']].index
                    else:
                        # 对于其他异常类型，使用不同的筛选条件
                        anomalous_indices = valid_data.index  # 简化处理

                    if len(anomalous_indices) > 0:
                        # 获取所有异常记录的instance信息
                        anomalous_instances = df.loc[anomalous_indices, 'instance'].unique()
                        anomaly['pod_instances'] = list(anomalous_instances)
                        anomaly['pod_migration_detected'] = len(anomalous_instances) > 1

                        # 记录详细的异常时刻信息
                        anomaly['pod_processes_details'] = []
                        for idx in anomalous_indices[:5]:  # 最多记录5条详细信息
                            row = df.loc[idx]
                            detail = {
                                'timestamp': row.get('time', row.get('timestamp', 'unknown')),
                                'processes': row[column],
                                'instance': row.get('instance', 'unknown'),
                                'pod': object_name
                            }
                            anomaly['pod_processes_details'].append(detail)

                        self.logger.info(f"🔄 Pod进程异常: {object_name} 进程数={anomaly_info['value']:.0f} (阈值={anomaly_info['threshold']:.1f}), 涉及节点: {anomalous_instances}")
                        if len(anomalous_instances) > 1:
                            self.logger.warning(f"⚠️ Pod {object_name} 可能发生了节点迁移: {anomalous_instances}")

                # 🚀 新增：为所有Pod级别异常记录instance信息
                elif category == 'infra' and 'instance' in df.columns and 'pod' in df.columns:
                    # 为基础设施Pod异常记录instance信息 - 根据异常类型调整
                    if anomaly_info['anomaly_type'] == 'high_value':
                        anomalous_indices = valid_data[valid_data > anomaly_info['threshold']].index
                    else:
                        anomalous_indices = valid_data.index  # 简化处理

                    if len(anomalous_indices) > 0:
                        anomalous_instances = df.loc[anomalous_indices, 'instance'].unique()
                        anomaly['pod_instances'] = list(anomalous_instances)
                        anomaly['pod_migration_detected'] = len(anomalous_instances) > 1

                anomalies.append(anomaly)

                self.logger.info(f"🚨 发现{category}异常: {object_name}.{column} = {anomaly_info['value']:.2f} > {anomaly_info['threshold']:.2f} ({anomaly_info['anomaly_type']})")

        except Exception as e:
            self.logger.error(f"❌ 动态阈值异常检测失败 {column}: {e}")

        return anomalies

    def _detect_pod_data_breakpoints(self, df: pd.DataFrame, object_name: str, category: str) -> List[Dict[str, Any]]:
        """检测Pod多维数据的突然断点 - 用于识别网络故障注入、Pod重启等"""
        breakpoints = []

        try:
            # 🔧 修复：检查数据类型，如果是字典则尝试转换
            if not isinstance(df, pd.DataFrame):
                if isinstance(df, dict):
                    try:
                        # 尝试将字典转换为DataFrame
                        df = pd.DataFrame(df)
                        self.logger.info(f"🔄 成功将字典转换为DataFrame: {object_name}")
                    except Exception as e:
                        self.logger.warning(f"⏭️ 跳过断点检测: {object_name} - 字典转换失败: {e}")
                        return breakpoints
                else:
                    self.logger.warning(f"⏭️ 跳过断点检测: {object_name} - 数据类型不支持: {type(df)}")
                    return breakpoints

            self.logger.info(f"🔍 开始检测Pod断点: {object_name}, 数据形状: {df.shape}")
            if 'time' not in df.columns or len(df) < 10:
                self.logger.info(f"⏭️ 跳过断点检测: {object_name} - 时间列缺失或数据太少")
                return breakpoints

            # 确保时间列是datetime类型
            df = df.copy()
            df['time'] = pd.to_datetime(df['time'])
            df = df.sort_values('time')

            # 检测时间间隔断点
            time_diffs = df['time'].diff()
            normal_interval = time_diffs.median()

            # 基础设施数据断点检测：严格按分钟采样
            # 断点定义：如果01:01有数据，下一条是01:08，那么01:02-01:07这6分钟就是断点
            expected_interval = pd.Timedelta(minutes=1)  # 期望的1分钟间隔

            # 检测超过1分钟的间隔（即有数据缺失）
            large_gaps = time_diffs[time_diffs > expected_interval]
            self.logger.debug(f"🔍 {object_name} 基础设施数据断点检测: 期望间隔=1分钟, 发现大间隔数量={len(large_gaps)}")

            self.logger.info(f"🔍 {object_name} 断点检测: 阈值={expected_interval}, 大间隔数量={len(large_gaps)}")

            if len(large_gaps) > 0:
                self.logger.info(f"🔍 {object_name} 发现时间断点: {len(large_gaps)}个")

                for gap_idx, gap_duration in large_gaps.items():
                    # 🔧 修复：确保索引在有效范围内
                    if gap_idx <= 0 or gap_idx >= len(df):
                        continue

                    gap_start = df.iloc[gap_idx-1]['time'] if gap_idx > 0 else df.iloc[0]['time']
                    gap_end = df.iloc[gap_idx]['time'] if gap_idx < len(df) else df.iloc[-1]['time']

                    # 检查断点前后的数据变化
                    before_start = max(0, gap_idx-5)
                    before_end = max(0, gap_idx)
                    after_start = min(gap_idx, len(df))
                    after_end = min(gap_idx+5, len(df))

                    before_data = df.iloc[before_start:before_end] if before_end > before_start else pd.DataFrame()
                    after_data = df.iloc[after_start:after_end] if after_end > after_start else pd.DataFrame()

                    # 分析多维数据的变化
                    data_changes = self._analyze_multidimensional_changes(before_data, after_data, object_name)

                    # 计算实际缺失的分钟数
                    # 例如：01:01 -> 01:08，间隔7分钟，实际缺失6分钟（01:02-01:07）
                    total_gap_minutes = int(gap_duration.total_seconds() / 60)
                    missing_minutes = total_gap_minutes - 1  # 减去正常的1分钟间隔
                    duration_minutes = missing_minutes

                    # 只有真正缺失数据时才创建断点
                    if missing_minutes <= 0:
                        continue  # 跳过没有实际缺失的间隔

                    self.logger.debug(f"🔍 {object_name} 断点: {gap_start} -> {gap_end} (间隔: {gap_duration}, 缺失: {missing_minutes}分钟)")

                    breakpoint_info = {
                        'object_name': object_name,
                        'breakpoint_type': 'time_gap',
                        'gap_start': gap_start.strftime('%Y-%m-%d %H:%M:%S'),
                        'gap_end': gap_end.strftime('%Y-%m-%d %H:%M:%S'),
                        'gap_duration': str(gap_duration),
                        'duration_minutes': duration_minutes,  # 实际缺失的分钟数
                        'missing_minutes': duration_minutes,
                        'severity': 'critical' if duration_minutes >= 5 else 'high',
                        'data_changes': data_changes,
                        'possible_causes': self._infer_breakpoint_causes(gap_duration, data_changes)
                    }

                    breakpoints.append(breakpoint_info)
                    self.logger.warning(f"⚠️ {object_name} 断点: {gap_start} -> {gap_end} (间隔: {gap_duration})")

            # 🔧 修复：断点检测应该只关注数据缺失（时间间隔异常），不检测数据归零
            # 数据归零不是断点，而是正常的指标变化
            self.logger.debug(f"🔍 {object_name} 断点检测完成，只检测时间间隔异常，不检测数据归零")

        except Exception as e:
            self.logger.error(f"❌ Pod断点检测失败 {object_name}: {e}")

        return breakpoints

    def _analyze_multidimensional_changes(self, before_data: pd.DataFrame, after_data: pd.DataFrame, object_name: str) -> Dict[str, Any]:
        """分析断点前后的多维数据变化"""
        changes = {
            'metrics_affected': [],
            'value_changes': {},
            'pattern_changes': {}
        }

        try:
            numeric_columns = before_data.select_dtypes(include=[np.number]).columns

            for col in numeric_columns:
                if col in ['time']:
                    continue

                before_mean = before_data[col].mean() if len(before_data) > 0 else 0
                after_mean = after_data[col].mean() if len(after_data) > 0 else 0

                # 检测显著变化
                if before_mean > 0:
                    change_ratio = abs(after_mean - before_mean) / before_mean
                    if change_ratio > 0.5:  # 50%以上的变化
                        changes['metrics_affected'].append(col)
                        changes['value_changes'][col] = {
                            'before': round(before_mean, 4),
                            'after': round(after_mean, 4),
                            'change_ratio': round(change_ratio, 2)
                        }
                elif after_mean > 0:
                    # 从0变为非0
                    changes['metrics_affected'].append(col)
                    changes['value_changes'][col] = {
                        'before': 0,
                        'after': round(after_mean, 4),
                        'change_type': 'recovery'
                    }

        except Exception as e:
            self.logger.debug(f"多维数据变化分析失败 {object_name}: {e}")

        return changes

    # def _detect_zero_transitions(self, data_series: pd.Series, time_series: pd.Series, column: str, object_name: str) -> List[Dict[str, Any]]:
    #     """检测数据突然归零的转换点"""
    #     transitions = []

    #     try:
    #         if len(data_series) < 5:
    #             return transitions

    #         # 检测从非零到零的转换
    #         non_zero_mask = data_series != 0
    #         zero_mask = data_series == 0

    #         # 找到连续的零值段
    #         zero_groups = []
    #         in_zero_group = False
    #         zero_start = None

    #         for i, is_zero in enumerate(zero_mask):
    #             if is_zero and not in_zero_group:
    #                 # 开始零值段
    #                 in_zero_group = True
    #                 zero_start = i
    #             elif not is_zero and in_zero_group:
    #                 # 结束零值段
    #                 in_zero_group = False
    #                 if zero_start is not None and i - zero_start >= 3:  # 至少连续3个零值
    #                     zero_groups.append((zero_start, i-1))

    #         # 处理最后一个零值段
    #         if in_zero_group and zero_start is not None:
    #             zero_groups.append((zero_start, len(data_series)-1))

    #         # 分析每个零值段
    #         for start_idx, end_idx in zero_groups:
    #             # 🔧 修复：确保索引在有效范围内
    #             if start_idx >= len(data_series) or end_idx >= len(data_series):
    #                 continue

    #             before_value = data_series.iloc[start_idx-1] if start_idx > 0 else 0
    #             after_value = data_series.iloc[end_idx+1] if end_idx+1 < len(data_series) else 0

    #             if before_value > 0:  # 从非零变为零
    #                     zero_duration = time_series.iloc[end_idx] - time_series.iloc[start_idx]

    #                     transition = {
    #                         'object_name': object_name,
    #                         'breakpoint_type': 'data_zero_transition',
    #                         'metric': column,
    #                         'zero_start': time_series.iloc[start_idx].strftime('%Y-%m-%d %H:%M:%S'),
    #                         'zero_end': time_series.iloc[end_idx].strftime('%Y-%m-%d %H:%M:%S'),
    #                         'zero_duration': str(zero_duration),
    #                         'before_value': round(before_value, 4),
    #                         'after_value': round(after_value, 4),
    #                         'severity': 'high' if zero_duration > pd.Timedelta(minutes=2) else 'medium',
    #                         'possible_causes': ['pod_restart', 'network_isolation', 'service_stop']
    #                     }

    #                     transitions.append(transition)
    #                     self.logger.info(f"📊 {object_name}.{column} 数据归零: {transition['zero_start']} -> {transition['zero_end']}")

    #     except Exception as e:
    #         self.logger.debug(f"零值转换检测失败 {object_name}.{column}: {e}")

    #     return transitions

    def _infer_breakpoint_causes(self, gap_duration: pd.Timedelta, data_changes: Dict[str, Any]) -> List[str]:
        """根据断点特征推断可能的原因"""
        causes = []

        try:
            # 根据间隔时长推断
            if gap_duration > pd.Timedelta(minutes=10):
                causes.append('pod_restart')
                causes.append('node_failure')
            elif gap_duration > pd.Timedelta(minutes=2):
                causes.append('network_fault_injection')
                causes.append('pod_restart')
            else:
                causes.append('temporary_network_issue')

            # 根据数据变化模式推断
            affected_metrics = data_changes.get('metrics_affected', [])
            if len(affected_metrics) > 5:
                causes.append('comprehensive_failure')

            # 🔧 增强：更精确的故障类型推断
            # 检查网络相关指标
            network_metrics = [m for m in affected_metrics if 'network' in m.lower()]
            if len(network_metrics) > 0:
                causes.append('network_fault_injection')
                causes.append('network_partition')
                # 如果网络指标归零，很可能是故障注入
                if any('zero_transition' in str(data_changes) for _ in [1]):
                    causes.append('network_isolation')

            # 检查CPU指标
            cpu_metrics = [m for m in affected_metrics if 'cpu' in m.lower()]
            if len(cpu_metrics) > 0:
                causes.append('pod_restart')
                causes.append('resource_exhaustion')

            # 检查内存指标
            memory_metrics = [m for m in affected_metrics if 'memory' in m.lower()]
            if len(memory_metrics) > 0:
                causes.append('pod_restart')
                causes.append('oom_kill')

            # 如果CPU和内存同时异常，很可能是Pod重启
            if len(cpu_metrics) > 0 and len(memory_metrics) > 0:
                causes.append('pod_restart')
                causes.append('node_failure')

        except Exception as e:
            self.logger.debug(f"断点原因推断失败: {e}")
            causes = ['unknown']

        return causes if causes else ['unknown']

    def _create_breakpoint_suspicious_entity(self, object_type: str, object_name: str,
                                           breakpoint: Dict[str, Any], df: pd.DataFrame,
                                           request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """为Pod断点创建SuspiciousEntity"""
        try:
            breakpoint_type = breakpoint.get('breakpoint_type', 'unknown')
            severity = breakpoint.get('severity', 'medium')
            possible_causes = breakpoint.get('possible_causes', [])

            # 构建描述信息
            if breakpoint_type == 'time_gap':
                description = f"Pod数据断点: {breakpoint.get('gap_start')} -> {breakpoint.get('gap_end')} (间隔: {breakpoint.get('gap_duration')})"
                anomaly_type = 'pod_data_breakpoint'
            elif breakpoint_type == 'data_zero_transition':
                metric = breakpoint.get('metric', 'unknown')
                description = f"Pod指标归零: {metric} 在 {breakpoint.get('zero_start')} -> {breakpoint.get('zero_end')}"
                anomaly_type = 'pod_metric_zero_transition'
            else:
                description = f"Pod异常断点: {breakpoint_type}"
                anomaly_type = 'pod_unknown_breakpoint'

            # 计算置信度
            confidence = 0.9 if len(possible_causes) > 1 else 0.7
            if 'network_fault_injection' in possible_causes:
                confidence = 0.95  # 网络故障注入的置信度更高

            # 🔧 新增：构建类似APM业务异常的格式化描述
            breakpoint_summary = self._format_breakpoint_summary(breakpoint, possible_causes)

            # 🔧 修复：创建标准的SuspiciousEntity对象，而不是简单字典
            from .base_aiops_agent import SuspiciousEntity, EntityInfo, AnomalyFeature, TimeRange, DataLocation

            # 构建实体信息
            entity_info = EntityInfo(
                service=object_name.split('-')[0] if '-' in object_name else object_name,
                pod=object_name if object_type == 'pod' else None,
                namespace='hipstershop',  # 默认命名空间
                metric_type='breakpoint_detection'
            )

            # 构建异常特征
            anomaly_feature = AnomalyFeature(
                pattern=anomaly_type,
                confidence=confidence,
                metric_value=0.0,  # 断点检测不基于具体数值
                threshold=0.0,
                metric_type='breakpoint_detection',
                severity=severity
            )

            # 构建时间范围
            time_range = TimeRange(
                start=breakpoint.get('gap_start', breakpoint.get('zero_start', '')),
                end=breakpoint.get('gap_end', breakpoint.get('zero_end', ''))
            )

            # 构建数据位置信息
            data_location = DataLocation(
                file_type='metric',
                root_dir='2025-06-06/',
                file_paths=[f'/data/phaseone_data/2025-06-06/metric-parquet/infra/infra_pod/{object_name}*'],
                file_matching_rule=f'Pod {object_name} breakpoint detection'
            )

            # 🔧 修复：构建statistical_analysis字段，确保ReasoningAgent能正确显示断点信息
            statistical_analysis = {
                'object_name': object_name,
                'object_type': object_type,
                'metric_category': 'infra',
                'metric_subcategory': 'pod_breakpoint',
                'metric_name': f"{object_name}_breakpoint",
                'metric_display_name': f"Pod断点异常: {breakpoint_type}",
                'metric_unit': 'count',
                'current_value': 1.0,  # 断点数量
                'threshold': 0.5,
                'deviation_ratio': 2.0,  # 断点的偏差比率
                'severity': severity,
                'severity_level': severity,  # 🔧 修复：ReasoningAgent期望的字段名
                'formatted_value': f"1个{severity}断点",
                'formatted_threshold': '0.5个',
                'service_criticality': 'normal',
                'metric_impact_type': 'availability'
            }

            entity = SuspiciousEntity(
                entity=entity_info,
                time_range=time_range,
                anomaly_feature=anomaly_feature,
                data_location=data_location,
                confidence=confidence,
                raw_data_records=[{
                    'breakpoint_details': breakpoint,
                    'possible_root_causes': possible_causes,
                    'detection_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': 'pod_breakpoint_detection'
                }],
                data_summary={
                    # 🔧 关键修复：将statistical_analysis的内容直接放在data_summary根级别
                    **statistical_analysis,
                    # 断点特有的信息
                    'breakpoint_summary': breakpoint_summary,
                    'has_breakpoint_anomalies': True,
                    'pod_breakpoint_anomalies': {
                        'breakpoint_events': [breakpoint],
                        'breakpoint_count': 1,
                        'max_severity': severity,
                        'overall_severity': severity,
                        'description': description,
                        'time_ranges': [{
                            'start': breakpoint.get('gap_start', breakpoint.get('zero_start', '')),
                            'end': breakpoint.get('gap_end', breakpoint.get('zero_end', '')),
                            'duration_minutes': breakpoint.get('duration_minutes', 0),
                            'type': breakpoint_type
                        }],
                        'affected_metrics': [breakpoint.get('metric_name', 'unknown')]
                    }
                }
            )

            self.logger.info(f"🔍 创建断点实体: {object_name} - {anomaly_type} (置信度: {confidence:.1%})")
            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建断点实体失败 {object_name}: {e}")
            return None



    async def _force_pod_breakpoint_detection(self, infra_data: Dict[str, pd.DataFrame], request: AnalysisRequest) -> List[SuspiciousEntity]:
        """强制执行Pod断点检测 - 不依赖于常规异常检测"""
        breakpoint_entities = []

        try:
            self.logger.info(f"🔍 强制断点检测: 处理 {len(infra_data)} 个基础设施对象")

            for object_name, object_df in infra_data.items():
                if object_df is None or len(object_df) == 0:
                    continue

                # 🔧 修复：处理基础设施指标数据，从中提取Pod断点信息
                # 基础设施数据包含Pod指标，需要从指标数据中检测断点
                if object_name.startswith('pod_pod_'):
                    # 这是Pod指标数据，执行断点检测
                    object_type = 'pod_metric'
                    # 从指标名称中提取指标类型
                    metric_type = object_name.replace('pod_pod_', '')
                    self.logger.info(f"🔍 开始为Pod指标 {metric_type} 执行断点检测")

                    # 执行断点检测
                    breakpoints = self._detect_pod_data_breakpoints(object_df, object_name, 'infra')
                    self.logger.info(f"🔍 Pod指标 {metric_type} 断点检测结果: {len(breakpoints)}个断点")

                    if breakpoints:
                        # 为每个断点创建SuspiciousEntity
                        for i, breakpoint in enumerate(breakpoints):
                            self.logger.info(f"🔍 处理断点 {i+1}/{len(breakpoints)}: {breakpoint.get('breakpoint_type', 'unknown')}")

                            # 🔧 从指标数据中推断Pod名称
                            # 从DataFrame中获取Pod实例信息
                            pod_instances = self._extract_pod_instances_from_metric_data(object_df, metric_type)

                            # 为每个Pod实例创建断点异常信息
                            for pod_instance in pod_instances:
                                # 🔧 修复：确保断点事件包含正确的metric_name
                                enhanced_breakpoint = breakpoint.copy()
                                enhanced_breakpoint['metric_name'] = metric_type
                                enhanced_breakpoint['pod_name'] = pod_instance

                                # 创建断点异常信息
                                pod_breakpoint_anomalies = {
                                    'breakpoint_events': [enhanced_breakpoint],
                                    'breakpoint_count': 1,
                                    'max_severity': enhanced_breakpoint.get('severity', 'medium'),
                                    'description': f'检测到Pod {pod_instance} {metric_type}指标断点异常: {enhanced_breakpoint.get("breakpoint_type", "unknown")}',
                                    'metric_type': metric_type
                                }

                                # 创建断点SuspiciousEntity
                                entity = self._create_breakpoint_suspicious_entity(
                                    'pod', pod_instance, enhanced_breakpoint, object_df, request
                                )

                                if entity:
                                    breakpoint_entities.append(entity)
                                    self.logger.info(f"✅ 成功创建断点实体: {pod_instance} ({metric_type})")
                                else:
                                    self.logger.error(f"❌ 断点实体创建失败: {pod_instance} - 返回了None")

            return breakpoint_entities

        except Exception as e:
            self.logger.error(f"❌ 强制断点检测失败: {e}")
            return []

    def _extract_pod_instances_from_metric_data(self, df: pd.DataFrame, metric_type: str) -> List[str]:
        """从指标数据中提取Pod实例名称"""
        try:
            pod_instances = []

            # 检查DataFrame中是否有Pod相关的列
            if 'pod' in df.columns:
                # 直接从pod列获取Pod名称
                unique_pods = df['pod'].dropna().unique()
                pod_instances.extend([str(pod) for pod in unique_pods if str(pod) != 'nan'])

            elif 'instance' in df.columns:
                # 从instance列中提取Pod名称
                unique_instances = df['instance'].dropna().unique()
                for instance in unique_instances:
                    if isinstance(instance, str) and any(service in instance for service in
                        ['frontend', 'cart', 'email', 'currency', 'product', 'checkout', 'shipping', 'ad', 'recommendation', 'payment']):
                        pod_instances.append(instance)

            # 如果没有找到Pod实例，使用通用的Pod名称
            if not pod_instances:
                # 根据指标类型推断可能的Pod
                common_services = ['frontend', 'cartservice', 'emailservice', 'currencyservice',
                                 'productcatalogservice', 'checkoutservice', 'shippingservice',
                                 'adservice', 'recommendationservice', 'paymentservice']

                # 为每个服务生成可能的Pod实例
                for service in common_services:
                    for i in range(3):  # 假设每个服务有0-2个副本
                        pod_instances.append(f"{service}-{i}")

            self.logger.debug(f"🔍 从{metric_type}指标中提取到{len(pod_instances)}个Pod实例")
            return pod_instances[:5]  # 限制最多5个实例，避免过多

        except Exception as e:
            self.logger.error(f"❌ 提取Pod实例失败: {e}")
            return ['unknown-pod-0']  # 返回默认Pod名称



    def _format_breakpoint_summary(self, breakpoint: Dict[str, Any], possible_causes: List[str]) -> str:
        """格式化断点检测结果为类似APM业务异常的格式"""
        try:
            breakpoint_type = breakpoint.get('breakpoint_type', 'unknown')
            severity = breakpoint.get('severity', 'medium')

            # 构建断点描述
            if breakpoint_type == 'time_gap':
                gap_duration = breakpoint.get('gap_duration', 'unknown')
                description = f"数据断点{gap_duration}"
            elif breakpoint_type == 'data_zero_transition':
                metric = breakpoint.get('metric', 'unknown')
                zero_duration = breakpoint.get('zero_duration', 'unknown')
                description = f"{metric}归零{zero_duration}"
            else:
                description = f"异常断点({breakpoint_type})"

            # 构建可能原因描述
            if 'network_fault_injection' in possible_causes:
                cause_desc = "疑似网络故障注入"
            elif 'pod_restart' in possible_causes:
                cause_desc = "疑似Pod重启"
            elif 'node_failure' in possible_causes:
                cause_desc = "疑似节点故障"
            elif 'oom_kill' in possible_causes:
                cause_desc = "疑似内存溢出"
            else:
                cause_desc = f"疑似{possible_causes[0] if possible_causes else 'unknown'}"

            # 计算评分（基于严重程度和置信度）
            if severity == 'critical':
                score = 5
            elif severity == 'high':
                score = 4
            elif severity == 'medium':
                score = 3
            else:
                score = 2

            # 构建最终格式（类似APM业务异常格式）
            summary = f"{description}, {cause_desc} (严重程度:{severity}, 评分:{score})"

            return summary

        except Exception as e:
            self.logger.debug(f"格式化断点摘要失败: {e}")
            return "断点异常检测 (严重程度:medium, 评分:3)"

    def _detect_comprehensive_anomalies(self, valid_data: pd.Series, column: str, object_name: str,
                                       threshold: float, threshold_type: str, threshold_metadata: dict,
                                       category: str, component: str) -> List[Dict[str, Any]]:
        """综合异常检测：检测高值、低值、突变等多种异常类型"""
        anomalies = []

        if len(valid_data) == 0:
            return anomalies

        # 基本统计信息
        mean_val = valid_data.mean()
        std_val = valid_data.std()
        min_val = valid_data.min()
        max_val = valid_data.max()

        # 1. 🚀 传统高值异常检测
        if hasattr(self, 'intelligent_threshold_manager') and component in ['tidb', 'tikv', 'pd']:
            # 对每个数据点进行异常判断
            anomalous_mask = valid_data.apply(
                lambda x: self.intelligent_threshold_manager.is_anomaly(
                    component, column, x, threshold, threshold_type, threshold_metadata
                )
            )
            high_anomalous_values = valid_data[anomalous_mask]
        else:
            # 传统异常判断（高于阈值）
            high_anomalous_values = valid_data[valid_data > threshold]

        if len(high_anomalous_values) > 0:
            anomalies.append({
                'anomaly_type': 'high_value',
                'value': high_anomalous_values.max(),
                'threshold': threshold,
                'anomaly_count': len(high_anomalous_values),
                'severity': self._calculate_severity(high_anomalous_values.max(), threshold),
                'description': f"High {column} detected: {high_anomalous_values.max():.3f} > {threshold:.3f}"
            })

        # 2. 🚀 CPU突然为0异常检测（进程可能崩溃）
        if 'cpu' in column.lower():
            zero_count = (valid_data == 0).sum()
            if zero_count > 0 and mean_val > 0.1:  # 平均值大于0.1%但有0值
                zero_ratio = zero_count / len(valid_data)
                if zero_ratio > 0.1:  # 超过10%的时间CPU为0
                    anomalies.append({
                        'anomaly_type': 'cpu_zero',
                        'value': 0.0,
                        'threshold': 0.01,  # 最小期望CPU使用率
                        'anomaly_count': zero_count,
                        'severity': 'critical',
                        'description': f"CPU dropped to 0% for {object_name} ({zero_ratio:.1%} of time, possible process crash)"
                    })

        # 3. 🚀 内存泄漏检测（持续增长）
        if 'memory' in column.lower() and len(valid_data) > 5:
            # 计算趋势（线性回归斜率）
            import numpy as np
            x = np.arange(len(valid_data))
            try:
                slope = np.polyfit(x, valid_data.values, 1)[0]
                if slope > threshold * 0.1:  # 增长率超过阈值的10%
                    anomalies.append({
                        'anomaly_type': 'memory_leak',
                        'value': slope,
                        'threshold': threshold * 0.1,
                        'anomaly_count': 1,
                        'severity': 'high',
                        'description': f"Potential memory leak for {object_name} (growth rate: {slope:.3f}/interval)"
                    })
            except:
                pass  # 如果线性回归失败，跳过

        # 4. 🚀 进程数异常检测
        if 'process' in column.lower():
            # 进程数突然下降（可能是进程崩溃）- 直接使用阈值1
            if min_val < 1:  # 进程数低于1表示异常
                anomalies.append({
                    'anomaly_type': 'process_drop',
                    'value': min_val,
                    'threshold': 1,  # 直接设置阈值为1
                    'anomaly_count': (valid_data < 1).sum(),
                    'severity': 'high',
                    'description': f"Process count dropped for {object_name} (min: {min_val:.0f}, 阈值: 1)"
                })

            # 进程数异常增长
            if max_val > mean_val * 2 and mean_val > 1:  # 最大值超过平均值2倍
                anomalies.append({
                    'anomaly_type': 'process_spike',
                    'value': max_val,
                    'threshold': mean_val * 2,
                    'anomaly_count': (valid_data > mean_val * 2).sum(),
                    'severity': 'medium',
                    'description': f"Process count spike for {object_name} (max: {max_val:.0f} vs avg: {mean_val:.0f})"
                })

        # 5. 🔧 修复：网络/磁盘I/O突增检测 - 避免与主要异常重复
        if any(keyword in column.lower() for keyword in ['network', 'disk', 'fs_', 'bytes']):
            # 🔧 修复：只有在没有检测到主要高值异常时才检测I/O突增
            if len(anomalies) == 0 and std_val > 0 and max_val > mean_val * 3:  # 最大值超过平均值3倍
                io_type = "network" if "network" in column.lower() else "disk"
                anomalies.append({
                    'anomaly_type': f'{io_type}_spike',
                    'value': max_val,
                    'threshold': mean_val * 3,
                    'anomaly_count': (valid_data > mean_val * 3).sum(),
                    'severity': 'medium',
                    'description': f"{io_type.title()} I/O spike for {object_name} ({max_val/1024:.1f}KB vs avg {mean_val/1024:.1f}KB)"
                })

        # 6. 🔧 修复：指标值突变检测 - 避免与主要异常重复
        if len(valid_data) > 1 and std_val > 0:
            # 🔧 修复：只有在没有检测到主要高值异常时才检测突变
            if len(anomalies) == 0:  # 只有在没有其他异常时才检测突变
                # 计算相邻值的变化率
                diff_values = valid_data.diff().abs()
                max_change = diff_values.max()
                change_threshold = threshold * 0.5 if threshold > 0 else std_val * 2

                if max_change > change_threshold:
                    anomalies.append({
                        'anomaly_type': 'value_spike',
                        'value': max_change,
                        'threshold': change_threshold,
                        'anomaly_count': (diff_values > change_threshold).sum(),
                        'severity': 'medium',
                        'description': f"Value spike detected for {object_name} (max change: {max_change:.3f})"
                    })

        return anomalies

    async def _detect_apm_anomalies(self, apm_data: Dict[str, pd.DataFrame], request: AnalysisRequest) -> List[SuspiciousEntity]:
        """检测APM异常 - 完全重写版本，使用动态阈值"""
        suspicious_entities = []

        for data_key, df in apm_data.items():
            if df.empty:
                continue

            try:
                # 🔧 修复：从DataFrame中获取真实的对象信息
                object_name = df['parsed_object_name'].iloc[0] if 'parsed_object_name' in df.columns else 'unknown'
                object_type = df['parsed_object_type'].iloc[0] if 'parsed_object_type' in df.columns else 'unknown'
                metric_type = df['parsed_metric_type'].iloc[0] if 'parsed_metric_type' in df.columns else 'unknown'

                self.logger.debug(f"🔍 分析APM对象: {object_name} ({object_type}) - {metric_type}")

                # 🔧 优化：使用策略管理器决定阈值检测方法
                all_anomalies = []

                # 1. 检查阈值策略
                should_use_baseline = self.strategy_manager.should_use_baseline('apm', object_name)
                baseline = self.baseline_manager.get_baseline_for_object(object_name, object_type) if should_use_baseline else None

                if baseline and should_use_baseline:
                    # 使用基线数据检测异常
                    baseline_anomalies = self._detect_apm_anomalies_with_baseline_v2(
                        df, object_name, object_type, baseline
                    )
                    all_anomalies.extend(baseline_anomalies)
                    self.logger.info(f"🎯 使用基线数据检测APM异常: {object_name} -> {len(baseline_anomalies)}个异常")

                    # 如果基线检测没有发现异常，且启用了回退机制，则使用动态阈值
                    if len(baseline_anomalies) == 0 and self.strategy_manager.should_fallback_to_dynamic('apm', object_name):
                        self.logger.info(f"🔄 基线检测无异常，回退到动态阈值: {object_name}")
                        for column in ['error_ratio', 'rrt', 'request', 'response']:
                            if column in df.columns:
                                column_anomalies = self._detect_column_anomalies_with_dynamic_threshold(
                                    df, column, object_name, metric_type, 'apm', ''
                                )
                                all_anomalies.extend(column_anomalies)
                else:
                    # 使用动态阈值检测
                    fallback_strategy = self.strategy_manager.get_fallback_strategy('apm', object_name)
                    for column in ['error_ratio', 'rrt', 'request', 'response']:
                        if column in df.columns:
                            column_anomalies = self._detect_column_anomalies_with_dynamic_threshold(
                                df, column, object_name, metric_type, 'apm', ''
                            )
                            all_anomalies.extend(column_anomalies)

                    reason = "无基线数据" if not baseline else "策略配置"
                    self.logger.info(f"🔄 使用{fallback_strategy}阈值检测APM异常({reason}): {object_name} -> {len(all_anomalies)}个异常")

                # 为每个异常创建SuspiciousEntity
                self.logger.info(f"🔧 开始为{len(all_anomalies)}个异常创建SuspiciousEntity: {object_name}")
                for i, anomaly in enumerate(all_anomalies):
                    try:
                        self.logger.info(f"🔧 处理异常{i+1}/{len(all_anomalies)}: {anomaly.get('column', 'unknown')} = {anomaly.get('value', 'unknown')}")
                        entity = self._create_apm_suspicious_entity_v2(
                            object_type, object_name, anomaly, df, request
                        )
                        if entity:
                            suspicious_entities.append(entity)
                            # 🔧 调试：记录SuspiciousEntity的详细信息
                            anomaly_feature = entity.anomaly_feature if hasattr(entity, 'anomaly_feature') else {}
                            self.logger.info(f"✅ 成功创建SuspiciousEntity: {object_name}.{anomaly.get('column', 'unknown')}")
                            self.logger.info(f"🔧 SuspiciousEntity详情: detection_method={anomaly.get('detection_method', 'unknown')}, severity={anomaly.get('severity', 'unknown')}")
                            if 'baseline_key' in anomaly:
                                self.logger.info(f"🔧 基线异常详情: baseline_key={anomaly.get('baseline_key')}, deviation_ratio={anomaly.get('deviation_ratio')}")
                        else:
                            self.logger.warning(f"⚠️ SuspiciousEntity创建失败: {object_name}.{anomaly.get('column', 'unknown')}")
                    except Exception as e:
                        self.logger.error(f"❌ 创建SuspiciousEntity异常: {object_name}.{anomaly.get('column', 'unknown')} -> {e}")

            except Exception as e:
                self.logger.error(f"❌ APM异常检测失败 {data_key}: {e}")

        # 🔧 调试：记录最终的suspicious_entities列表
        self.logger.info(f"🔧 APM异常检测最终返回: {len(suspicious_entities)}个SuspiciousEntity")
        emailservice_entities = [e for e in suspicious_entities if 'emailservice' in str(e.entity.service).lower() or 'emailservice' in str(e.entity.pod).lower()]
        self.logger.info(f"🔧 其中emailservice相关: {len(emailservice_entities)}个")
        for i, entity in enumerate(emailservice_entities):
            self.logger.info(f"🔧 emailservice实体{i+1}: service={entity.entity.service}, pod={entity.entity.pod}, pattern={entity.anomaly_feature.pattern}")

        return suspicious_entities

    def _detect_apm_anomalies_with_baseline_v2(self, df: pd.DataFrame, object_name: str,
                                             object_type: str, baseline: Dict[str, float]) -> List[Dict[str, Any]]:
        """使用基线数据检测APM异常 - 新版本，支持Pod级别基线"""
        anomalies = []

        try:
            self.logger.info(f"🎯 使用基线检测APM异常: {object_name} (类型: {object_type})")

            # 检测各种APM指标
            apm_metrics = {
                'error_ratio': 'error_ratio',
                'rrt': 'rrt',
                'client_error_ratio': 'client_error_ratio',
                'server_error_ratio': 'server_error_ratio',
                'timeout': 'timeout'
            }

            self.logger.info(f"📋 检测指标列表: {list(apm_metrics.keys())}")
            available_columns = [col for col in apm_metrics.keys() if col in df.columns]
            self.logger.info(f"📊 可用数据列: {available_columns}")

            for column, baseline_key in apm_metrics.items():
                if column in df.columns and baseline_key in baseline:
                    column_anomalies = self._detect_baseline_anomalies_for_column_v2(
                        df, column, object_name, baseline_key, baseline, object_type
                    )
                    anomalies.extend(column_anomalies)

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 基线异常检测失败 {object_name}: {e}")
            return []

    def _detect_baseline_anomalies_for_column_v2(self, df: pd.DataFrame, column: str,
                                               object_name: str, baseline_key: str,
                                               baseline: Dict[str, float], object_type: str) -> List[Dict[str, Any]]:
        """为单个列检测基于基线的异常 - 新版本"""
        anomalies = []

        try:
            if column not in df.columns:
                return []

            # 获取列数据
            column_data = df[column].dropna()
            if column_data.empty:
                return []

            # 计算统计值
            mean_value = column_data.mean()
            max_value = column_data.max()

            # 使用基线管理器判断异常
            for value_type, value in [('mean', mean_value), ('max', max_value)]:
                is_anomaly, severity, deviation = self.baseline_manager.is_anomaly_for_object(
                    object_name, baseline_key, value, object_type
                )

                if is_anomaly:
                    # 获取阈值信息
                    thresholds = self.baseline_manager.calculate_thresholds_for_object(object_name, object_type)
                    threshold = thresholds[severity][baseline_key] if thresholds else value

                    anomaly = {
                        'column': column,
                        'value': value,
                        'baseline_key': baseline_key,
                        'object_name': object_name,
                        'object_type': object_type,
                        'severity': severity,
                        'deviation_ratio': deviation,
                        'threshold': threshold,
                        'detection_method': 'baseline_v2',
                        'value_type': value_type,  # mean 或 max
                        'timestamp': df.index[-1] if not df.empty else None
                    }
                    anomalies.append(anomaly)

                    self.logger.info(f"🚨 基线异常检测: {object_name}.{column}({value_type})={value:.2f}, 严重程度={severity}, 偏差={deviation:.2f}x")
                    self.logger.info(f"🔧 调试threshold值: {threshold} (类型: {type(threshold)})")

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 列基线异常检测失败 {column}: {e}")
            return []

    def _extract_service_name_from_object(self, object_name: str, object_type: str) -> Optional[str]:
        """从对象名称中提取服务名称"""
        try:
            # APM对象名称通常格式为: servicename-xxx 或 servicename
            if '-' in object_name:
                service_name = object_name.split('-')[0]
            else:
                service_name = object_name

            # 验证是否为已知服务
            if service_name in self.baseline_manager.get_all_services():
                return service_name

            # 尝试其他可能的格式
            for known_service in self.baseline_manager.get_all_services():
                if known_service in object_name.lower():
                    return known_service

            return None
        except Exception as e:
            self.logger.debug(f"提取服务名称失败: {object_name} -> {e}")
            return None

    def _detect_apm_anomalies_with_baseline(self, df: pd.DataFrame, service_name: str,
                                          object_name: str, object_type: str) -> List[Dict[str, Any]]:
        """使用基线数据检测APM异常"""
        anomalies = []

        try:
            # 获取服务基线数据
            baseline = self.baseline_manager.get_service_baseline(service_name)
            if not baseline:
                return []

            self.logger.info(f"🎯 使用基线检测APM异常: {service_name}")

            # 检测各种APM指标
            apm_metrics = {
                'error_ratio': 'error_ratio',
                'rrt': 'rrt',
                'client_error_ratio': 'client_error_ratio',
                'server_error_ratio': 'server_error_ratio',
                'timeout': 'timeout'
            }

            self.logger.info(f"📋 检测指标列表: {list(apm_metrics.keys())}")
            available_columns = [col for col in apm_metrics.keys() if col in df.columns]
            self.logger.info(f"📊 可用数据列: {available_columns}")

            for column, baseline_key in apm_metrics.items():
                if column in df.columns and baseline_key in baseline:
                    column_anomalies = self._detect_baseline_anomalies_for_column(
                        df, column, service_name, baseline_key, object_name
                    )
                    anomalies.extend(column_anomalies)

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 基线异常检测失败 {service_name}: {e}")
            return []

    def _detect_baseline_anomalies_for_column(self, df: pd.DataFrame, column: str,
                                            service_name: str, baseline_key: str,
                                            object_name: str) -> List[Dict[str, Any]]:
        """为单个列检测基于基线的异常"""
        anomalies = []

        try:
            if column not in df.columns:
                return []

            # 获取列数据
            column_data = df[column].dropna()
            if column_data.empty:
                return []

            # 计算统计值
            mean_value = column_data.mean()
            max_value = column_data.max()

            # 使用基线管理器判断异常
            for value in [mean_value, max_value]:
                is_anomaly, severity, deviation = self.baseline_manager.is_anomaly(
                    service_name, baseline_key, value
                )

                if is_anomaly:
                    anomaly = {
                        'column': column,
                        'value': value,
                        'baseline_key': baseline_key,
                        'service_name': service_name,
                        'severity': severity,
                        'deviation_ratio': deviation,
                        'threshold': self.baseline_manager.calculate_service_thresholds(service_name)[severity][baseline_key],
                        'detection_method': 'baseline',
                        'object_name': object_name,
                        'timestamp': df.index[-1] if not df.empty else None
                    }
                    anomalies.append(anomaly)

                    self.logger.info(f"🚨 基线异常检测: {service_name}.{column}={value:.2f}, 严重程度={severity}, 偏差={deviation:.2f}x")

            return anomalies

        except Exception as e:
            self.logger.error(f"❌ 列基线异常检测失败 {column}: {e}")
            return []

    def _create_apm_suspicious_entity_v2(self, object_type: str, object_name: str,
                                        anomaly: Dict[str, Any], df: pd.DataFrame,
                                        request: AnalysisRequest) -> SuspiciousEntity:
        """创建APM可疑实体 - 新版本，显示正确的对象信息"""
        try:
            # 构建清晰的异常描述
            column = anomaly['column']
            value = anomaly['value']
            threshold = anomaly['threshold']
            severity = anomaly['severity']

            # 🔧 优化：支持基线异常的描述生成
            detection_method = anomaly.get('detection_method', 'dynamic')
            service_name = anomaly.get('service_name', '')
            object_name_from_anomaly = anomaly.get('object_name', '')
            object_type_from_anomaly = anomaly.get('object_type', '')

            if detection_method in ['baseline', 'baseline_v2'] and (service_name or object_name_from_anomaly):
                # 基线异常描述
                baseline_key = anomaly.get('baseline_key', column)
                deviation_ratio = anomaly.get('deviation_ratio', 1.0)
                value_type = anomaly.get('value_type', '')

                # 构建值类型描述
                value_desc = f"({value_type})" if value_type else ""

                if column == 'error_ratio' or baseline_key == 'error_ratio':
                    metric_display = f"错误率{value_desc}: {value:.2f}% (基线阈值: {threshold:.2f}%, 偏差: {deviation_ratio:.2f}x)"
                elif column == 'rrt' or baseline_key == 'rrt':
                    metric_display = f"响应时间{value_desc}: {value:.1f}ms (基线阈值: {threshold:.1f}ms, 偏差: {deviation_ratio:.2f}x)"
                elif baseline_key == 'timeout':
                    metric_display = f"超时率{value_desc}: {value:.2f}% (基线阈值: {threshold:.2f}%, 偏差: {deviation_ratio:.2f}x)"
                elif baseline_key == 'client_error_ratio':
                    metric_display = f"客户端错误率{value_desc}: {value:.2f}% (基线阈值: {threshold:.2f}%, 偏差: {deviation_ratio:.2f}x)"
                elif baseline_key == 'server_error_ratio':
                    metric_display = f"服务端错误率{value_desc}: {value:.2f}% (基线阈值: {threshold:.2f}%, 偏差: {deviation_ratio:.2f}x)"
                else:
                    metric_display = f"{column}{value_desc}: {value:.2f} (基线阈值: {threshold:.2f}, 偏差: {deviation_ratio:.2f}x)"
            else:
                # 原有的动态阈值描述
                if column == 'error_ratio':
                    metric_display = f"错误率: {value:.2%} (阈值: {threshold:.2%})"
                elif column == 'rrt':
                    metric_display = f"响应时间: {value:.1f}ms (阈值: {threshold:.1f}ms)"
                elif column == 'request':
                    metric_display = f"请求数: {value:.0f} (阈值: {threshold:.0f})"
                elif column == 'response':
                    metric_display = f"响应数: {value:.0f} (阈值: {threshold:.0f})"
                else:
                    metric_display = f"{column}: {value:.2f} (阈值: {threshold:.2f})"

            # 构建原始数据证据
            raw_data_evidence = []
            if not df.empty:
                # 取前5条记录作为证据
                evidence_df = df.head(5)
                for _, row in evidence_df.iterrows():
                    evidence = {
                        "timestamp": str(row.get('time', '')),
                        "object": object_name,
                        "metric": column,
                        "value": float(row.get(column, 0)),
                        "threshold": threshold
                    }
                    raw_data_evidence.append(evidence)

            # 🔧 正确提取服务名：redis-cart-0 -> redis-cart
            if '-' in object_name:
                parts = object_name.split('-')
                if len(parts) >= 2 and parts[-1].isdigit():
                    # 最后一部分是数字，说明是Pod实例号，服务名是前面的部分
                    service_name = '-'.join(parts[:-1])
                else:
                    # 不是标准的Pod命名，使用第一部分作为服务名
                    service_name = parts[0]
            else:
                service_name = object_name

            entity = SuspiciousEntity(
                entity=EntityInfo(
                    service=service_name,  # 正确的服务名
                    namespace="hipstershop",
                    pod=object_name  # 保留完整的Pod名称
                ),
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"{column}_anomaly",
                    confidence=0.9 if severity in ['critical', 'high'] else 0.7,
                    metric_value=value,
                    threshold=threshold,
                    metric_type=column,  # 🔧 添加metric_type字段
                    severity=severity,   # 🔧 添加severity字段
                    # 🔧 新增：基线异常的详细信息
                    detection_method=anomaly.get('detection_method', 'dynamic'),
                    deviation_ratio=anomaly.get('deviation_ratio', 1.0),
                    baseline_key=anomaly.get('baseline_key', ''),
                    value_type=anomaly.get('value_type', '')
                ),
                data_location=DataLocation(
                    file_type="apm",
                    root_dir="",
                    file_paths=[anomaly.get('file_path', '')],
                    file_matching_rule=f"{object_type}_{object_name}_apm_metrics"
                ),
                confidence=0.9 if severity in ['critical', 'high'] else 0.7,
                raw_data_records=raw_data_evidence,
                data_summary={
                    # 基础分类信息 - 供ReasoningAgent分类使用
                    "metric_category": "apm",
                    "metric_subcategory": self._get_metric_subcategory("apm", column),
                    "object_type": object_type,
                    "object_name": object_name,
                    "metric_name": column,
                    "metric_display_name": self._get_metric_display_name(column),
                    "metric_unit": self._get_metric_unit(column),

                    # 异常数值信息
                    "current_value": value,
                    "threshold": threshold,
                    "deviation_ratio": value / threshold if threshold > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": severity,

                    # 业务上下文信息
                    "service_criticality": self._get_service_criticality(object_name),
                    "metric_impact_type": self._get_metric_impact_type("apm", column),
                    "affected_layer": "application_layer",

                    # 兼容性字段
                    "metric": column,
                    "metric_display": metric_display,
                    "value": value,
                    "severity": severity,
                    "p95": anomaly.get('p95', 0),
                    "p99": anomaly.get('p99', 0),

                    # 🔧 新增：时间分析信息
                    "temporal_analysis": self._analyze_temporal_patterns(df, column, anomaly),

                    # 🎯 新增：APM业务语义异常分析结果 - 只在Pod级别进行，避免重复
                    **self._add_apm_business_anomaly_analysis(df, service_name, object_type)

                    # 🔧 修复：APM层不应该包含断点检测，断点只能来自infra层
                    # **self._add_pod_breakpoint_anomaly_analysis(df, object_name, object_type)
                }
            )

            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建APM可疑实体失败: {e}")
            return None

    def _add_apm_business_anomaly_analysis(self, df: pd.DataFrame, service_name: str, object_type: str) -> Dict[str, Any]:
        """为APM可疑实体添加业务语义异常分析结果 - 🔧 修复重复问题"""
        try:
            # 🔧 修复：只在Pod级别进行APM业务语义异常分析，避免Service级别重复
            if object_type != 'pod':
                self.logger.info(f"🔧 跳过APM业务语义异常分析: object_type={object_type} (只在Pod级别进行)")
                return {"data_summary": {}}

            self.logger.info(f"🎯 开始APM业务语义异常分析: service_name={service_name}, object_type={object_type}, df_shape={df.shape}")

            # 调用APM业务语义异常分析（通过threshold_manager）
            apm_business_anomalies = self.threshold_manager.analyze_apm_business_anomalies(df, service_name)

            self.logger.info(f"🎯 APM业务异常分析结果: severity={apm_business_anomalies.get('overall_severity', 'normal')}, score={apm_business_anomalies.get('severity_score', 0)}")

            # 返回增强的data_summary字段
            return {
                "apm_business_anomalies": apm_business_anomalies if apm_business_anomalies else {},
                "has_packet_loss": bool(apm_business_anomalies.get('packet_loss_anomalies')),
                "has_load_latency_issues": bool(apm_business_anomalies.get('load_latency_anomalies')),
                "has_error_spikes": bool(apm_business_anomalies.get('error_spike_anomalies')),
                "has_timeout_anomalies": bool(apm_business_anomalies.get('timeout_anomalies')),
                "has_consistency_anomalies": bool(apm_business_anomalies.get('consistency_anomalies')),
                "apm_severity_score": apm_business_anomalies.get('severity_score', 0),
                "has_apm_anomalies": bool(apm_business_anomalies.get('severity_score', 0) > 0)
            }

        except Exception as e:
            self.logger.error(f"❌ APM业务语义异常分析失败: {e}")
            return {
                "apm_business_anomalies": {},
                "has_packet_loss": False,
                "has_load_latency_issues": False,
                "has_error_spikes": False,
                "has_timeout_anomalies": False,
                "has_consistency_anomalies": False,
                "apm_severity_score": 0,
                "has_apm_anomalies": False
            }

    def _add_pod_breakpoint_anomaly_analysis(self, df: pd.DataFrame, object_name: str, object_type: str) -> Dict[str, Any]:
        """为基础设施可疑实体添加Pod断点异常分析结果 - 🔧 按照APM业务异常的模式，显示时间范围"""
        try:
            # 🔧 只在Pod级别进行断点异常分析
            if object_type != 'pod':
                return {}

            self.logger.info(f"🎯 开始Pod断点异常分析: object_name={object_name}, object_type={object_type}, df_shape={df.shape}")

            # 🔧 重新设计：从所有Pod指标中检测断点，而不是单一DataFrame
            # 需要加载该Pod的所有指标数据来检测断点
            all_pod_breakpoints = []

            # 获取该Pod的所有指标数据
            pod_metrics = ['cpu_usage', 'memory_working_set_bytes', 'fs_reads_bytes', 'fs_writes_bytes',
                          'network_receive_bytes', 'network_transmit_bytes', 'network_receive_packets',
                          'network_transmit_packets', 'processes']

            for metric_name in pod_metrics:
                try:
                    # 构建指标文件路径（模拟从实际数据中检测）
                    metric_key = f"pod_pod_{metric_name}"

                    # 模拟断点检测（实际应该从真实数据中检测）
                    # 这里我们检查是否有该指标的断点
                    breakpoints = self._detect_pod_metric_breakpoints(object_name, metric_name)

                    if breakpoints:
                        for bp in breakpoints:
                            bp['metric_name'] = metric_name
                            bp['pod_name'] = object_name
                        all_pod_breakpoints.extend(breakpoints)

                except Exception as e:
                    self.logger.debug(f"检测Pod {object_name} 指标 {metric_name} 断点失败: {e}")
                    continue

            if not all_pod_breakpoints:
                self.logger.info(f"🔍 Pod {object_name} 未检测到断点异常")
                return {}

            # 🔧 按照APM业务异常的格式构建断点异常信息，重点显示时间范围
            pod_breakpoint_anomalies = {
                'breakpoint_events': all_pod_breakpoints[:5],  # 最多保留5个断点
                'breakpoint_count': len(all_pod_breakpoints),
                'max_severity': max(bp.get('severity', 'medium') for bp in all_pod_breakpoints),
                'description': f'检测到{len(all_pod_breakpoints)}个Pod断点异常',
                'overall_severity': 'high' if len(all_pod_breakpoints) > 5 else 'medium' if len(all_pod_breakpoints) > 2 else 'low',
                'time_ranges': self._extract_breakpoint_time_ranges(all_pod_breakpoints),
                'affected_metrics': list(set(bp.get('metric_name', 'unknown') for bp in all_pod_breakpoints))
            }

            self.logger.info(f"🎯 Pod断点异常分析结果: severity={pod_breakpoint_anomalies['overall_severity']}, count={len(all_pod_breakpoints)}, metrics={pod_breakpoint_anomalies['affected_metrics']}")

            # 🔧 按照APM业务异常的返回格式
            return {
                "pod_breakpoint_anomalies": pod_breakpoint_anomalies,
                "has_breakpoint_anomalies": True,
                "breakpoint_severity": pod_breakpoint_anomalies['max_severity'],
                "breakpoint_count": len(all_pod_breakpoints)
            }

        except Exception as e:
            self.logger.error(f"❌ Pod断点异常分析失败: {e}")
            return {}

    def _detect_pod_metric_breakpoints(self, pod_name: str, metric_name: str) -> List[Dict[str, Any]]:
        """检测特定Pod的特定指标的断点 - 🔧 修复：只检测真正的数据缺失，不返回硬编码断点"""
        try:
            # 🔧 修复：断点检测应该基于真实的数据分析，而不是硬编码的示例数据
            # 移除所有硬编码的断点返回，只有在真正检测到数据缺失时才返回断点

            # TODO: 这里应该实现真正的断点检测逻辑，基于实际的指标数据
            # 目前暂时返回空列表，避免误报

            return []

        except Exception as e:
            self.logger.error(f"❌ 检测Pod {pod_name} 指标 {metric_name} 断点失败: {e}")
            return []

    def _extract_breakpoint_time_ranges(self, breakpoints: List[Dict[str, Any]]) -> List[str]:
        """提取断点的时间范围信息 - 🔧 格式化为可读的时间范围"""
        try:
            time_ranges = []

            for bp in breakpoints:
                if bp.get('breakpoint_type') == 'time_gap':
                    start_time = bp.get('gap_start', '')
                    end_time = bp.get('gap_end', '')
                    duration = bp.get('duration_minutes', 0)

                    if start_time and end_time:
                        # 提取时间部分 (HH:MM)
                        start_hm = start_time.split(' ')[1][:5] if ' ' in start_time else start_time[:5]
                        end_hm = end_time.split(' ')[1][:5] if ' ' in end_time else end_time[:5]
                        time_ranges.append(f"{start_hm}-{end_hm}({duration}分钟)")

                elif bp.get('breakpoint_type') == 'data_zero_transition':
                    start_time = bp.get('zero_start', '')
                    end_time = bp.get('zero_end', '')
                    duration = bp.get('duration_minutes', 0)

                    if start_time and end_time:
                        start_hm = start_time.split(' ')[1][:5] if ' ' in start_time else start_time[:5]
                        end_hm = end_time.split(' ')[1][:5] if ' ' in end_time else end_time[:5]
                        time_ranges.append(f"{start_hm}-{end_hm}({duration}分钟,归零)")

            return time_ranges

        except Exception as e:
            self.logger.error(f"❌ 提取断点时间范围失败: {e}")
            return []

    def _create_infra_suspicious_entity_v2(self, object_type: str, object_name: str,
                                          anomaly: Dict[str, Any], df: pd.DataFrame,
                                          request: AnalysisRequest, pod_breakpoint_anomalies: Dict[str, Any] = None) -> SuspiciousEntity:
        """创建基础设施可疑实体 - 新版本，显示正确的对象信息"""
        try:
            # 构建清晰的异常描述
            column = anomaly['column']
            value = anomaly['value']
            threshold = anomaly['threshold']
            severity = anomaly['severity']
            metric_type = anomaly['metric_name']

            # 根据指标类型生成友好的描述
            if column == 'cpu_usage':
                metric_display = f"CPU使用率: {value:.2f}% (阈值: {threshold:.2f}%)"
            elif column == 'memory_usage':
                if value > 1024*1024*1024:  # GB
                    metric_display = f"内存使用: {value/(1024*1024*1024):.1f}GB (阈值: {threshold/(1024*1024*1024):.1f}GB)"
                else:
                    metric_display = f"内存使用: {value:.0f}MB (阈值: {threshold:.0f}MB)"
            elif column == 'value':
                metric_display = f"{metric_type}: {value:.2f} (阈值: {threshold:.2f})"
            else:
                metric_display = f"{column}: {value:.2f} (阈值: {threshold:.2f})"

            # 构建原始数据证据
            raw_data_evidence = []
            if not df.empty:
                # 取前5条记录作为证据
                evidence_df = df.head(5)
                for _, row in evidence_df.iterrows():
                    evidence = {
                        "timestamp": str(row.get('time', '')),
                        "object": object_name,
                        "metric": column,
                        "value": float(row.get(column, 0)),
                        "threshold": threshold
                    }
                    raw_data_evidence.append(evidence)

            # 🚀 修复：从实际数据中提取命名空间信息
            namespace_name = "system"  # 默认命名空间
            if not df.empty:
                first_row = df.iloc[0]
                # 尝试从数据中提取命名空间信息
                if 'namespace' in df.columns and pd.notna(first_row.get('namespace')) and first_row['namespace'] != 'null':
                    namespace_name = str(first_row['namespace'])
                elif object_type == 'pod':
                    # 对于Pod，如果没有明确的命名空间，可能是应用Pod
                    namespace_name = "hipstershop"

                self.logger.debug(f"🔍 基础设施实体命名空间: {object_type}={object_name} -> namespace={namespace_name}")

            entity = SuspiciousEntity(
                entity=EntityInfo(
                    service=f"{object_type}_infra",
                    namespace=namespace_name,
                    pod=object_name
                ),
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"{column}_infra_anomaly",
                    confidence=0.8 if severity in ['critical', 'high'] else 0.6,
                    metric_value=value,
                    threshold=threshold,
                    metric_type=column,  # 🔧 添加metric_type字段
                    severity=severity    # 🔧 添加severity字段
                ),
                data_location=DataLocation(
                    file_type="infra",
                    root_dir="",
                    file_paths=[anomaly.get('file_path', '')],
                    file_matching_rule=f"infra_{object_type}_{column}"
                ),
                confidence=0.8 if severity in ['critical', 'high'] else 0.6,
                raw_data_records=raw_data_evidence,
                data_summary={
                    # 基础分类信息 - 供ReasoningAgent分类使用
                    "metric_category": "infra",
                    "metric_subcategory": self._get_metric_subcategory("infra", metric_type),
                    "object_type": object_type,
                    "object_name": object_name,
                    "metric_name": metric_type,
                    "metric_display_name": self._get_metric_display_name(metric_type),
                    "metric_unit": self._get_metric_unit(metric_type),

                    # 异常数值信息
                    "current_value": value,
                    "threshold": threshold,
                    "deviation_ratio": value / threshold if threshold > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": severity,

                    # 业务上下文信息
                    "service_criticality": self._get_service_criticality(object_name),
                    "metric_impact_type": self._get_metric_impact_type("infra", metric_type),
                    "affected_layer": "infrastructure_layer",

                    # 兼容性字段
                    "metric": column,
                    "metric_display": metric_display,
                    "value": value,
                    "severity": severity,
                    "p95": anomaly.get('p95', 0),
                    "p99": anomaly.get('p99', 0),

                    # 🔧 新增：时间分析信息
                    "temporal_analysis": self._analyze_temporal_patterns(df, column, anomaly),

                    # 🔧 新增：Pod断点异常信息（按照APM业务异常的模式）
                    "pod_breakpoint_anomalies": pod_breakpoint_anomalies if pod_breakpoint_anomalies else {},
                    "has_breakpoint_anomalies": bool(pod_breakpoint_anomalies),
                    "breakpoint_severity": pod_breakpoint_anomalies.get('max_severity', 'normal') if pod_breakpoint_anomalies else 'normal'
                }
            )

            # 🔧 添加断点信息传递日志
            if pod_breakpoint_anomalies:
                self.logger.info(f"✅ 【断点检测日志6】SuspiciousEntity已创建，包含断点信息: {object_name}, 断点数量: {pod_breakpoint_anomalies.get('breakpoint_count', 0)}")
            else:
                self.logger.debug(f"🔍 【断点检测日志7】SuspiciousEntity已创建，无断点信息: {object_name}")

            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建Infra可疑实体失败: {e}")
            return None

    def _detect_apm_metric_anomalies(self, df: pd.DataFrame, object_type: str, object_name: str) -> List[Dict[str, Any]]:
        """检测APM指标异常 - 使用统一阈值管理器"""
        anomalies = []

        # 检测DataFrame中的所有指标
        for metric in df.columns:
            if metric in ['timestamp', 'time']:  # 跳过时间列
                continue

            metric_data = df[metric].dropna()
            if not metric_data.empty:
                try:
                    # 🚀 使用优化的阈值管理器获取阈值
                    threshold, threshold_type, threshold_metadata = self.threshold_manager.get_intelligent_threshold(
                        category='apm',
                        metric_name=metric,
                        object_name=object_name,
                        historical_data=metric_data
                    )

                    # 使用95分位数进行异常检测
                    p95_value = metric_data.quantile(0.95)
                    max_value = metric_data.max()

                    # 🚀 简化的异常判断
                    is_anomaly = p95_value > threshold

                    if is_anomaly:
                        anomalies.append({
                            'metric': metric,
                            'value': p95_value,
                            'max_value': max_value,
                            'threshold': threshold,
                            'threshold_type': threshold_type,
                            'threshold_source': threshold_metadata.get('source', 'unknown'),
                            'severity': self._calculate_severity(p95_value, threshold),
                            'anomaly_count': (metric_data > threshold).sum(),
                            'object_type': object_type,
                            'object_name': object_name
                        })

                        self.logger.debug(f"🚨 APM异常: {object_name}.{metric} = {p95_value:.4f} > {threshold:.4f} ({threshold_type})")

                except Exception as e:
                    self.logger.debug(f"APM指标{metric}异常检测失败: {e}")

        return anomalies

    async def _detect_infra_anomalies(self, infra_data: Dict[str, pd.DataFrame], request: AnalysisRequest) -> List[SuspiciousEntity]:
        """检测基础设施异常 - Node/Pod级别"""
        suspicious_entities = []

        for data_key, df in infra_data.items():
            # 跳过元数据条目
            if data_key.endswith('_metadata') or not isinstance(df, pd.DataFrame) or df.empty:
                continue

            try:
                # 🔧 修复：从元数据中获取真实的对象信息
                metadata_key = data_key + '_metadata'
                if metadata_key in infra_data:
                    metadata = infra_data[metadata_key]
                    object_type = metadata['object_type']
                    metric_type = metadata['metric_type']
                    object_names = metadata['object_names']
                else:
                    # 兜底：使用旧的方式
                    object_type = 'unknown'
                    metric_type = 'unknown'
                    object_names = ['unknown']

                self.logger.debug(f"🔍 分析Infra对象: {object_names} ({object_type}) - {metric_type}")

                # 🔧 修复：按实际对象名称分组检测异常
                for object_name in object_names:
                    # 过滤出该对象的数据
                    if object_type == 'node' and 'kubernetes_node' in df.columns:
                        object_df = df[df['kubernetes_node'] == object_name]
                    elif object_type == 'pod' and 'pod' in df.columns:
                        object_df = df[df['pod'] == object_name]
                    else:
                        object_df = df  # 兜底：使用全部数据

                    if object_df.empty:
                        continue

                    self.logger.debug(f"🔍 分析具体对象: {object_name} ({len(object_df)} 条记录)")

                    # 🚀 修复重复检测：智能选择最佳列进行异常检测
                    object_anomalies = []

                    # 按优先级定义列名候选列表
                    column_candidates = [
                        # 优先使用具体的指标列名
                        metric_type,  # 使用metric_type作为列名（最高优先级）
                        # Node级别指标
                        'node_cpu_usage_rate', 'node_memory_usage_rate',
                        'node_disk_write_bytes_total', 'node_disk_read_bytes_total',
                        'node_network_transmit_bytes', 'node_network_receive_bytes',
                        # Pod级别指标
                        'pod_cpu_usage', 'pod_memory_working_set_bytes',
                        'pod_network_transmit_bytes', 'pod_network_receive_bytes',
                        'pod_fs_writes_bytes', 'pod_fs_reads_bytes',
                        'pod_processes',
                        # 通用列名（最低优先级）
                        'cpu_usage', 'memory_usage', 'value'
                    ]

                    # 🔧 关键修复：只选择第一个存在的列进行检测，避免重复
                    selected_column = None
                    for column in column_candidates:
                        if column and column in object_df.columns:
                            selected_column = column
                            break

                    if selected_column:
                        self.logger.debug(f"🔍 检测对象 {object_name} 列 {selected_column} 的异常")
                        column_anomalies = self._detect_column_anomalies_with_dynamic_threshold(
                            object_df, selected_column, object_name, metric_type, 'infra', ''
                        )
                        object_anomalies.extend(column_anomalies)
                        self.logger.debug(f"🔍 对象 {object_name} 列 {selected_column} 发现 {len(column_anomalies)} 个异常")
                    else:
                        self.logger.warning(f"⚠️ 对象 {object_name} 没有找到可检测的列，可用列: {list(object_df.columns)}")

                    # 🔧 修复：Pod断点检测 - 按照APM业务异常的模式
                    pod_breakpoint_anomalies = {}
                    if object_type == 'pod':
                        self.logger.info(f"🔍 【断点检测日志1】开始为Pod {object_name} 执行断点检测")
                        breakpoints = self._detect_pod_data_breakpoints(object_df, object_name, 'infra')
                        self.logger.info(f"🔍 【断点检测日志2】Pod {object_name} 断点检测结果: {len(breakpoints)}个断点")

                        if breakpoints:
                            # 🔧 按照APM业务异常的格式存储断点信息
                            pod_breakpoint_anomalies = {
                                'breakpoint_events': breakpoints[:5],  # 最多保留5个断点
                                'breakpoint_count': len(breakpoints),
                                'max_severity': max(bp.get('severity', 'low') for bp in breakpoints),
                                'description': f'检测到{len(breakpoints)}个Pod断点异常'
                            }
                            self.logger.info(f"✅ 【断点检测日志3】Pod {object_name} 断点异常已记录: {pod_breakpoint_anomalies['description']}")
                            self.logger.info(f"✅ 【断点检测日志4】断点详情: {breakpoints[:2]}")  # 显示前2个断点的详情
                        else:
                            self.logger.info(f"❌ 【断点检测日志5】Pod {object_name} 未检测到断点异常")

                    # 🚀 去重：为每个异常创建SuspiciousEntity，避免重复
                    for anomaly in object_anomalies:
                        entity = self._create_infra_suspicious_entity_v2(
                            object_type, object_name, anomaly, object_df, request, pod_breakpoint_anomalies
                        )
                        if entity:
                            suspicious_entities.append(entity)

            except Exception as e:
                self.logger.error(f"❌ Infra异常检测失败 {data_key}: {e}")

        return suspicious_entities

    async def _detect_tidb_anomalies(self, tidb_data: Dict[str, pd.DataFrame], request: AnalysisRequest) -> List[SuspiciousEntity]:
        """检测TiDB数据库异常"""
        suspicious_entities = []

        for data_key, df in tidb_data.items():
            if df.empty:
                continue

            # 解析指标名称: tidb_qps -> qps
            if data_key.startswith('tidb_'):
                metric_name = data_key[5:]  # 移除'tidb_'前缀
            else:
                continue

            # 🔧 特殊处理：TiDB的分类型指标（qps）
            if metric_name in ['qps']:
                tidb_anomalies = self._detect_tidb_typed_metric_anomalies(df, metric_name, request)
                for anomaly in tidb_anomalies:
                    entity = self._create_tidb_typed_suspicious_entity(
                        metric_name, anomaly, df, request
                    )
                    if entity:
                        suspicious_entities.append(entity)
                continue  # 跳过常规处理

            # 🚀 使用统一阈值管理器检测TiDB指标异常
            try:
                # 🚀 修复：TiDB对象名称固定为tidb，不从instance动态生成
                object_name = 'tidb'  # 固定对象名，不使用动态IP

                self.logger.debug(f"🔍 TiDB对象名称: {object_name} (固定命名)")

                # 获取优化的阈值
                threshold, threshold_type, threshold_metadata = self.threshold_manager.get_intelligent_threshold(
                    category='tidb',
                    metric_name=metric_name,
                    object_name=object_name,
                    historical_data=df[metric_name] if metric_name in df.columns else None
                )

                anomalies = self._detect_single_metric_anomaly(
                    df, metric_name, threshold, 'tidb', request.start_time
                )

                for anomaly in anomalies:
                    entity = self._create_tidb_suspicious_entity(
                        metric_name, anomaly, df, request
                    )
                    if entity:
                        suspicious_entities.append(entity)

            except Exception as e:
                self.logger.error(f"❌ TiDB指标异常检测失败 {metric_name}: {e}")
                continue

        return suspicious_entities

    def _create_tidb_suspicious_entity(self, metric_name: str, anomaly: Dict[str, Any],
                                     df: pd.DataFrame, request: AnalysisRequest) -> Optional[SuspiciousEntity]:
        """创建TiDB可疑实体"""
        try:
            value = anomaly['value']
            threshold = anomaly['threshold']
            severity = anomaly.get('severity', 'medium')

            # 使用UnitNormalizer进行统一的格式化显示
            value_str = UnitNormalizer.format_value_with_unit(value, metric_name)
            threshold_str = UnitNormalizer.format_value_with_unit(threshold, metric_name)
            metric_display = f"{metric_name}: {value_str} (阈值: {threshold_str})"

            # 🚀 修复：TiDB组件固定命名，不从instance动态生成
            service_name = "tidb"  # 固定为tidb，不使用动态IP
            namespace_name = "default"  # 使用default命名空间
            pod_name = None  # 默认Pod名

            if not df.empty:
                # 从实际数据中提取实体信息（仅提取namespace和pod，不改变service_name）
                first_row = df.iloc[0]

                # 提取命名空间信息（如果存在且不为null）
                if 'namespace' in df.columns and pd.notna(first_row.get('namespace')) and first_row['namespace'] != 'null':
                    namespace_name = str(first_row['namespace'])

                # 提取Pod信息（如果存在且不为null）
                if 'pod' in df.columns and pd.notna(first_row.get('pod')) and first_row['pod'] != 'null':
                    pod_name = str(first_row['pod'])

                self.logger.debug(f"🔍 TiDB实体信息: service={service_name}, namespace={namespace_name}, pod={pod_name}")

            entity = SuspiciousEntity(
                entity=EntityInfo(
                    service=service_name,
                    namespace=namespace_name,
                    pod=pod_name
                ),
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"{metric_name}_tidb_anomaly",
                    confidence=0.9,
                    metric_value=value,
                    threshold=threshold,
                    metric_type=metric_name,
                    severity=severity
                ),
                data_location=DataLocation(
                    file_type="tidb",
                    root_dir="",
                    file_paths=[],
                    file_matching_rule=f"infra_tidb_{metric_name}"
                ),
                confidence=0.9,
                raw_data_records=[],
                data_summary={
                    # 🔧 修复：TiDB应该使用tidb类别，不是infra类别
                    "metric_category": "tidb",
                    "metric_subcategory": self._get_metric_subcategory("tidb", metric_name),
                    "object_type": "tidb",
                    "object_name": service_name,  # 修复：使用动态提取的服务名
                    "metric_name": metric_name,
                    "metric_display_name": self._get_metric_display_name(metric_name),
                    "metric_unit": self._get_metric_unit(metric_name),

                    # 异常数值信息
                    "current_value": value,
                    "threshold": threshold,
                    "deviation_ratio": value / threshold if threshold > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": severity,

                    # 业务上下文信息
                    "service_criticality": "critical",  # 数据库总是关键的
                    "metric_impact_type": "data_layer",
                    "affected_layer": "data_layer",

                    # 兼容性字段
                    "metric": metric_name,
                    "metric_display": metric_display,
                    "value": value,
                    "severity": severity,
                    "p95_value": value,
                }
            )

            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建TiDB可疑实体失败: {e}")
            return None

    async def _detect_other_anomalies(self, other_data: Dict[str, pd.DataFrame], request: AnalysisRequest) -> List[SuspiciousEntity]:
        """检测其他组件异常 - PD/TiKV级别"""
        suspicious_entities = []

        self.logger.info(f"🔍 开始检测Other组件异常，数据键: {list(other_data.keys())}")

        # 🚀 检测TiKV/PD组件数据缺失异常
        expected_components = ['tikv', 'pd']
        loaded_components = set()
        components_with_fault_data = set()

        for data_key, df in other_data.items():
            component = data_key.split('_')[0]
            if component in expected_components:
                loaded_components.add(component)

                # 检查故障时间段内是否有数据
                if not df.empty and 'time' in df.columns:
                    df_time_filtered = self._filter_data_by_time_range(df, TimeRange(start=request.start_time, end=request.end_time))
                    if not df_time_filtered.empty:
                        components_with_fault_data.add(component)

        # 检测完全缺失的组件
        missing_components = set(expected_components) - loaded_components
        for missing_component in missing_components:
            self.logger.warning(f"🚨 {missing_component} 组件数据完全缺失，可能服务不可用")
            missing_entity = self._create_missing_component_entity(missing_component, request)
            if missing_entity:
                suspicious_entities.append(missing_entity)

        # 检测故障期间数据缺失的组件
        fault_missing_components = loaded_components - components_with_fault_data
        for fault_missing_component in fault_missing_components:
            self.logger.warning(f"🚨 {fault_missing_component} 组件在故障期间数据缺失，可能服务中断")
            missing_entity = self._create_missing_component_entity(fault_missing_component, request)
            if missing_entity:
                suspicious_entities.append(missing_entity)

        for data_key, df in other_data.items():
            if df.empty:
                self.logger.warning(f"⚠️ {data_key} 数据为空，跳过")
                continue

            # 解析组件类型和指标名称
            parts = data_key.split('_', 1)
            if len(parts) != 2:
                self.logger.warning(f"⚠️ {data_key} 格式错误，跳过")
                continue

            component_type = parts[0]  # pd, tikv
            metric_name = parts[1]     # leader_count, cpu_usage等

            self.logger.info(f"🔍 处理组件: {component_type}, 指标: {metric_name}, 数据行数: {len(df)}")

            # 🔧 特殊处理：PD的分类型指标（region_health, abnormal_region_count）
            if component_type == 'pd' and metric_name in ['region_health', 'abnormal_region_count']:
                pd_anomalies = self._detect_pd_typed_metric_anomalies(df, metric_name, request)
                for anomaly in pd_anomalies:
                    entity = self._create_other_suspicious_entity(
                        component_type, metric_name, anomaly, df, request
                    )
                    if entity:
                        suspicious_entities.append(entity)
                continue  # 跳过常规处理

            # 🔧 特殊处理：TiKV的分类型指标（qps, grpc_qps）
            if component_type == 'tikv' and metric_name in ['qps', 'grpc_qps']:
                tikv_anomalies = self._detect_tikv_typed_metric_anomalies(df, metric_name, request)
                for anomaly in tikv_anomalies:
                    entity = self._create_other_suspicious_entity(
                        component_type, metric_name, anomaly, df, request
                    )
                    if entity:
                        suspicious_entities.append(entity)
                continue  # 跳过常规处理

            # 🔧 特殊处理：TiDB的分类型指标（qps）
            if component_type == 'tidb' and metric_name in ['qps']:
                tidb_anomalies = self._detect_tidb_typed_metric_anomalies(df, metric_name, request)
                for anomaly in tidb_anomalies:
                    entity = self._create_other_suspicious_entity(
                        component_type, metric_name, anomaly, df, request
                    )
                    if entity:
                        suspicious_entities.append(entity)
                continue  # 跳过常规处理

            # 检测组件指标异常 - 使用优化的阈值管理器
            # 🔧 修复：使用threshold_manager获取阈值
            thresholds = self.threshold_manager.default_thresholds.get('other', {}).get(component_type, {})

            self.logger.debug(f"🔍 {component_type} 可用阈值: {list(thresholds.keys())}")

            if metric_name in thresholds:
                threshold = thresholds[metric_name]
                self.logger.info(f"🔍 检测{component_type}组件异常: {metric_name} (阈值: {threshold})")
                anomalies = self._detect_single_metric_anomaly(df, metric_name, threshold, component_type, request.start_time)

                self.logger.info(f"📊 {component_type}.{metric_name} 发现 {len(anomalies)} 个异常")

                for anomaly in anomalies:
                    entity = self._create_other_suspicious_entity(
                        component_type, metric_name, anomaly, df, request
                    )
                    if entity:
                        suspicious_entities.append(entity)
                        self.logger.info(f"✅ 创建{component_type}可疑实体: {metric_name}")
            else:
                self.logger.warning(f"⚠️ {component_type}.{metric_name} 没有配置阈值，可用阈值: {list(thresholds.keys())}")

        self.logger.info(f"📊 Other组件异常检测完成，发现 {len(suspicious_entities)} 个可疑实体")
        return suspicious_entities

    def _create_missing_component_entity(self, component_type: str, request: AnalysisRequest) -> SuspiciousEntity:
        """创建缺失组件的可疑实体"""
        try:
            # 创建数据缺失异常
            anomaly = {
                'value': 0,  # 数据缺失
                'threshold': 1,  # 期望有数据
                'timestamp': request.start_time,
                'severity': 'critical'
            }

            entity = SuspiciousEntity(
                entity=EntityInfo(
                    service=component_type,
                    namespace="default",
                    pod=None
                ),
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"{component_type}_service_unavailable",
                    confidence=0.9,  # 数据完全缺失，置信度很高
                    metric_value=0,
                    threshold=1,
                    metric_type="service_availability"
                ),
                data_summary={
                    "metric_category": "other",
                    "metric_subcategory": "service_availability",
                    "object_type": component_type,
                    "object_name": component_type,
                    "anomaly_type": "service_unavailable",
                    "severity": "critical",
                    "description": f"{component_type} service completely unavailable - no monitoring data",
                    "impact_assessment": {
                        "impact_level": "critical" if component_type == "tikv" else "high",
                        "description": f"{component_type} service unavailable causing data layer failures",
                        "affected_services": ["tidb", "frontend", "productcatalogservice"] if component_type == "tikv" else ["tidb"]
                    }
                },
                raw_data_evidence=[{
                    "timestamp": request.start_time,
                    "object": component_type,
                    "metric": "service_availability",
                    "value": 0,
                    "threshold": 1,
                    "description": f"No monitoring data available for {component_type} during fault period"
                }],
                statistical_analysis={
                    "data_points": 0,
                    "missing_data_duration": "entire_fault_period",
                    "severity_assessment": "critical_service_failure"
                },
                confidence=0.9,
                data_location=f"other/{component_type}_missing"
            )

            self.logger.info(f"✅ 创建{component_type}服务不可用实体")
            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建{component_type}缺失实体失败: {e}")
            return None

    def _detect_pd_typed_metric_anomalies(self, df: pd.DataFrame, metric_name: str, request: AnalysisRequest) -> List[Dict[str, Any]]:
        """🔧 新增：检测PD分类型指标异常（region_health, abnormal_region_count）"""
        anomalies = []

        try:
            if 'type' not in df.columns:
                self.logger.warning(f"⚠️ PD {metric_name} 数据缺少type列")
                return anomalies

            # 🔧 使用优化的阈值管理器，基于实际数据校准
            # 按type分组检测异常
            for region_type in df['type'].unique():
                type_data = df[df['type'] == region_type]
                if type_data.empty:
                    continue

                # 🔧 使用智能阈值管理器获取type特定阈值
                historical_data = type_data[metric_name] if metric_name in type_data.columns else None
                threshold, threshold_type, metadata = self.threshold_manager.get_intelligent_threshold(
                    'other', metric_name, f'pd_{region_type}', historical_data, region_type
                )
                data_column = f'{metric_name}'  # region_health 或 abnormal_region_count

                if data_column not in type_data.columns:
                    continue

                # 检测超过阈值的数据点
                anomalous_data = type_data[type_data[data_column] > threshold]

                if not anomalous_data.empty:
                    max_value = anomalous_data[data_column].max()
                    anomaly_count = len(anomalous_data)

                    # 计算严重程度
                    deviation_ratio = max_value / threshold if threshold > 0 else 1
                    if deviation_ratio > 3:
                        severity = 'critical'
                    elif deviation_ratio > 2:
                        severity = 'high'
                    else:
                        severity = 'medium'

                    anomalies.append({
                        'value': max_value,
                        'threshold': threshold,
                        'threshold_type': threshold_type,
                        'threshold_metadata': metadata,
                        'severity': severity,
                        'anomaly_count': anomaly_count,
                        'region_type': region_type,
                        'type_specific_threshold': True,  # 标记使用了type特定阈值
                        'metric_display': f"{region_type}: {max_value} (阈值: {threshold:.2f}, {threshold_type})",
                        'timestamp': anomalous_data.iloc[0]['time'] if 'time' in anomalous_data.columns else request.start_time,
                        'description': f"PD {region_type} region异常: {metric_name}={max_value:.2f} > {threshold:.2f} ({threshold_type})"
                    })

                    self.logger.info(f"🚨 PD {metric_name} 异常: {region_type}={max_value:.2f} > {threshold:.2f} ({threshold_type})")

        except Exception as e:
            self.logger.error(f"❌ PD分类型指标异常检测失败: {e}")

        return anomalies

    def _detect_tikv_typed_metric_anomalies(self, df: pd.DataFrame, metric_name: str, request: AnalysisRequest) -> List[Dict[str, Any]]:
        """🔧 新增：检测TiKV分类型指标异常（qps, grpc_qps）"""
        anomalies = []

        try:
            if 'type' not in df.columns:
                self.logger.warning(f"⚠️ TiKV {metric_name} 数据缺少type列")
                return anomalies

            # TiKV QPS类型阈值配置
            tikv_type_thresholds = {
                # QPS操作类型阈值
                'batch_get': 1.0,                   # batch_get > 1 QPS
                'get': 5.0,                         # get > 5 QPS
                'scan': 2.0,                        # scan > 2 QPS
                'prewrite': 1.0,                    # prewrite > 1 QPS
                'commit': 1.0,                      # commit > 1 QPS
                'rollback': 0.5,                    # rollback > 0.5 QPS
                'acquire_pessimistic_lock': 0.1,    # 悲观锁 > 0.1 QPS

                # gRPC QPS操作类型阈值
                'kv_get': 8.0,                      # kv_get > 8 QPS (实际10-11)
                'kv_batch_get': 1.0,                # kv_batch_get > 1 QPS
                'kv_prewrite': 0.1,                 # kv_prewrite > 0.1 QPS
                'kv_commit': 0.1,                   # kv_commit > 0.1 QPS
                'kv_scan': 1.0,                     # kv_scan > 1 QPS
                'kv_pessimistic_lock': 0.1,         # 悲观锁 > 0.1 QPS
            }

            # 按type分组检测异常
            for op_type in df['type'].unique():
                if pd.isna(op_type) or op_type is None:
                    continue

                # 查找匹配的阈值
                threshold = None
                for pattern, thresh in tikv_type_thresholds.items():
                    if pattern in str(op_type).lower():
                        threshold = thresh
                        break

                if threshold is None:
                    continue  # 没有配置阈值的操作类型跳过

                type_data = df[df['type'] == op_type]
                if type_data.empty:
                    continue

                data_column = metric_name  # qps 或 grpc_qps

                if data_column not in type_data.columns:
                    continue

                # 检测超过阈值的数据点
                anomalous_data = type_data[type_data[data_column] > threshold]

                if not anomalous_data.empty:
                    max_value = anomalous_data[data_column].max()
                    anomaly_count = len(anomalous_data)

                    # 计算严重程度
                    deviation_ratio = max_value / threshold if threshold > 0 else 1
                    if deviation_ratio > 3:
                        severity = 'critical'
                    elif deviation_ratio > 2:
                        severity = 'high'
                    else:
                        severity = 'medium'

                    anomalies.append({
                        'value': max_value,
                        'threshold': threshold,
                        'severity': severity,
                        'anomaly_count': anomaly_count,
                        'operation_type': op_type,
                        'metric_display': f"{op_type}操作: {max_value:.2f} QPS (阈值: {threshold})",
                        'timestamp': anomalous_data.iloc[0]['time'] if 'time' in anomalous_data.columns else request.start_time
                    })

                    self.logger.info(f"🚨 TiKV {metric_name} 异常: {op_type}={max_value:.2f} > {threshold}")

        except Exception as e:
            self.logger.error(f"❌ TiKV分类型指标异常检测失败: {e}")

        return anomalies

    def _detect_tidb_typed_metric_anomalies(self, df: pd.DataFrame, metric_name: str, request: AnalysisRequest) -> List[Dict[str, Any]]:
        """🔧 新增：检测TiDB分类型指标异常（qps）"""
        anomalies = []

        try:
            if 'type' not in df.columns:
                self.logger.warning(f"⚠️ TiDB {metric_name} 数据缺少type列")
                return anomalies

            # TiDB SQL操作类型阈值配置
            tidb_sql_type_thresholds = {
                # 查询操作阈值
                'select': 8.0,                      # Select > 8 QPS (实际最高11.27)
                'use': 8.0,                         # Use > 8 QPS (实际最高11.27)

                # 事务操作阈值
                'begin': 0.2,                       # Begin > 0.2 QPS
                'commit': 0.2,                      # Commit > 0.2 QPS
                'rollback': 0.1,                    # Rollback > 0.1 QPS

                # 数据操作阈值
                'insert': 0.15,                     # Insert > 0.15 QPS (实际最高0.2)
                'update': 0.05,                     # Update > 0.05 QPS (实际最高0.09)
                'delete': 0.05,                     # Delete > 0.05 QPS

                # 其他操作阈值
                'set': 0.1,                         # Set > 0.1 QPS
                'show': 1.0,                        # Show > 1 QPS
                'explain': 0.1,                     # Explain > 0.1 QPS
            }

            # 按type分组检测异常
            for sql_type in df['type'].unique():
                if pd.isna(sql_type) or sql_type is None:
                    continue

                # 查找匹配的阈值
                threshold = None
                sql_type_lower = str(sql_type).lower()
                for pattern, thresh in tidb_sql_type_thresholds.items():
                    if pattern in sql_type_lower:
                        threshold = thresh
                        break

                if threshold is None:
                    continue  # 没有配置阈值的SQL类型跳过

                type_data = df[df['type'] == sql_type]
                if type_data.empty:
                    continue

                data_column = metric_name  # qps

                if data_column not in type_data.columns:
                    continue

                # 检测超过阈值的数据点
                anomalous_data = type_data[type_data[data_column] > threshold]

                if not anomalous_data.empty:
                    max_value = anomalous_data[data_column].max()
                    anomaly_count = len(anomalous_data)

                    # 计算严重程度
                    deviation_ratio = max_value / threshold if threshold > 0 else 1
                    if deviation_ratio > 3:
                        severity = 'critical'
                    elif deviation_ratio > 2:
                        severity = 'high'
                    else:
                        severity = 'medium'

                    anomalies.append({
                        'value': max_value,
                        'threshold': threshold,
                        'severity': severity,
                        'anomaly_count': anomaly_count,
                        'sql_operation_type': sql_type,
                        'metric_display': f"{sql_type}操作: {max_value:.2f} QPS (阈值: {threshold})",
                        'timestamp': anomalous_data.iloc[0]['time'] if 'time' in anomalous_data.columns else request.start_time
                    })

                    self.logger.info(f"🚨 TiDB {metric_name} 异常: {sql_type}={max_value:.2f} > {threshold}")

        except Exception as e:
            self.logger.error(f"❌ TiDB分类型指标异常检测失败: {e}")

        return anomalies

    def _create_tidb_typed_suspicious_entity(self, metric_name: str, anomaly: Dict[str, Any],
                                           df: pd.DataFrame, request: AnalysisRequest) -> Optional[SuspiciousEntity]:
        """🔧 新增：创建TiDB分类型可疑实体"""
        try:
            value = anomaly['value']
            threshold = anomaly['threshold']
            severity = anomaly.get('severity', 'medium')
            sql_operation_type = anomaly.get('sql_operation_type', 'unknown')

            return SuspiciousEntity(
                entity=EntityInfo(
                    service="tidb",
                    namespace="default",
                    pod=f"tidb-{sql_operation_type}"
                ),
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"tidb_{metric_name}_{sql_operation_type}_anomaly",
                    confidence=0.8 if severity in ['critical', 'high'] else 0.6,
                    metric_value=value,
                    threshold=threshold,
                    metric_type=metric_name,
                    severity=severity
                ),
                data_location=DataLocation(
                    file_type="tidb",
                    root_dir="",
                    file_paths=[],
                    file_matching_rule=f"infra_tidb_{metric_name}"
                ),
                confidence=0.8 if severity in ['critical', 'high'] else 0.6,
                raw_data_records=[{
                    'timestamp': anomaly.get('timestamp', request.start_time),
                    'object': 'tidb',
                    'metric': metric_name,
                    'sql_operation_type': sql_operation_type,
                    'value': value,
                    'threshold': threshold
                }],
                data_summary={
                    # 基础分类信息 - 供ReasoningAgent分类使用
                    "metric_category": "other",  # 🔧 修复：使用other分类，让TiDB异常显示在CLUSTER COMPONENT ANOMALIES中
                    "metric_subcategory": f"{metric_name}_metrics",
                    "object_type": "tidb",
                    "object_name": "tidb",
                    "metric_name": metric_name,
                    "metric_display_name": self._get_tidb_sql_display_name(anomaly, metric_name),
                    "metric_unit": self._get_metric_unit(metric_name),

                    # 异常数值信息
                    "current_value": value,
                    "threshold": threshold,
                    "deviation_ratio": value / threshold if threshold > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": severity,

                    # 时间和位置信息
                    "peak_time": anomaly.get('timestamp', request.start_time),
                    "duration_minutes": 0,  # 单点异常
                    "data_location": f"tidb/{metric_name}_{sql_operation_type}"
                }
            )

        except Exception as e:
            self.logger.error(f"❌ 创建TiDB分类型可疑实体失败: {e}")
            return None

    def _get_tidb_sql_display_name(self, anomaly: Dict[str, Any], metric_name: str) -> str:
        """🔧 新增：获取TiDB SQL操作异常的特殊显示名称"""
        if 'sql_operation_type' in anomaly:
            sql_type = anomaly['sql_operation_type']
            sql_type_names = {
                'Select': 'Select查询QPS',
                'Use': 'Use数据库QPS',
                'Insert': 'Insert插入QPS',
                'Update': 'Update更新QPS',
                'Delete': 'Delete删除QPS',
                'Begin': 'Begin事务QPS',
                'Commit': 'Commit提交QPS',
                'Rollback': 'Rollback回滚QPS',
                'Set': 'Set设置QPS',
                'Show': 'Show查看QPS',
                'Explain': 'Explain解释QPS'
            }
            return sql_type_names.get(sql_type, f"{sql_type}操作QPS")
        else:
            return self._get_metric_display_name(metric_name)

    def _detect_single_metric_anomaly(self, df: pd.DataFrame, metric_name: str, threshold: float, object_type: str, current_time: str = None) -> List[Dict[str, Any]]:
        """检测单个指标的异常 - 支持自适应阈值"""
        anomalies = []

        # 尝试不同的字段名
        possible_fields = [metric_name, 'value', 'metric_value']
        metric_data = None

        for field in possible_fields:
            if field in df.columns:
                metric_data = df[field].dropna()
                break

        if metric_data is None or metric_data.empty:
            return anomalies

        # 🔧 确定metric_category和object_name
        if object_type == 'tidb':
            metric_category = 'tidb'
            object_name = 'tidb'  # 修复：不使用tidb-cluster
        elif object_type in ['tikv', 'pd']:
            metric_category = 'other'
            object_name = object_type  # 修复：不使用cluster后缀
        elif object_type in ['apm', 'infra']:
            metric_category = object_type
            object_name = 'cluster'
        else:
            metric_category = 'other'
            object_name = object_type

        # 🚀 使用优化的阈值管理器获取阈值
        final_threshold, threshold_type, threshold_metadata = self.threshold_manager.get_intelligent_threshold(
            category=metric_category,
            metric_name=metric_name,
            object_name=object_name,
            historical_data=metric_data
        )

        self.logger.debug(f"🎯 统一阈值: {metric_category}.{metric_name} = {final_threshold:.6f} ({threshold_type})")
        self.logger.debug(f"   阈值来源: {threshold_metadata.get('source', 'unknown')}")
        self.logger.debug(f"   置信度: {threshold_metadata.get('confidence', 0.5):.1%}")

        # 使用95分位数进行异常检测
        p95_value = metric_data.quantile(0.95)
        max_value = metric_data.max()
        min_value = metric_data.min()

        self.logger.debug(f"🔍 {object_type}.{metric_name}: P95={p95_value:.4f}, Max={max_value:.4f}, 阈值={final_threshold:.4f} ({threshold_type})")

        # 🚀 简化的异常判断逻辑
        is_anomaly = p95_value > final_threshold

        if is_anomaly:
            anomalies.append({
                'metric': metric_name,
                'value': p95_value,
                'max_value': max_value,
                'threshold': final_threshold,
                'original_threshold': threshold,
                'threshold_type': threshold_type,
                'severity': self._calculate_severity(p95_value, final_threshold),
                'anomaly_count': (metric_data > final_threshold).sum(),
                'object_type': object_type
            })
            self.logger.info(f"🚨 检测到{object_type}异常: {metric_name}={p95_value:.4f} > {final_threshold:.4f} ({threshold_type})")

        return anomalies

    def _calculate_severity(self, value: float, threshold: float) -> str:
        """计算异常严重程度"""
        ratio = value / threshold

        if ratio > 5.0:
            return "critical"
        elif ratio > 2.0:
            return "high"
        elif ratio > 1.5:
            return "medium"
        else:
            return "low"

    def _detect_instantaneous_fault_patterns(self, time_range: TimeRange, all_data: Dict) -> List[Dict[str, Any]]:
        """🔧 新增：专门针对短时间窗口（如1秒）的瞬时故障检测"""
        patterns = []

        try:
            start_dt = pd.to_datetime(time_range.start, utc=True)
            end_dt = pd.to_datetime(time_range.end, utc=True)
            duration = (end_dt - start_dt).total_seconds()

            self.logger.info(f"🔍 瞬时故障模式检测: 时间窗口 {duration} 秒")

            if duration <= 5:  # 短时间窗口
                # 1. 🔧 检测Pod重新调度事件（基于instance字段变化）
                pod_rescheduling_patterns = self._detect_pod_rescheduling_patterns(all_data.get('infra', {}), time_range)
                patterns.extend(pod_rescheduling_patterns)
                self.logger.info(f"🔍 检测到 {len(pod_rescheduling_patterns)} 个Pod重新调度模式")

                # 2. 检测Pod删除事件（基于文件名标识）
                deletion_events = self._detect_pod_deletion_events(time_range)
                patterns.extend(deletion_events)
                self.logger.info(f"🔍 检测到 {len(deletion_events)} 个Pod删除事件")

                # 3. 检测容器编排层面的问题
                orchestration_issues = self._detect_orchestration_issues(time_range, all_data)
                patterns.extend(orchestration_issues)
                self.logger.info(f"🔍 检测到 {len(orchestration_issues)} 个容器编排问题")

            self.logger.info(f"🔍 瞬时故障模式检测完成: 总共发现 {len(patterns)} 个模式")

        except Exception as e:
            self.logger.error(f"❌ 瞬时故障模式检测失败: {e}")

        return patterns

    def _detect_pod_instance_changes(self, all_data: Dict, time_range: TimeRange) -> List[Dict[str, Any]]:
        """🔧 新增：检测Pod实例在短时间窗口内的突然变更"""
        changes = []

        try:
            for data_key, df in all_data.items():
                if 'pod' in data_key and isinstance(df, pd.DataFrame) and not df.empty:
                    if 'pod' in df.columns and 'time' in df.columns and len(df) > 1:
                        # 按时间排序
                        df_sorted = df.sort_values('time')

                        # 检测Pod实例的突然消失
                        pod_instances = df_sorted['pod'].unique()
                        for pod in pod_instances:
                            pod_data = df_sorted[df_sorted['pod'] == pod]

                            if len(pod_data) > 1:
                                # 检测数据突然中断（可能表示Pod重启或删除）
                                time_diffs = pod_data['time'].diff()
                                large_gaps = time_diffs[time_diffs > pd.Timedelta(minutes=2)]

                                if len(large_gaps) > 0:
                                    changes.append({
                                        'type': 'pod_instance_change',
                                        'pod_name': pod,
                                        'change_time': pod_data.loc[large_gaps.index[0], 'time'],
                                        'severity': 'high',
                                        'description': f'Pod {pod} 在时间窗口内出现数据中断',
                                        'gap_duration': large_gaps.iloc[0].total_seconds()
                                    })

        except Exception as e:
            self.logger.warning(f"⚠️ Pod实例变更检测失败: {e}")

        return changes

    def _detect_pod_node_migration(self, all_data: Dict) -> List[Dict[str, Any]]:
        """🔧 新增：检测Pod在不同Node之间的迁移"""
        migrations = []

        try:
            for data_key, df in all_data.items():
                if 'infra' in data_key and isinstance(df, pd.DataFrame) and not df.empty:
                    if 'pod' in df.columns and 'kubernetes_node' in df.columns and len(df) > 1:
                        for pod in df['pod'].unique():
                            pod_data = df[df['pod'] == pod].sort_values('time')

                            if len(pod_data) > 1:
                                # 检测Node变更
                                unique_nodes = pod_data['kubernetes_node'].unique()
                                if len(unique_nodes) > 1:
                                    migrations.append({
                                        'type': 'pod_node_migration',
                                        'pod_name': pod,
                                        'from_node': pod_data['kubernetes_node'].iloc[0],
                                        'to_node': pod_data['kubernetes_node'].iloc[-1],
                                        'migration_time': pod_data['time'].iloc[-1],
                                        'severity': 'high',
                                        'description': f'Pod {pod} 从 {pod_data["kubernetes_node"].iloc[0]} 迁移到 {pod_data["kubernetes_node"].iloc[-1]}'
                                    })

        except Exception as e:
            self.logger.warning(f"⚠️ Pod Node迁移检测失败: {e}")

        return migrations

    def _detect_orchestration_issues(self, time_range: TimeRange, all_data: Dict) -> List[Dict[str, Any]]:
        """🔧 新增：检测容器编排层面的问题"""
        issues = []

        try:
            # 检测多个Pod同时出现问题的模式
            pod_issues = {}

            for data_key, df in all_data.items():
                if 'pod' in data_key and isinstance(df, pd.DataFrame) and not df.empty:
                    if 'pod' in df.columns and 'error' in df.columns:
                        error_pods = df[df['error'] > 0]['pod'].unique()
                        for pod in error_pods:
                            if pod not in pod_issues:
                                pod_issues[pod] = []
                            pod_issues[pod].append(data_key)

            # 如果多个Pod同时出现问题，可能是编排层面的问题
            if len(pod_issues) > 2:
                issues.append({
                    'type': 'orchestration_issue',
                    'affected_pods': list(pod_issues.keys()),
                    'severity': 'critical',
                    'description': f'检测到 {len(pod_issues)} 个Pod同时出现问题，可能是容器编排层面的故障',
                    'pod_count': len(pod_issues)
                })

        except Exception as e:
            self.logger.warning(f"⚠️ 容器编排问题检测失败: {e}")

        return issues

    def _create_instantaneous_fault_entity(self, pattern: Dict[str, Any], request: AnalysisRequest) -> Optional[SuspiciousEntity]:
        """🔧 新增：创建瞬时故障可疑实体"""
        try:
            pattern_type = pattern.get('type', 'unknown')
            severity = pattern.get('severity', 'medium')
            description = pattern.get('description', '未知瞬时故障')

            self.logger.debug(f"🔍 创建瞬时故障实体: pattern_type={pattern_type}, severity={severity}")

            # 根据不同的故障模式创建实体
            if pattern_type == 'pod_deletion':
                return SuspiciousEntity(
                    object_type='pod',
                    object_name=pattern.get('pod_name', 'unknown'),
                    anomaly_type='pod_deletion_event',
                    severity=severity,
                    description=description,
                    evidence={
                        'deletion_time': str(pattern.get('deletion_time', '')),
                        'evidence_data': pattern.get('evidence', {}),
                        'fault_pattern': 'instantaneous_pod_deletion'
                    },
                    raw_data=[],
                    analysis_context={
                        'detection_method': 'instantaneous_fault_detection',
                        'time_window': f"{request.start_time} - {request.end_time}",
                        'pattern_type': pattern_type
                    }
                )
            elif pattern_type == 'pod_instance_change':
                return SuspiciousEntity(
                    object_type='pod',
                    object_name=pattern.get('pod_name', 'unknown'),
                    anomaly_type='pod_instance_change',
                    severity=severity,
                    description=description,
                    evidence={
                        'change_time': str(pattern.get('change_time', '')),
                        'gap_duration': pattern.get('gap_duration', 0),
                        'fault_pattern': 'instantaneous_pod_change'
                    },
                    raw_data=[],
                    analysis_context={
                        'detection_method': 'instantaneous_fault_detection',
                        'time_window': f"{request.start_time} - {request.end_time}",
                        'pattern_type': pattern_type
                    }
                )
            elif pattern_type == 'pod_rescheduling_batch':
                # 🔧 新增：处理Pod重新调度批量模式
                affected_pods = pattern.get('affected_pods', [])
                affected_services = pattern.get('affected_services', [])

                # 确定主要的故障组件
                if len(affected_services) == 1 and len(affected_pods) >= 3:
                    # 如果是单个服务的所有Pod都被重新调度，故障组件是Service
                    main_component = affected_services[0]
                    object_type = 'service'
                else:
                    # 否则使用第一个受影响的Pod作为主要组件
                    main_component = affected_pods[0] if affected_pods else 'unknown'
                    object_type = 'pod'

                return SuspiciousEntity(
                    entity=EntityInfo(
                        service=affected_services[0] if affected_services else 'unknown',
                        namespace="hipstershop",
                        pod=main_component if object_type == 'pod' else None
                    ),
                    time_range=TimeRange(start=request.start_time, end=request.end_time),
                    anomaly_feature=AnomalyFeature(
                        pattern="pod_rescheduling_batch",
                        confidence=pattern.get('confidence', 0.9),
                        raw_data_context=pattern,
                        precise_timestamp=str(pattern.get('time', '')),
                        source_record_id=f"pod_rescheduling_{main_component}"
                    ),
                    data_location=DataLocation(
                        file_type="infra",
                        root_dir="",
                        file_paths=[],
                        file_matching_rule="pod_rescheduling_detection"
                    ),
                    confidence=pattern.get('confidence', 0.9),
                    raw_data_records=pattern.get('events', []),
                    data_summary={
                        'rescheduling_time': str(pattern.get('time', '')),
                        'affected_pods': affected_pods,
                        'affected_services': affected_services,
                        'rescheduling_count': pattern.get('rescheduling_count', 0),
                        'fault_pattern': 'pod_rescheduling_batch',
                        'detection_method': 'pod_rescheduling_detection',
                        'time_window': f"{request.start_time} - {request.end_time}",
                        'pattern_type': pattern_type
                    }
                )
            elif pattern_type == 'pod_node_migration':
                return SuspiciousEntity(
                    object_type='pod',
                    object_name=pattern.get('pod_name', 'unknown'),
                    anomaly_type='pod_node_migration',
                    severity=severity,
                    description=description,
                    evidence={
                        'migration_time': str(pattern.get('migration_time', '')),
                        'from_node': pattern.get('from_node', ''),
                        'to_node': pattern.get('to_node', ''),
                        'fault_pattern': 'instantaneous_pod_migration'
                    },
                    raw_data=[],
                    analysis_context={
                        'detection_method': 'instantaneous_fault_detection',
                        'time_window': f"{request.start_time} - {request.end_time}",
                        'pattern_type': pattern_type
                    }
                )
            elif pattern_type == 'orchestration_issue':
                return SuspiciousEntity(
                    object_type='cluster',
                    object_name='kubernetes_cluster',
                    anomaly_type='orchestration_issue',
                    severity=severity,
                    description=description,
                    evidence={
                        'affected_pods': pattern.get('affected_pods', []),
                        'pod_count': pattern.get('pod_count', 0),
                        'fault_pattern': 'instantaneous_orchestration_issue'
                    },
                    raw_data=[],
                    analysis_context={
                        'detection_method': 'instantaneous_fault_detection',
                        'time_window': f"{request.start_time} - {request.end_time}",
                        'pattern_type': pattern_type
                    }
                )
            else:
                # 通用瞬时故障实体
                return SuspiciousEntity(
                    object_type='system',
                    object_name='unknown',
                    anomaly_type='instantaneous_fault',
                    severity=severity,
                    description=description,
                    evidence=pattern,
                    raw_data=[],
                    analysis_context={
                        'detection_method': 'instantaneous_fault_detection',
                        'time_window': f"{request.start_time} - {request.end_time}",
                        'pattern_type': pattern_type
                    }
                )

        except Exception as e:
            self.logger.error(f"❌ 创建瞬时故障实体失败: {e}")
            return None



    def _create_other_suspicious_entity(self, component_type: str, metric_name: str, anomaly: Dict[str, Any],
                                      df: pd.DataFrame, request: AnalysisRequest) -> Optional[SuspiciousEntity]:
        """创建其他组件可疑实体"""
        try:
            # 🔧 特殊处理：PD分类型异常显示
            if 'region_type' in anomaly:
                # PD分类型异常，使用预设的显示格式，明确说明异常类型
                region_type = anomaly['region_type']
                region_value = anomaly['value']
                threshold = anomaly['threshold']

                # 翻译Region类型为中文说明
                region_type_names = {
                    'miss-peer-region-count': '缺失副本Region数量',
                    'undersized-region-count': '副本不足Region数量',
                    'empty-region-count': '空Region数量',
                    'down-peer-region-count': '下线副本Region数量',
                    'pending-peer-region-count': '待处理副本Region数量',
                    'offline-peer-region-count': '离线副本Region数量',
                    'extra-peer-region-count': '多余副本Region数量',
                    'oversized-region-count': '副本过多Region数量',
                    'learner-peer-region-count': '学习者副本Region数量',
                    'witness-leader-region-count': '见证者Leader Region数量'
                }

                region_name = region_type_names.get(region_type, region_type)
                metric_display = f"{region_name}: {region_value} (阈值: {threshold})"

                # 🔧 同时更新data_summary中的display_name，确保在最终输出中显示正确
                data_summary = {
                    "metric_category": "other",
                    "metric_subcategory": f"{metric_name}_metrics",
                    "object_type": component_type,
                    "object_name": component_type,
                    "metric_name": metric_name,
                    "metric_display_name": region_name,  # 🔧 使用中文名称
                    "metric_unit": self._get_metric_unit(metric_name),
                    "current_value": anomaly['value'],
                    "threshold": anomaly['threshold'],
                    "deviation_ratio": anomaly['value'] / anomaly['threshold'] if anomaly['threshold'] > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": anomaly['severity'],
                    "region_type": region_type  # 🔧 保留region_type信息
                }
            elif 'cpu_usage' in metric_name:
                metric_display = f"CPU使用率: {anomaly['value']:.1f}% (阈值: {anomaly['threshold']}%)"
            elif 'memory_usage' in metric_name:
                metric_display = f"内存使用: {anomaly['value']/(1024*1024*1024):.1f}GB (阈值: {anomaly['threshold']/(1024*1024*1024):.1f}GB)"
            elif 'count' in metric_name:
                metric_display = f"{metric_name}: {anomaly['value']:.0f} (阈值: {anomaly['threshold']})"
            else:
                metric_display = f"{metric_name}: {anomaly['value']:.2f} (阈值: {anomaly['threshold']})"

            # 🚀 修复：从实际数据中提取组件实体信息
            service_name = component_type  # 固定为组件类型名（pd或tikv）
            namespace_name = "default"  # 使用default命名空间
            pod_name = None  # 默认Pod名

            if not df.empty:
                # 从实际数据中提取实体信息（仅提取namespace和pod，不改变service_name）
                first_row = df.iloc[0]

                # 提取命名空间信息（如果存在且不为null）
                if 'namespace' in df.columns and pd.notna(first_row.get('namespace')) and first_row['namespace'] != 'null':
                    namespace_name = str(first_row['namespace'])

                # 提取Pod信息（如果存在且不为null）
                if 'pod' in df.columns and pd.notna(first_row.get('pod')) and first_row['pod'] != 'null':
                    pod_name = str(first_row['pod'])

                self.logger.debug(f"🔍 {component_type}实体信息: service={service_name}, namespace={namespace_name}, pod={pod_name}")

            entity = SuspiciousEntity(
                entity=EntityInfo(
                    service=service_name,
                    namespace=namespace_name,
                    pod=pod_name
                ),
                time_range=TimeRange(start=request.start_time, end=request.end_time),
                anomaly_feature=AnomalyFeature(
                    pattern=f"{metric_name}_{component_type}_anomaly",
                    confidence=0.8,
                    metric_value=anomaly['value'],
                    threshold=anomaly['threshold'],
                    metric_type=metric_name,  # 🔧 添加metric_type字段
                    severity=anomaly.get('severity', 'medium')  # 🔧 添加severity字段
                ),
                data_location=DataLocation(
                    file_type="other",
                    root_dir="",
                    file_paths=[],
                    file_matching_rule=f"infra_{component_type}_{metric_name}"
                ),
                confidence=0.8,
                raw_data_records=[],
                data_summary={
                    # 基础分类信息 - 供ReasoningAgent分类使用
                    "metric_category": "other",
                    "metric_subcategory": f"{metric_name}_metrics",
                    "object_type": component_type,
                    "object_name": component_type,  # 修复：不使用cluster后缀
                    "metric_name": metric_name,
                    # 🔧 对于PD分类型异常，使用特殊的显示名称
                    "metric_display_name": self._get_pd_region_display_name(anomaly, metric_name),
                    "metric_unit": self._get_metric_unit(metric_name),

                    # 异常数值信息
                    "current_value": anomaly['value'],
                    "threshold": anomaly['threshold'],
                    "deviation_ratio": anomaly['value'] / anomaly['threshold'] if anomaly['threshold'] > 0 else 0,
                    "anomaly_count": anomaly.get('anomaly_count', 0),
                    "severity_level": anomaly.get('severity', 'medium'),

                    # 业务上下文信息
                    "service_criticality": "critical",  # 集群组件都是关键的
                    "metric_impact_type": "data_layer",
                    "affected_layer": "cluster_layer",

                    # 兼容性字段
                    "metric": metric_name,
                    "p95_value": anomaly['value'],
                    "severity": anomaly['severity']
                }
            )

            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建{component_type}可疑实体失败: {e}")
            return None

    def _create_empty_response(self, request: AnalysisRequest) -> AnalysisResponse:
        """创建空响应"""
        results = {
            "analysis_summary": "未检测到任何metric异常",
            "apm_anomalies": 0,
            "infra_anomalies": 0,
            "tidb_anomalies": 0,
            "other_anomalies": 0,
            "total_anomalies": 0,
            "detection_method": "multi_level_threshold_based",
            "suspicious_entities": [],
            "standardized_output": {
                "suspicious_entities": [],
                "analysis_summary": "未检测到任何metric异常"
            }
        }

        return AnalysisResponse(
            case_uuid=request.case_uuid,
            start_time=request.start_time,
            end_time=request.end_time,
            data=request.data,
            analysis_type="metrics",
            results=results,
            success=True
        )

    def _detect_pod_rescheduling_patterns(self, infra_data: Dict[str, pd.DataFrame], time_range: TimeRange) -> List[Dict[str, Any]]:
        """🔧 新增：检测Pod删除/重新调度模式 - 基于instance字段变化"""
        patterns = []

        try:
            self.logger.info("🔍 开始检测Pod重新调度事件")

            # 查找包含Pod CPU使用率的数据，这个数据包含了所有Pod的instance信息
            cpu_data = None
            for key, df in infra_data.items():
                # 跳过元数据键
                if key.endswith('_metadata'):
                    continue
                # 查找Pod CPU使用率数据 - 必须是pod级别的数据
                if 'pod_cpu_usage' in key and isinstance(df, pd.DataFrame) and 'pod' in df.columns:
                    cpu_data = df
                    self.logger.info(f"🔍 找到Pod CPU使用率数据: {key}, 数据量: {len(df)} 条记录")
                    break
                # 调试：记录所有包含cpu_usage的键
                elif 'cpu_usage' in key:
                    self.logger.debug(f"🔍 跳过非Pod CPU数据: {key} (列: {list(df.columns) if isinstance(df, pd.DataFrame) else 'N/A'})")

            if cpu_data is None or cpu_data.empty:
                self.logger.warning("⚠️ 未找到包含Pod instance信息的数据")
                return patterns

            # 🔧 重要修正：由于metric数据是分钟级别的，需要将秒级时间窗口扩展到分钟级别
            start_dt = pd.to_datetime(time_range.start, utc=True)
            end_dt = pd.to_datetime(time_range.end, utc=True)
            duration_seconds = (end_dt - start_dt).total_seconds()

            # 将时间窗口扩展到分钟边界
            start_minute = start_dt.replace(second=0, microsecond=0)
            end_minute = end_dt.replace(second=59, microsecond=999999)
            if end_dt.second > 0 or end_dt.microsecond > 0:
                end_minute = end_minute.replace(minute=end_minute.minute + 1, second=0, microsecond=0)

            self.logger.info(f"⏰ 原始时间窗口: {duration_seconds} 秒，扩展到分钟级别: {start_minute} 到 {end_minute}")

            # 创建扩展的时间范围用于数据过滤（前后各加5分钟缓冲）
            from datetime import timedelta
            extended_start = start_minute - timedelta(minutes=5)
            extended_end = end_minute + timedelta(minutes=5)
            extended_time_range = TimeRange(
                start=extended_start.isoformat(),
                end=extended_end.isoformat()
            )

            # 时间过滤
            cpu_data = self._filter_data_by_time_range(cpu_data, extended_time_range)
            if cpu_data.empty:
                return patterns

            cpu_data['time'] = pd.to_datetime(cpu_data['time'])
            cpu_data = cpu_data.sort_values(['pod', 'time'])

            # 检测每个Pod的instance变化
            rescheduling_events = []

            for pod_name in cpu_data['pod'].unique():
                if pd.isna(pod_name) or pod_name == 'null':
                    continue

                pod_data = cpu_data[cpu_data['pod'] == pod_name].sort_values('time')
                if len(pod_data) < 2:
                    continue

                # 检测instance字段变化
                instances = pod_data['instance'].unique()
                if len(instances) > 1:
                    # 发现instance变化，记录变化时间点
                    current_instance = None
                    for _, row in pod_data.iterrows():
                        if pd.notna(row['instance']) and row['instance'] != current_instance:
                            if current_instance is not None:
                                # 检查是否在分钟级时间窗口内
                                if start_minute <= row['time'] <= end_minute:
                                    time_diff_seconds = (row['time'] - start_dt).total_seconds()

                                    # 记录重新调度事件
                                    rescheduling_events.append({
                                        'pod': pod_name,
                                        'time': row['time'],
                                        'from_instance': current_instance,
                                        'to_instance': row['instance'],
                                        'event_type': 'pod_rescheduling',
                                        'time_diff_seconds': time_diff_seconds
                                    })

                                    self.logger.info(f"🔄 检测到Pod重新调度: {pod_name} {current_instance} → {row['instance']} at {row['time']} (距故障开始 {time_diff_seconds:.0f}秒)")

                            current_instance = row['instance']

            # 将重新调度事件转换为模式
            if rescheduling_events:
                # 按时间分组，识别批量重新调度
                time_groups = {}
                for event in rescheduling_events:
                    time_key = event['time'].strftime('%Y-%m-%d %H:%M')
                    if time_key not in time_groups:
                        time_groups[time_key] = []
                    time_groups[time_key].append(event)

                for time_key, events in time_groups.items():
                    if len(events) >= 1:  # 至少1个Pod重新调度就记录
                        # 分析重新调度模式
                        if len(events) >= 3:
                            pattern_desc = f"批量重新调度: {len(events)}个Pod在{time_key}同时重新调度"
                            severity = 'high'
                        else:
                            pattern_desc = f"Pod重新调度: {len(events)}个Pod在{time_key}重新调度"
                            severity = 'medium'

                        # 统计受影响的服务
                        affected_services = set()
                        for event in events:
                            service = event['pod'].rsplit('-', 1)[0]
                            affected_services.add(service)

                        pattern = {
                            'type': 'pod_rescheduling_batch',  # 修正字段名
                            'pattern_type': 'pod_rescheduling_batch',
                            'time': events[0]['time'],
                            'affected_pods': [e['pod'] for e in events],
                            'affected_services': list(affected_services),
                            'rescheduling_count': len(events),
                            'events': events,
                            'severity': severity,
                            'description': pattern_desc,
                            'confidence': 0.9,
                            'category': 'infrastructure'
                        }
                        patterns.append(pattern)

            self.logger.info(f"📊 Pod重新调度检测完成: 发现 {len(patterns)} 个重新调度模式，涉及 {len(rescheduling_events)} 个Pod")
            return patterns

        except Exception as e:
            self.logger.error(f"❌ Pod重新调度检测失败: {e}")
            return patterns

    def _analyze_temporal_patterns(self, df: pd.DataFrame, metric_name: str, anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """🔧 新增：分析时间模式，提取异常的时间特征"""
        analysis = {}

        try:
            if df.empty or 'time' not in df.columns:
                return analysis

            # 确定数据字段
            data_field = metric_name
            if metric_name not in df.columns:
                possible_fields = [metric_name, 'value', f'{metric_name}_value']
                for field in possible_fields:
                    if field in df.columns:
                        data_field = field
                        break
                else:
                    return analysis

            # 过滤异常数据
            threshold = anomaly.get('threshold', 0)
            if threshold <= 0:
                return analysis

            anomaly_data = df[df[data_field] > threshold].copy()

            if anomaly_data.empty:
                return analysis

            # 1. 异常持续时间分析
            anomaly_start = anomaly_data['time'].min()
            anomaly_end = anomaly_data['time'].max()
            duration = anomaly_end - anomaly_start

            analysis.update({
                "anomaly_start_time": anomaly_start.isoformat(),
                "anomaly_end_time": anomaly_end.isoformat(),
                "anomaly_duration": str(duration),
                "anomaly_duration_minutes": duration.total_seconds() / 60
            })

            # 2. 峰值时间
            peak_idx = anomaly_data[data_field].idxmax()
            peak_time = anomaly_data.loc[peak_idx, 'time']
            analysis["peak_time"] = peak_time.isoformat()

            # 3. 趋势分析
            if len(anomaly_data) >= 3:
                # 计算趋势斜率
                time_numeric = (anomaly_data['time'] - anomaly_data['time'].min()).dt.total_seconds()
                correlation = time_numeric.corr(anomaly_data[data_field])

                if correlation > 0.3:
                    analysis["trend_direction"] = "increasing"
                elif correlation < -0.3:
                    analysis["trend_direction"] = "decreasing"
                else:
                    analysis["trend_direction"] = "stable"

                analysis["trend_correlation"] = correlation

            self.logger.debug(f"🕒 时间分析完成: {metric_name}, 峰值时间: {analysis.get('peak_time', 'N/A')}")

        except Exception as e:
            self.logger.debug(f"⚠️ 时间分析失败 {metric_name}: {e}")

        return analysis

    def _detect_tidb_cluster_multidimensional_anomalies(self, request) -> List[Dict[str, Any]]:
        """检测TiDB集群的多维度异常"""
        try:
            self.logger.info("🔍 开始TiDB集群多维度异常检测")

            multidim_anomalies = []

            # 检测TiDB、PD、TiKV三个组件的多维度异常
            for component_type in ['tidb', 'pd', 'tikv']:
                try:
                    # 收集该组件的当前指标值
                    current_metrics = self._collect_component_current_metrics(
                        component_type, request.start_time, request.end_time
                    )

                    if not current_metrics:
                        self.logger.debug(f"未找到{component_type}组件的指标数据")
                        continue

                    # 执行多维度异常检测
                    anomaly_result = self.multidim_threshold_manager.detect_multidimensional_anomaly(
                        component_type, current_metrics, request.start_time
                    )

                    if anomaly_result.get('overall_anomaly', False):
                        # 生成多维度异常报告
                        report = self.multidim_threshold_manager.generate_multidim_report(
                            component_type, anomaly_result
                        )

                        multidim_anomalies.append({
                            'component_type': component_type,
                            'anomaly_type': 'multidimensional',
                            'anomaly_score': anomaly_result.get('multidimensional_anomaly_score', 0),
                            'single_metric_anomalies': anomaly_result.get('single_metric_anomalies', []),
                            'correlation_anomalies': anomaly_result.get('correlation_anomalies', []),
                            'report': report,
                            'timestamp': request.start_time
                        })

                        self.logger.info(f"🚨 检测到{component_type}多维度异常，评分: {anomaly_result.get('multidimensional_anomaly_score', 0):.2f}")

                except Exception as e:
                    self.logger.error(f"检测{component_type}多维度异常失败: {e}")
                    continue

            self.logger.info(f"✅ TiDB集群多维度异常检测完成，发现{len(multidim_anomalies)}个异常组件")
            return multidim_anomalies

        except Exception as e:
            self.logger.error(f"TiDB集群多维度异常检测失败: {e}")
            return []

    def _collect_component_current_metrics(self, component_type: str, start_time: str, end_time: str) -> Dict[str, float]:
        """收集组件的当前指标值"""
        try:
            current_metrics = {}

            # 根据组件类型确定数据路径
            start_dt = pd.to_datetime(start_time)
            date_str = start_dt.strftime('%Y-%m-%d')

            # 🚀 获取该组件的所有指标名称（简化版本）
            # 定义各组件的核心指标
            component_metrics_map = {
                'tidb': ['qps', 'duration_99th', 'duration_95th', 'cpu_usage', 'memory_usage', 'connection_count'],
                'tikv': ['qps', 'cpu_usage', 'memory_usage', 'io_util', 'region_pending'],
                'pd': ['cpu_usage', 'memory_usage', 'region_count', 'store_up_count']
            }

            if component_type not in component_metrics_map:
                return {}

            all_metric_names = component_metrics_map[component_type]

            # 加载每个指标的数据
            for metric_name in all_metric_names:
                try:
                    if component_type == 'tidb':
                        file_path = os.path.join(
                            self.data_root_path, date_str, "metric-parquet", "infra",
                            f"infra_tidb_{metric_name}_{date_str}.parquet"
                        )
                    else:  # pd, tikv
                        file_path = os.path.join(
                            self.data_root_path, date_str, "metric-parquet", "other",
                            f"infra_{component_type}_{metric_name}_{date_str}.parquet"
                        )

                    if os.path.exists(file_path):
                        df = pd.read_parquet(file_path)
                        if not df.empty:
                            # 过滤时间范围
                            df['time'] = pd.to_datetime(df['time'])
                            start_dt = pd.to_datetime(start_time)
                            end_dt = pd.to_datetime(end_time)

                            time_mask = (df['time'] >= start_dt) & (df['time'] <= end_dt)
                            filtered_df = df[time_mask]

                            if not filtered_df.empty:
                                # 提取数值列
                                value_cols = [col for col in filtered_df.columns if col in [metric_name, 'value', 'metric_value']]
                                if value_cols:
                                    values = filtered_df[value_cols[0]].dropna()
                                    if not values.empty:
                                        # 使用95分位数作为当前值
                                        current_metrics[metric_name] = values.quantile(0.95)

                except Exception as e:
                    self.logger.debug(f"加载{component_type}.{metric_name}指标失败: {e}")
                    continue

            self.logger.debug(f"收集到{component_type}组件{len(current_metrics)}个指标")
            return current_metrics

        except Exception as e:
            self.logger.error(f"收集{component_type}组件指标失败: {e}")
            return {}

    def _create_multidim_suspicious_entity(self, anomaly: Dict[str, Any], request) -> Optional[SuspiciousEntity]:
        """创建多维度异常的可疑实体"""
        try:
            component_type = anomaly['component_type']
            anomaly_score = anomaly['anomaly_score']
            single_anomalies = anomaly['single_metric_anomalies']
            correlation_anomalies = anomaly['correlation_anomalies']

            # 构建异常特征描述
            anomaly_features = []

            # 添加单指标异常
            for single_anomaly in single_anomalies:
                metric = single_anomaly['metric']
                deviation = single_anomaly['deviation']
                severity = single_anomaly['severity']
                anomaly_features.append(f"{metric}({deviation:.1f}x,{severity})")

            # 添加相关性异常
            for corr_anomaly in correlation_anomalies:
                rule_name = corr_anomaly['rule_name']
                confidence = corr_anomaly['confidence']
                anomaly_features.append(f"相关性异常:{rule_name}(置信度:{confidence:.2f})")

            # 计算综合置信度
            confidence = min(anomaly_score, 1.0)

            # 创建可疑实体
            entity = SuspiciousEntity(
                entity_type="cluster_component",
                entity_id=component_type,  # 修复：不使用cluster后缀
                service_name=f"tidb-{component_type}",
                namespace="default",  # 修复：使用default命名空间而不是tidb-cluster
                pod_name=f"{component_type}-multidim",
                anomaly_feature=AnomalyFeature(
                    pattern=f"multidimensional_anomaly",
                    confidence=confidence,
                    latency=0,  # 多维度异常不直接对应延迟
                    metric_value=anomaly_score,
                    threshold=0.3  # 多维度异常阈值
                ),
                raw_data_evidence=[{
                    'timestamp': request.start_time,
                    'component': component_type,
                    'anomaly_type': 'multidimensional',
                    'anomaly_score': anomaly_score,
                    'features': ', '.join(anomaly_features[:3]),  # 限制长度
                    'report': anomaly['report'][:500] if len(anomaly['report']) > 500 else anomaly['report']  # 限制报告长度
                }],
                statistical_analysis={
                    'anomaly_score': anomaly_score,
                    'single_metric_count': len(single_anomalies),
                    'correlation_count': len(correlation_anomalies),
                    'severity': 'high' if anomaly_score > 0.7 else 'medium' if anomaly_score > 0.4 else 'low'
                },
                confidence=confidence,
                data_location=f"tidb_cluster/{component_type}/multidimensional"
            )

            self.logger.debug(f"✅ 创建{component_type}多维度可疑实体成功，评分: {anomaly_score:.2f}")
            return entity

        except Exception as e:
            self.logger.error(f"❌ 创建多维度可疑实体失败: {e}")
            return None
