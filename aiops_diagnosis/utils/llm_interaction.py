"""
LLM交互工具 - 提供健壮的LLM调用和响应解析
"""

import json
import re
import asyncio
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import time

@dataclass
class LLMResponse:
    """LLM响应数据类"""
    success: bool
    content: str
    parsed_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    response_time: float = 0.0
    retry_count: int = 0

class RobustLLMInteraction:
    """健壮的LLM交互类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LLM交互器
        
        Args:
            config: LLM配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 配置参数 - 使用统一配置
        from ..llm_config import LLMConfig
        self.timeout_seconds = config.get('timeout_seconds', LLMConfig.UNIFIED_TIMEOUT)
        self.max_retries = config.get('max_retries', LLMConfig.UNIFIED_MAX_RETRIES)
        self.retry_delay = config.get('retry_delay', 1.0)
        
        # JSON提取模式
        json_config = config.get('json_extraction', {})
        self.json_patterns = json_config.get('patterns', [
            r"```json\s*(.*?)\s*```",
            r"```\s*(.*?)\s*```", 
            r"\{.*\}"
        ])
        self.fallback_enabled = json_config.get('fallback_enabled', True)
    
    async def call_llm_with_timeout(self, llm_client, messages: List[Dict[str, str]], 
                                  timeout: Optional[float] = None) -> LLMResponse:
        """
        带超时控制的LLM调用
        
        Args:
            llm_client: LLM客户端
            messages: 消息列表
            timeout: 超时时间（秒）
            
        Returns:
            LLM响应对象
        """
        if timeout is None:
            timeout = self.timeout_seconds
        
        start_time = time.time()
        retry_count = 0
        
        for attempt in range(self.max_retries + 1):
            try:
                # 使用asyncio.wait_for实现超时控制
                response = await asyncio.wait_for(
                    self._make_llm_call(llm_client, messages),
                    timeout=timeout
                )
                
                response_time = time.time() - start_time
                
                return LLMResponse(
                    success=True,
                    content=response,
                    response_time=response_time,
                    retry_count=retry_count
                )
                
            except asyncio.TimeoutError:
                retry_count += 1
                error_msg = f"LLM调用超时 (attempt {attempt + 1}/{self.max_retries + 1})"
                self.logger.warning(error_msg)
                
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))  # 指数退避
                    continue
                else:
                    return LLMResponse(
                        success=False,
                        content="",
                        error=f"LLM调用超时，已重试{self.max_retries}次",
                        response_time=time.time() - start_time,
                        retry_count=retry_count
                    )
                    
            except Exception as e:
                retry_count += 1
                error_msg = f"LLM调用异常: {e} (attempt {attempt + 1}/{self.max_retries + 1})"
                self.logger.error(error_msg)
                
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    return LLMResponse(
                        success=False,
                        content="",
                        error=f"LLM调用失败: {e}",
                        response_time=time.time() - start_time,
                        retry_count=retry_count
                    )
    
    async def _make_llm_call(self, llm_client, messages: List[Dict[str, str]]) -> str:
        """
        实际的LLM调用（需要子类实现或传入具体的调用逻辑）
        
        Args:
            llm_client: LLM客户端
            messages: 消息列表
            
        Returns:
            LLM响应内容
        """
        # 这里需要根据具体的LLM客户端实现
        # 示例：
        if hasattr(llm_client, 'achat'):
            response = await llm_client.achat(messages)
            return response.message.content
        elif hasattr(llm_client, 'chat'):
            response = llm_client.chat(messages)
            return response.message.content
        else:
            raise NotImplementedError("LLM客户端不支持的调用方法")
    
    def extract_json_from_response(self, content: str) -> Optional[Dict[str, Any]]:
        """
        从LLM响应中提取JSON数据
        
        Args:
            content: LLM响应内容
            
        Returns:
            解析后的JSON数据，失败时返回None
        """
        if not content:
            return None
        
        # 尝试多种JSON提取模式
        for pattern in self.json_patterns:
            try:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                if matches:
                    json_str = matches[0].strip()
                    
                    # 清理JSON字符串
                    json_str = self._clean_json_string(json_str)
                    
                    # 尝试解析JSON
                    parsed_data = json.loads(json_str)
                    
                    self.logger.debug(f"成功使用模式 '{pattern}' 提取JSON")
                    return parsed_data
                    
            except (json.JSONDecodeError, IndexError) as e:
                self.logger.debug(f"模式 '{pattern}' 解析失败: {e}")
                continue
        
        # 如果启用了回退机制，尝试直接解析整个内容
        if self.fallback_enabled:
            try:
                # 尝试找到第一个完整的JSON对象
                json_start = content.find('{')
                if json_start != -1:
                    # 找到匹配的右括号
                    brace_count = 0
                    json_end = json_start
                    
                    for i, char in enumerate(content[json_start:], json_start):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break
                    
                    if brace_count == 0:
                        json_str = content[json_start:json_end]
                        json_str = self._clean_json_string(json_str)
                        parsed_data = json.loads(json_str)
                        
                        self.logger.debug("使用回退机制成功提取JSON")
                        return parsed_data
                        
            except json.JSONDecodeError as e:
                self.logger.debug(f"回退机制解析失败: {e}")
        
        self.logger.warning("无法从LLM响应中提取有效的JSON数据")
        return None
    
    def _clean_json_string(self, json_str: str) -> str:
        """
        清理JSON字符串
        
        Args:
            json_str: 原始JSON字符串
            
        Returns:
            清理后的JSON字符串
        """
        # 移除注释
        json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
        
        # 移除多余的空白字符
        json_str = json_str.strip()
        
        # 修复常见的JSON格式问题
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除对象末尾的逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组末尾的逗号
        
        return json_str
    
    async def call_and_parse_json(self, llm_client, messages: List[Dict[str, str]], 
                                timeout: Optional[float] = None) -> LLMResponse:
        """
        调用LLM并解析JSON响应
        
        Args:
            llm_client: LLM客户端
            messages: 消息列表
            timeout: 超时时间
            
        Returns:
            包含解析结果的LLM响应对象
        """
        # 调用LLM
        response = await self.call_llm_with_timeout(llm_client, messages, timeout)
        
        if not response.success:
            return response
        
        # 解析JSON
        parsed_data = self.extract_json_from_response(response.content)
        
        if parsed_data is not None:
            response.parsed_data = parsed_data
            self.logger.info("LLM响应JSON解析成功")
        else:
            response.success = False
            response.error = "无法解析LLM响应中的JSON数据"
            self.logger.error("LLM响应JSON解析失败")
        
        return response
    
    def validate_json_response(self, parsed_data: Dict[str, Any], 
                             required_fields: List[str]) -> bool:
        """
        验证JSON响应的完整性
        
        Args:
            parsed_data: 解析后的JSON数据
            required_fields: 必需字段列表
            
        Returns:
            验证是否通过
        """
        if not isinstance(parsed_data, dict):
            self.logger.error("解析的数据不是字典类型")
            return False
        
        missing_fields = [field for field in required_fields if field not in parsed_data]
        
        if missing_fields:
            self.logger.error(f"缺少必需字段: {missing_fields}")
            return False
        
        return True
