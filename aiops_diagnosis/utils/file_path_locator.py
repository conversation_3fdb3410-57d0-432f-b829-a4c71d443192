#!/usr/bin/env python3
"""
文件路径定位工具
基于时间和组件信息精确定位目标文件路径
"""

import logging
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from ..autogen_agents.base_aiops_agent import DataLocation, TimeRange, EntityInfo

logger = logging.getLogger(__name__)


class FilePathLocator:
    """文件路径定位器 - 实现时间→组件→文件路径的映射"""
    
    def __init__(self, data_root_path: str = "/data/phaseone_data"):
        self.data_root_path = Path(data_root_path)
        
        # 文件命名规则配置
        self.file_patterns = {
            "log": {
                "pattern": "log_filebeat-server_{date}_{hour}-00-00.parquet",
                "directory": "log-parquet",
                "time_granularity": "hour"  # 按小时划分
            },
            "trace": {
                "pattern": "trace_jaeger-span_{date}_{hour}-00-00.parquet", 
                "directory": "trace-parquet",
                "time_granularity": "hour"  # 按小时划分
            },
            "metric": {
                "apm_pod": {
                    "pattern": "pod_{service}-{instance}_{date}.parquet",
                    "directory": "metric-parquet/apm/pod",
                    "time_granularity": "day"  # 按天划分
                },
                "apm_service": {
                    "pattern": "service_{service}_{date}.parquet",
                    "directory": "metric-parquet/apm/service", 
                    "time_granularity": "day"
                },
                "infra_pod": {
                    "pattern": "*{metric_type}_{date}.parquet",
                    "directory": "metric-parquet/infra/infra_pod",
                    "time_granularity": "day"
                }
            }
        }
    
    def locate_log_files(self, time_range: TimeRange, entity: EntityInfo) -> DataLocation:
        """定位日志文件"""
        logger.info(f"🔍 定位日志文件: {entity.service}, 时间范围: {time_range.start} - {time_range.end}")
        
        # 解析时间范围，转换为北京时间
        start_dt = self._parse_utc_to_beijing(time_range.start)
        end_dt = self._parse_utc_to_beijing(time_range.end)
        
        # 获取涉及的小时文件
        file_paths = []
        current_hour = start_dt.replace(minute=0, second=0, microsecond=0)
        end_hour = end_dt.replace(minute=0, second=0, microsecond=0)
        
        while current_hour <= end_hour:
            beijing_date = current_hour.strftime('%Y-%m-%d')
            beijing_hour = current_hour.strftime('%H')
            
            # 构建文件路径
            filename = self.file_patterns["log"]["pattern"].format(
                date=beijing_date,
                hour=beijing_hour
            )
            
            relative_path = f"{self.file_patterns['log']['directory']}/{filename}"
            file_paths.append(relative_path)
            
            current_hour += timedelta(hours=1)
        
        # 构建匹配规则说明
        if len(file_paths) == 1:
            rule = f"时间范围落在文件覆盖的小时内（{start_dt.strftime('%H')}:00-{(start_dt + timedelta(hours=1)).strftime('%H')}:00）"
        else:
            rule = f"时间范围跨{len(file_paths)}个小时，需要{len(file_paths)}个文件"
        
        # 修复root_dir路径，匹配实际的文件结构
        date_str = start_dt.strftime('%Y-%m-%d')
        root_dir = f"{date_str}/"  

        return DataLocation(
            file_type="log",
            root_dir=root_dir,
            file_paths=file_paths,
            file_matching_rule=rule
        )
    
    def locate_metric_files(self, time_range: TimeRange, entity: EntityInfo) -> DataLocation:
        """定位指标文件"""
        logger.info(f"🔍 定位指标文件: {entity.service}, Pod: {entity.pod}, 指标类型: {entity.metric_type}")
        
        # 解析时间范围，转换为北京时间
        start_dt = self._parse_utc_to_beijing(time_range.start)
        end_dt = self._parse_utc_to_beijing(time_range.end)
        
        # 获取涉及的日期
        dates_needed = set()
        current_date = start_dt.date()
        end_date = end_dt.date()
        
        while current_date <= end_date:
            dates_needed.add(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        file_paths = []
        
        # 根据实体类型选择文件模式
        if entity.pod:
            # Pod级指标
            pattern_info = self.file_patterns["metric"]["apm_pod"]
            for date in dates_needed:
                # 提取服务名和实例号
                service_name, instance = self._parse_pod_name(entity.pod)
                filename = pattern_info["pattern"].format(
                    service=service_name,
                    instance=instance,
                    date=date
                )
                relative_path = f"{pattern_info['directory']}/{filename}"
                file_paths.append(relative_path)
            
            rule = f"Pod级指标文件按{{服务名}}-{{实例号}}_日期命名"
            
        elif entity.metric_type:
            # 基础设施指标 - 根据实际文件命名规则
            pattern_info = self.file_patterns["metric"]["infra_pod"]
            for date in dates_needed:
                # 构建具体的文件路径，使用实际的命名规则
                filename = f"infra_pod_{entity.metric_type}_{date}.parquet"
                relative_path = f"{pattern_info['directory']}/{filename}"

                # 检查文件是否存在
                full_path = self.data_root_path / relative_path
                if full_path.exists():
                    file_paths.append(relative_path)
                else:
                    # 如果具体文件不存在，尝试查找匹配的文件
                    directory_path = self.data_root_path / pattern_info['directory']
                    if directory_path.exists():
                        import glob
                        pattern = f"*{entity.metric_type}*{date}*.parquet"
                        matching_files = glob.glob(str(directory_path / pattern))
                        for match in matching_files:
                            rel_path = os.path.relpath(match, str(self.data_root_path))
                            file_paths.append(rel_path)
            
            rule = f"基础设施指标文件按指标类型_{entity.metric_type}_日期命名"
            
        else:
            # 服务级指标
            pattern_info = self.file_patterns["metric"]["apm_service"]
            for date in dates_needed:
                filename = pattern_info["pattern"].format(
                    service=entity.service,
                    date=date
                )
                relative_path = f"{pattern_info['directory']}/{filename}"
                file_paths.append(relative_path)
            
            rule = f"服务级指标文件按服务名_{entity.service}_日期命名"
        
        # 修复root_dir路径，匹配实际的文件结构
        date_str = list(dates_needed)[0]
        root_dir = f"{date_str}/"  

        return DataLocation(
            file_type="metric",
            root_dir=root_dir,
            file_paths=file_paths,
            file_matching_rule=rule
        )

    def locate_apm_pod_files(self, time_range: TimeRange, entity: EntityInfo = None) -> DataLocation:
        """定位APM Pod级别指标文件"""
        logger.info(f"🔍 定位APM Pod文件: 时间范围: {time_range.start} - {time_range.end}")

        # 解析时间范围，转换为北京时间
        start_dt = self._parse_utc_to_beijing(time_range.start)
        end_dt = self._parse_utc_to_beijing(time_range.end)

        file_paths = []

        # 遍历时间范围内的每一天
        current_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y-%m-%d")

            # APM Pod文件路径: /data/phaseone_data/{date}/metric-parquet/apm/pod/
            pod_dir = self.data_root_path / f"{date_str}" / "metric-parquet" / "apm" / "pod"

            if pod_dir.exists():
                # 扫描所有pod文件，包括deleted文件用于瞬时故障检测
                for pod_file in pod_dir.glob("pod_*_*.parquet"):
                    file_paths.append(pod_file)
                    if "(deleted)" in pod_file.name:
                        logger.debug(f"🔍 找到Pod删除事件文件: {pod_file}")
                    else:
                        logger.debug(f"✅ 找到APM Pod文件: {pod_file}")
            else:
                logger.debug(f"⚠️ APM Pod目录不存在: {pod_dir}")

            current_dt += timedelta(days=1)

        logger.info(f"📊 APM Pod文件定位完成: 找到 {len(file_paths)} 个文件")

        rule = {
            "pattern": "pod_{service_name}-{instance_id}_{date}.parquet",
            "description": "APM Pod级别指标文件按天存储",
            "time_granularity": "day"
        }

        root_dir = self.data_root_path / "metric-parquet" / "apm" / "pod"

        return DataLocation(
            file_type="apm_pod",
            root_dir=str(root_dir),
            file_paths=[str(p) for p in file_paths],
            file_matching_rule=str(rule)
        )

    def locate_apm_service_files(self, time_range: TimeRange, entity: EntityInfo = None) -> DataLocation:
        """定位APM Service级别指标文件"""
        logger.info(f"🔍 定位APM Service文件: 时间范围: {time_range.start} - {time_range.end}")

        # 解析时间范围，转换为北京时间
        start_dt = self._parse_utc_to_beijing(time_range.start)
        end_dt = self._parse_utc_to_beijing(time_range.end)

        file_paths = []

        # 遍历时间范围内的每一天
        current_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y-%m-%d")

            # APM Service文件路径: /data/phaseone_data/{date}/metric-parquet/apm/service/
            service_dir = self.data_root_path / f"{date_str}" / "metric-parquet" / "apm" / "service"

            if service_dir.exists():
                # 扫描所有service文件
                for service_file in service_dir.glob("service_*_*.parquet"):
                    file_paths.append(service_file)
                    logger.debug(f"✅ 找到APM Service文件: {service_file}")
            else:
                logger.debug(f"⚠️ APM Service目录不存在: {service_dir}")

            current_dt += timedelta(days=1)

        logger.info(f"📊 APM Service文件定位完成: 找到 {len(file_paths)} 个文件")

        rule = {
            "pattern": "service_{service_name}_{date}.parquet",
            "description": "APM Service级别指标文件按天存储",
            "time_granularity": "day"
        }

        root_dir = self.data_root_path / "metric-parquet" / "apm" / "service"

        return DataLocation(
            file_type="apm_service",
            root_dir=str(root_dir),
            file_paths=[str(p) for p in file_paths],
            file_matching_rule=str(rule)
        )

    def locate_infra_files(self, time_range: TimeRange, entity: EntityInfo = None) -> DataLocation:
        """定位基础设施指标文件"""
        logger.info(f"🔍 定位基础设施文件: 时间范围: {time_range.start} - {time_range.end}")

        # 解析时间范围，转换为北京时间
        start_dt = self._parse_utc_to_beijing(time_range.start)
        end_dt = self._parse_utc_to_beijing(time_range.end)

        file_paths = []

        # 遍历时间范围内的每一天
        current_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y-%m-%d")

            # 基础设施文件路径: /data/phaseone_data/{date}/metric-parquet/infra/
            infra_base_dir = self.data_root_path / f"{date_str}" / "metric-parquet" / "infra"

            if infra_base_dir.exists():
                # 🚀 修复重复数据：按指标类型去重，避免加载重复的指标文件
                seen_metrics = set()  # 记录已经加载的指标类型

                # 扫描所有子目录: infra_node, infra_pod, infra_tidb
                for subdir in ["infra_node", "infra_pod", "infra_tidb"]:
                    infra_dir = infra_base_dir / subdir
                    if infra_dir.exists():
                        # 扫描所有infra文件，但按指标类型去重
                        for infra_file in infra_dir.glob("infra_*_*.parquet"):
                            # 从文件名提取指标类型
                            metric_type = self._extract_metric_type_from_infra_filename(infra_file.name)

                            # 只加载未见过的指标类型
                            if metric_type not in seen_metrics:
                                file_paths.append(infra_file)
                                seen_metrics.add(metric_type)
                                logger.debug(f"✅ 找到基础设施文件: {infra_file} (指标: {metric_type})")
                            else:
                                logger.debug(f"⚠️ 跳过重复指标文件: {infra_file} (指标: {metric_type} 已存在)")
            else:
                logger.debug(f"⚠️ 基础设施目录不存在: {infra_base_dir}")

            current_dt += timedelta(days=1)

        logger.info(f"📊 基础设施文件定位完成: 找到 {len(file_paths)} 个文件")

        rule = {
            "pattern": "infra_{type}_{metric_name}_{date}.parquet",
            "description": "基础设施指标文件按天存储",
            "time_granularity": "day"
        }

        root_dir = self.data_root_path / "metric-parquet" / "infra"

        return DataLocation(
            file_type="infra",
            root_dir=str(root_dir),
            file_paths=[str(p) for p in file_paths],
            file_matching_rule=str(rule)
        )

    def _extract_metric_type_from_infra_filename(self, filename: str) -> str:
        """从基础设施文件名提取指标类型，用于去重"""
        try:
            # 文件名格式: infra_node_node_disk_write_bytes_total_2025-06-06.parquet
            # 或: infra_pod_pod_cpu_usage_2025-06-06.parquet

            # 移除日期和扩展名
            name_without_date = filename.split('_2025-')[0] if '_2025-' in filename else filename.replace('.parquet', '')

            # 提取核心指标名称
            if name_without_date.startswith('infra_node_'):
                # Node级别指标: infra_node_node_disk_write_bytes_total -> node_disk_write_bytes_total
                metric_type = name_without_date.replace('infra_node_', '')
            elif name_without_date.startswith('infra_pod_'):
                # Pod级别指标: infra_pod_pod_cpu_usage -> pod_cpu_usage
                metric_type = name_without_date.replace('infra_pod_', '')
            elif name_without_date.startswith('infra_tidb_'):
                # TiDB级别指标: infra_tidb_connection_count -> tidb_connection_count
                metric_type = name_without_date.replace('infra_', '')
            else:
                # 其他情况，使用完整名称
                metric_type = name_without_date

            return metric_type

        except Exception as e:
            logger.warning(f"⚠️ 无法从文件名提取指标类型: {filename}, 错误: {e}")
            return filename  # 兜底：使用文件名本身

    def locate_trace_files(self, time_range: TimeRange, entity: EntityInfo) -> DataLocation:
        """定位调用链文件"""
        logger.info(f"🔍 定位调用链文件: {entity.service}, 时间范围: {time_range.start} - {time_range.end}")

        # 调用链文件按小时划分，与日志文件规则一致
        log_location = self.locate_log_files(time_range, entity)

        # 构建调用链文件路径
        trace_file_paths = [
            path.replace("log_filebeat-server", "trace_jaeger-span").replace("log-parquet", "trace-parquet")
            for path in log_location.file_paths
        ]

        return DataLocation(
            file_type="trace",
            root_dir=log_location.root_dir,
            file_paths=trace_file_paths,
            file_matching_rule="调用链文件按小时划分，包含该时间段内的所有Span"
        )

    def locate_other_files(self, time_range: TimeRange, entity: EntityInfo = None) -> DataLocation:
        """定位其他组件指标文件（PD、TiKV等）"""
        logger.info(f"🔍 定位其他组件文件: 时间范围: {time_range.start} - {time_range.end}")

        # 解析时间范围，转换为北京时间
        start_dt = self._parse_utc_to_beijing(time_range.start)
        end_dt = self._parse_utc_to_beijing(time_range.end)

        file_paths = []

        # 遍历时间范围内的每一天
        current_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y-%m-%d")

            # 其他组件文件路径: /data/phaseone_data/{date}/metric-parquet/other/
            other_dir = self.data_root_path / f"{date_str}" / "metric-parquet" / "other"

            if other_dir.exists():
                # 扫描所有other文件: infra_{component}_{metric}_{date}.parquet
                for other_file in other_dir.glob("infra_*_*.parquet"):
                    file_paths.append(other_file)
                    logger.debug(f"✅ 找到其他组件文件: {other_file}")
            else:
                logger.debug(f"⚠️ 其他组件目录不存在: {other_dir}")

            current_dt += timedelta(days=1)

        logger.info(f"📊 其他组件文件定位完成: 找到 {len(file_paths)} 个文件")

        rule = {
            "pattern": "infra_{component}_{metric_name}_{date}.parquet",
            "description": "其他组件指标文件按天存储，包含PD、TiKV等组件",
            "time_granularity": "day"
        }

        root_dir = self.data_root_path / "metric-parquet" / "other"

        return DataLocation(
            file_type="other",
            root_dir=str(root_dir),
            file_paths=file_paths,
            file_matching_rule=rule
        )

    def _parse_utc_to_beijing(self, utc_time_str: str) -> datetime:
        """将UTC时间转换为北京时间"""
        utc_dt = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
        beijing_dt = utc_dt + timedelta(hours=8)
        return beijing_dt
    
    def _parse_pod_name(self, pod_name: str) -> Tuple[str, str]:
        """解析Pod名称，提取服务名和实例号"""
        if '-' in pod_name:
            parts = pod_name.rsplit('-', 1)
            if len(parts) == 2 and parts[1].isdigit():
                return parts[0], parts[1]
        
        # 如果不符合标准格式，返回原名称和0
        return pod_name, "0"
    
    def validate_file_paths(self, data_location: DataLocation) -> Dict[str, Any]:
        """验证文件路径是否存在"""
        validation_result = {
            "valid_files": [],
            "missing_files": [],
            "total_size_mb": 0.0,
            "warnings": []
        }
        
        for relative_path in data_location.file_paths:
            full_path = self.data_root_path / data_location.root_dir / relative_path
            
            if full_path.exists():
                validation_result["valid_files"].append(str(relative_path))
                try:
                    size_mb = full_path.stat().st_size / (1024 * 1024)
                    validation_result["total_size_mb"] += size_mb
                except Exception as e:
                    logger.warning(f"无法获取文件大小: {full_path}, 错误: {e}")
            else:
                validation_result["missing_files"].append(str(relative_path))
                
                # 检查是否是deleted文件
                if "(deleted)" in str(relative_path):
                    validation_result["warnings"].append(
                        f"文件 {relative_path} 包含'deleted'标记，该实例可能已删除，数据可能不完整"
                    )
        
        return validation_result
