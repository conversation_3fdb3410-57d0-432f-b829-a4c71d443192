#!/usr/bin/env python3
"""
LLM客户端管理器
解决事件循环冲突问题
"""

import asyncio
import threading
import logging
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import time

logger = logging.getLogger(__name__)


class LLMClientManager:
    """LLM客户端管理器，解决事件循环冲突"""
    
    def __init__(self):
        self._thread_pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="llm-worker")
        self._lock = threading.Lock()
        self._active_loops = {}
        
    def __del__(self):
        """清理资源"""
        try:
            self._thread_pool.shutdown(wait=False)
        except:
            pass
    
    def call_llm_sync(self, llm_helper, method_name: str, *args, **kwargs) -> Dict[str, Any]:
        """
        同步调用LLM方法，自动处理事件循环
        
        Args:
            llm_helper: LLM助手实例
            method_name: 要调用的方法名
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Dict[str, Any]: LLM调用结果
        """
        if not llm_helper:
            return {}
            
        try:
            # 检查当前是否在事件循环中
            try:
                current_loop = asyncio.get_running_loop()
                # 如果在事件循环中，使用线程池执行
                logger.debug("Running in event loop, using thread pool")
                future = self._thread_pool.submit(
                    self._run_in_new_thread, llm_helper, method_name, *args, **kwargs
                )
                return future.result(timeout=120)  # 2分钟超时
                
            except RuntimeError:
                # 没有运行的事件循环，直接执行
                logger.debug("No running event loop, executing directly")
                return self._run_in_new_thread(llm_helper, method_name, *args, **kwargs)
                
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return {}
    
    def _run_in_new_thread(self, llm_helper, method_name: str, *args, **kwargs) -> Dict[str, Any]:
        """在新线程中运行LLM调用"""
        thread_id = threading.get_ident()
        logger.debug(f"Running LLM call in thread {thread_id}")
        
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            with self._lock:
                self._active_loops[thread_id] = loop
            
            try:
                # 获取要调用的方法
                method = getattr(llm_helper, method_name)
                
                # 执行异步方法
                result = loop.run_until_complete(method(*args, **kwargs))
                logger.debug(f"LLM call completed successfully in thread {thread_id}")
                return result
                
            finally:
                # 清理事件循环
                try:
                    # 取消所有未完成的任务
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    
                    # 等待任务完成
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        
                    # 关闭循环
                    loop.close()
                    
                except Exception as cleanup_error:
                    logger.warning(f"Error during loop cleanup: {cleanup_error}")
                
                with self._lock:
                    self._active_loops.pop(thread_id, None)
                    
        except Exception as e:
            logger.error(f"Error in LLM thread execution: {e}")
            return {}
    
    def cleanup(self):
        """清理所有资源"""
        logger.info("Cleaning up LLM client manager")
        
        # 关闭线程池
        try:
            self._thread_pool.shutdown(wait=True, timeout=30)
        except Exception as e:
            logger.warning(f"Error shutting down thread pool: {e}")
        
        # 清理活跃的事件循环
        with self._lock:
            for thread_id, loop in self._active_loops.items():
                try:
                    if not loop.is_closed():
                        loop.call_soon_threadsafe(loop.stop)
                except Exception as e:
                    logger.warning(f"Error stopping loop in thread {thread_id}: {e}")
            
            self._active_loops.clear()


# 全局实例
_llm_manager = None


def get_llm_manager() -> LLMClientManager:
    """获取全局LLM管理器实例"""
    global _llm_manager
    if _llm_manager is None:
        _llm_manager = LLMClientManager()
    return _llm_manager


def cleanup_llm_manager():
    """清理全局LLM管理器"""
    global _llm_manager
    if _llm_manager is not None:
        _llm_manager.cleanup()
        _llm_manager = None
