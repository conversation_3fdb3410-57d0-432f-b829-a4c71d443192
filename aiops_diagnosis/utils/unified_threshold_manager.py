"""
统一阈值管理器 - 整合所有阈值策略的统一入口
"""

import pandas as pd
import numpy as np
import os
import json
import yaml
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import logging
from pathlib import Path

from .intelligent_threshold_manager import IntelligentThresholdManager
from .adaptive_threshold_manager import AdaptiveThresholdManager
from .multidimensional_threshold_manager import MultiDimensionalThresholdManager


class SimpleDynamicThresholdManager:
    """简化的动态阈值管理器 - 内嵌在统一阈值管理器中"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self.logger = logging.getLogger(self.__class__.__name__)

        # 默认阈值配置
        self.default_thresholds = {
            'apm': {
                'error_ratio': 0.01,
                'rrt': 1000,
                'request': 10000,
                'response': 10000,
            },
            'infra': {
                'cpu_usage': 80.0,
                'memory_usage': 80.0,
                'io_util': 80.0,
                'network_packets': 100000,
                'network_bytes': 104857600,  # 100MB
                'fs_writes_bytes': 102400,   # 100KB
                'fs_reads_bytes': 102400,    # 100KB
                'processes': 1000,
            },
            'other': {
                'store_size': 100*1024*1024*1024,      # 100GB
                'region_pending': 1000000,             # 100万
                'read_mbps': 10*1024*1024*1024,        # 10GB/s
                'write_wal_mbps': 1*1024*1024*1024,    # 1GB/s
                'qps': 10000,
                'grpc_qps': 5000,
                'server_is_up': 0.5,
            }
        }

    def get_threshold(self, category: str, metric_name: str, object_name: str = None) -> float:
        """获取阈值"""
        # 1. 从默认阈值中查找
        if category in self.default_thresholds:
            category_thresholds = self.default_thresholds[category]
            if metric_name in category_thresholds:
                return category_thresholds[metric_name]

        # 2. 根据指标名称模糊匹配
        for cat_name, cat_thresholds in self.default_thresholds.items():
            for threshold_key, threshold_value in cat_thresholds.items():
                if threshold_key in metric_name.lower():
                    return threshold_value

        # 3. 最终fallback
        return 5000.0


class UnifiedThresholdManager:
    """
    统一阈值管理器 - 所有阈值获取的统一入口
    
    优先级顺序：
    1. IntelligentThresholdManager (校准数据) - 最高优先级
    2. AdaptiveThresholdManager (历史数据) - 中等优先级  
    3. MultiDimensionalThresholdManager (多维度) - 专用优先级
    4. DynamicThresholdManager (配置文件) - 基础优先级
    """
    
    def __init__(self, 
                 data_root: str = "/data/phaseone_data",
                 threshold_config_path: str = None,
                 calibration_data_path: str = "calibrated_thresholds.json",
                 adaptive_config_path: str = None):
        """
        初始化统一阈值管理器
        
        Args:
            data_root: 数据根目录
            threshold_config_path: 阈值配置文件路径
            calibration_data_path: 校准数据文件路径
            adaptive_config_path: 自适应配置文件路径
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data_root = data_root
        
        # 初始化各个阈值管理器
        self._init_managers(
            threshold_config_path, 
            calibration_data_path, 
            adaptive_config_path
        )
        
        # 组件类型映射
        self.tidb_components = {'tidb', 'tikv', 'pd'}
        self.infra_components = {'node', 'pod', 'container'}
        self.apm_components = {'service', 'application'}
        
        self.logger.info("✅ 统一阈值管理器初始化完成")
    
    def _init_managers(self, threshold_config_path: str, 
                      calibration_data_path: str, 
                      adaptive_config_path: str):
        """初始化各个阈值管理器"""
        try:
            # 1. 智能阈值管理器 (最高优先级)
            self.intelligent_manager = IntelligentThresholdManager(
                config_path=None,
                calibration_data_path=calibration_data_path
            )
            self.logger.info("✅ 智能阈值管理器初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 智能阈值管理器初始化失败: {e}")
            self.intelligent_manager = None
        
        try:
            # 2. 自适应阈值管理器 (中等优先级)
            self.adaptive_manager = AdaptiveThresholdManager(
                data_root=self.data_root,
                config_path=adaptive_config_path,
                lookback_days=7
            )
            self.logger.info("✅ 自适应阈值管理器初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 自适应阈值管理器初始化失败: {e}")
            self.adaptive_manager = None
        
        try:
            # 3. 多维度阈值管理器 (专用优先级)
            self.multidim_manager = MultiDimensionalThresholdManager(
                data_root=self.data_root,
                lookback_days=7
            )
            self.logger.info("✅ 多维度阈值管理器初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 多维度阈值管理器初始化失败: {e}")
            self.multidim_manager = None
        
        try:
            # 4. 简化动态阈值管理器 (基础优先级)
            self.dynamic_manager = SimpleDynamicThresholdManager(threshold_config_path)
            self.logger.info("✅ 简化动态阈值管理器初始化成功")

        except Exception as e:
            self.logger.error(f"❌ 简化动态阈值管理器初始化失败: {e}")
            self.dynamic_manager = None
    
    def get_threshold(self, category: str, metric_name: str, 
                     object_name: str = None, 
                     historical_data: pd.Series = None,
                     current_time: str = None) -> Tuple[float, str, Dict[str, Any]]:
        """
        获取阈值 - 统一入口
        
        Args:
            category: 指标类别 (apm, infra, other)
            metric_name: 指标名称
            object_name: 对象名称
            historical_data: 历史数据
            current_time: 当前时间
            
        Returns:
            (threshold_value, threshold_type, metadata)
        """
        try:
            # 确定组件类型
            component_type = self._determine_component_type(category, object_name)

            # 🔧 修复：对于'other'类别，尝试从指标名称推断组件类型
            if component_type == 'other' or category == 'other':
                inferred_type = self._infer_component_from_metric(metric_name)
                if inferred_type:
                    component_type = inferred_type

            # 1. 优先使用智能阈值管理器 (对于TiDB集群组件)
            if (self.intelligent_manager and
                component_type in self.tidb_components):

                threshold, threshold_type, metadata = self.intelligent_manager.get_intelligent_threshold(
                    component_type, metric_name, historical_data
                )

                # 如果获取到有效阈值，直接返回
                if threshold > 0 and metadata.get('source') in ['calibrated', 'calculated']:
                    self.logger.debug(f"🧠 使用智能阈值: {component_type}.{metric_name} = {threshold:.4f} ({threshold_type})")
                    return threshold, threshold_type, metadata
            
            # 2. 尝试使用自适应阈值管理器
            if self.adaptive_manager and current_time:
                try:
                    adaptive_threshold = self.adaptive_manager.get_adaptive_threshold(
                        component_type, metric_name, current_time
                    )
                    if adaptive_threshold > 0:
                        metadata = {
                            'source': 'adaptive',
                            'strategy': 'historical_data',
                            'unit': 'unknown',
                            'confidence': 0.7,
                            'data_points': 0
                        }
                        self.logger.debug(f"🔄 使用自适应阈值: {component_type}.{metric_name} = {adaptive_threshold:.4f}")
                        return adaptive_threshold, 'adaptive', metadata
                        
                except Exception as e:
                    self.logger.debug(f"自适应阈值获取失败: {e}")
            
            # 3. 尝试使用多维度阈值管理器 (对于TiDB集群)
            if (self.multidim_manager and 
                component_type in self.tidb_components and 
                current_time):
                try:
                    multidim_result = self.multidim_manager.get_multidimensional_threshold(
                        component_type, current_time
                    )
                    if multidim_result and metric_name in multidim_result:
                        threshold_info = multidim_result[metric_name]
                        threshold = threshold_info.get('threshold', 0)
                        if threshold > 0:
                            metadata = {
                                'source': 'multidimensional',
                                'strategy': 'multi_metric_correlation',
                                'unit': 'unknown',
                                'confidence': 0.6,
                                'data_points': 0
                            }
                            self.logger.debug(f"📊 使用多维度阈值: {component_type}.{metric_name} = {threshold:.4f}")
                            return threshold, 'multidimensional', metadata
                            
                except Exception as e:
                    self.logger.debug(f"多维度阈值获取失败: {e}")
            
            # 4. 最后使用动态阈值管理器 (基础fallback)
            if self.dynamic_manager:
                threshold = self.dynamic_manager.get_threshold(category, metric_name, object_name)
                metadata = {
                    'source': 'dynamic',
                    'strategy': 'configuration',
                    'unit': 'unknown',
                    'confidence': 0.5,
                    'data_points': 0
                }
                self.logger.debug(f"⚙️ 使用动态阈值: {category}.{metric_name} = {threshold:.4f}")
                return threshold, 'dynamic', metadata
            
            # 5. 最终fallback
            fallback_threshold = self._get_final_fallback(metric_name)
            # 🚀 应用服务感知调整
            adjusted_threshold = self._apply_service_aware_adjustment(fallback_threshold, metric_name, object_name or 'unknown')
            metadata = {
                'source': 'fallback',
                'strategy': 'hardcoded',
                'unit': 'unknown',
                'confidence': 0.3,
                'data_points': 0,
                'original_threshold': fallback_threshold,
                'adjustment_applied': adjusted_threshold != fallback_threshold
            }
            self.logger.warning(f"⚠️ 使用最终fallback阈值: {metric_name} = {adjusted_threshold} (原始: {fallback_threshold})")
            return adjusted_threshold, 'fallback', metadata
            
        except Exception as e:
            self.logger.error(f"获取阈值失败 {category}.{metric_name}: {e}")
            fallback_threshold = self._get_final_fallback(metric_name)
            metadata = {
                'source': 'error_fallback',
                'strategy': 'hardcoded',
                'unit': 'unknown',
                'confidence': 0.1,
                'data_points': 0
            }
            return fallback_threshold, 'error_fallback', metadata
    
    def _determine_component_type(self, category: str, object_name: str = None) -> str:
        """确定组件类型"""
        # 1. 根据对象名称判断
        if object_name:
            object_lower = object_name.lower()
            if 'tidb' in object_lower:
                return 'tidb'
            elif 'tikv' in object_lower:
                return 'tikv'
            elif 'pd' in object_lower:
                return 'pd'

        # 2. 根据类别判断
        if category == 'other':
            # 🔧 修复：other类别需要进一步判断，不能默认为tikv
            # 如果没有明确的对象名称，需要通过其他方式判断
            return 'other'  # 返回原始类别，让调用方进一步处理
        elif category == 'apm':
            return 'service'
        elif category == 'infra':
            return 'node'

        # 3. 默认返回
        return 'unknown'

    def _infer_component_from_metric(self, metric_name: str) -> str:
        """从指标名称推断组件类型"""
        metric_lower = metric_name.lower()

        # TiKV相关指标
        tikv_indicators = [
            'store_size', 'region_pending', 'read_mbps', 'write_wal_mbps',
            'io_util', 'grpc_qps', 'qps', 'server_is_up', 'raft_apply_wait',
            'raft_propose_wait', 'rocksdb_write_stall', 'threadpool_readpool_cpu',
            'snapshot_apply_count', 'available_size', 'capacity_size'
        ]

        # TiDB相关指标
        tidb_indicators = [
            'connection_count', 'duration_95th', 'duration_99th', 'duration_avg',
            'top_sql_cpu', 'uptime', 'transaction_retry', 'failed_query_ops',
            'block_cache_size'
        ]

        # PD相关指标
        pd_indicators = [
            'leader_primary', 'abnormal_region_count', 'region_count', 'region_health',
            'store_up_count', 'store_down_count', 'store_unhealth_count', 'store_slow_count',
            'store_low_space_count', 'witness_count', 'learner_count', 'leader_count',
            'storage_capacity', 'storage_size', 'storage_used_ratio'
        ]

        # 检查指标名称匹配
        for indicator in tikv_indicators:
            if indicator in metric_lower:
                return 'tikv'

        for indicator in tidb_indicators:
            if indicator in metric_lower:
                return 'tidb'

        for indicator in pd_indicators:
            if indicator in metric_lower:
                return 'pd'

        return None

    def _apply_service_aware_adjustment(self, base_threshold: float, metric_name: str, component_id: str) -> float:
        """🚀 应用服务感知的阈值调整"""
        try:
            # 服务重要性调整因子
            service_criticality = {
                # 核心业务服务 - 更严格的阈值
                'paymentservice': 0.6,      # 支付服务最关键
                'checkoutservice': 0.7,     # 结账服务次关键
                'cartservice': 0.8,         # 购物车服务

                # 用户体验服务 - 中等严格
                'frontend': 0.9,            # 前端服务
                'productcatalogservice': 0.9, # 商品目录服务
                'recommendationservice': 1.2, # 推荐服务可以宽松一些

                # 支撑服务 - 相对宽松
                'adservice': 1.1,           # 广告服务
                'currencyservice': 1.0,     # 货币服务
                'emailservice': 1.3,        # 邮件服务
                'shippingservice': 1.0,     # 物流服务

                # 基础设施服务 - 根据类型调整
                'redis-cart': 0.8,          # Redis缓存很重要
                'tidb': 0.7,               # 数据库最重要
                'tikv': 0.7,               # 存储很重要
                'pd': 0.8,                 # PD协调器重要
            }

            # 指标类型调整因子
            metric_adjustments = {
                # 响应时间指标 - 根据服务类型调整
                'response_time': {
                    'paymentservice': 0.5,    # 支付要求极快响应
                    'frontend': 0.8,          # 前端用户体验重要
                    'database': 0.6,          # 数据库响应要快
                },

                # 错误率指标 - 核心服务更严格
                'error_ratio': {
                    'paymentservice': 0.3,    # 支付错误率要极低
                    'checkoutservice': 0.5,   # 结账错误率要低
                    'frontend': 0.7,          # 前端可以稍微宽松
                },

                # 内存使用 - 根据服务特性调整
                'memory': {
                    'adservice': 2.0,         # 广告服务内存使用可以高一些
                    'recommendationservice': 1.5, # 推荐服务需要更多内存
                    'redis': 1.8,             # Redis内存使用天然较高
                }
            }

            # 获取基础调整因子
            adjustment_factor = 1.0
            component_lower = component_id.lower()

            # 应用服务重要性调整
            for service, factor in service_criticality.items():
                if service in component_lower:
                    adjustment_factor *= factor
                    self.logger.debug(f"🎯 服务重要性调整: {component_id} -> {factor}x")
                    break

            # 应用指标类型调整
            metric_lower = metric_name.lower()
            for metric_type, service_factors in metric_adjustments.items():
                if metric_type in metric_lower:
                    for service, factor in service_factors.items():
                        if service in component_lower:
                            adjustment_factor *= factor
                            self.logger.debug(f"🎯 指标类型调整: {metric_name}@{component_id} -> {factor}x")
                            break
                    break

            # 计算最终阈值
            adjusted_threshold = base_threshold * adjustment_factor

            if adjustment_factor != 1.0:
                self.logger.info(f"🚀 服务感知调整: {metric_name}@{component_id}: {base_threshold} -> {adjusted_threshold} (factor: {adjustment_factor:.2f})")

            return adjusted_threshold

        except Exception as e:
            self.logger.error(f"服务感知调整失败: {e}")
            return base_threshold

    def _get_final_fallback(self, metric_name: str) -> float:
        """获取最终fallback阈值"""
        # 🚀 全新设计：基于现实的分层阈值体系
        fallback_map = {
            # ===== 基础设施层指标 =====
            # CPU指标 (百分比)
            'cpu_usage': 85.0,
            'cpu_usage_rate': 85.0,
            'node_cpu_usage_rate': 85.0,
            'pod_cpu_usage': 80.0,

            # 内存指标 (字节)
            'memory_usage': 8*1024*1024*1024,                    # 8GB Pod内存使用
            'memory_working_set_bytes': 8*1024*1024*1024,        # 8GB Pod工作集内存
            'pod_memory_working_set_bytes': 8*1024*1024*1024,    # 8GB Pod内存
            'memory_memtotal_bytes': 128*1024*1024*1024,         # 128GB 节点总内存
            'node_memory_memtotal_bytes': 128*1024*1024*1024,    # 128GB 节点总内存
            'memory_memavailable_bytes': 8*1024*1024*1024,       # 8GB 可用内存
            'node_memory_memavailable_bytes': 8*1024*1024*1024,  # 8GB 节点可用内存
            'memory_usage_rate': 85.0,                           # 85% 内存使用率
            'node_memory_usage_rate': 85.0,                      # 85% 节点内存使用率

            # 磁盘指标 (字节)
            'filesystem_size_bytes': 500*1024*1024*1024,         # 500GB 文件系统大小
            'node_filesystem_size_bytes': 500*1024*1024*1024,    # 500GB 节点文件系统
            'filesystem_free_bytes': 50*1024*1024*1024,          # 50GB 可用磁盘空间
            'node_filesystem_free_bytes': 50*1024*1024*1024,     # 50GB 节点可用磁盘
            'filesystem_usage_rate': 85.0,                       # 85% 磁盘使用率
            'node_filesystem_usage_rate': 85.0,                  # 85% 节点磁盘使用率
            'filesystem_writes_bytes': 100*1024*1024,            # 100MB 磁盘写入
            'node_filesystem_writes_bytes': 100*1024*1024,       # 100MB 节点磁盘写入

            # 网络指标 (字节/包)
            'network_transmit_bytes': 100*1024*1024,             # 100MB 网络发送
            'network_receive_bytes': 100*1024*1024,              # 100MB 网络接收
            'node_network_transmit_bytes_total': 500*1024*1024,  # 500MB 节点网络发送
            'node_network_receive_bytes_total': 500*1024*1024,   # 500MB 节点网络接收
            'pod_network_transmit_bytes': 50*1024*1024,          # 50MB Pod网络发送
            'pod_network_receive_bytes': 50*1024*1024,           # 50MB Pod网络接收
            'network_packets': 1000000,                          # 100万个包

            # ===== 应用层指标 =====
            # 响应时间指标 (毫秒) - 基于服务类型的合理阈值
            'response_time': 8000,        # 8秒 - 通用服务响应时间
            'response_time_max': 15000,   # 15秒 - 最大响应时间
            'rrt': 8000,                  # 8秒 - 平均响应时间
            'rrt_max': 15000,             # 15秒 - 最大响应时间

            # 错误率指标 (比例 0-1)
            'error_ratio': 0.05,          # 5% - 通用错误率
            'client_error_ratio': 0.08,   # 8% - 客户端错误率 (4xx)
            'server_error_ratio': 0.02,   # 2% - 服务端错误率 (5xx)

            # QPS指标
            'qps': 50000,                 # 5万QPS
            'request_rate': 50000,        # 5万请求/秒
            'grpc_qps': 30000,            # 3万gRPC QPS

            # ===== 数据库层指标 =====
            # TiDB/TiKV指标
            'store_size': 1024*1024*1024*1024,     # 1TB 存储大小
            'region_pending': 10000,               # 1万个pending region
            'tikv_store_size_bytes': 1024*1024*1024*1024,  # 1TB TiKV存储
            'tikv_region_count': 100000,           # 10万个region
            'pd_tso_wait_duration': 100,           # 100ms PD TSO等待时间

            # Redis指标
            'redis_memory_usage': 16*1024*1024*1024,  # 16GB Redis内存
            'redis_connected_clients': 10000,         # 1万个连接
            'redis_ops_per_sec': 100000,              # 10万ops/秒

            # ===== 其他指标 =====
            'io_util': 85.0,              # 85% IO使用率
            'processes': 5000,            # 5000个进程
            'server_is_up': 0.5,          # 50% 服务可用性
        }
        
        # 🚀 智能指标名称匹配系统
        metric_lower = metric_name.lower().strip()

        # 第一步：精确匹配 (最高优先级)
        if metric_lower in fallback_map:
            threshold = fallback_map[metric_lower]
            self.logger.debug(f"🎯 精确匹配: {metric_name} -> {threshold}")
            return threshold

        # 第二步：标准化指标名称映射
        normalized_mappings = {
            # 内存指标标准化
            'node_memory_memtotal_bytes': 'node_memory_memtotal_bytes',
            'node_memory_memavailable_bytes': 'node_memory_memavailable_bytes',
            'pod_memory_working_set_bytes': 'pod_memory_working_set_bytes',
            'memory_working_set_bytes': 'memory_working_set_bytes',

            # 文件系统指标标准化
            'node_filesystem_size_bytes': 'node_filesystem_size_bytes',
            'node_filesystem_free_bytes': 'node_filesystem_free_bytes',
            'filesystem_size_bytes': 'filesystem_size_bytes',
            'filesystem_free_bytes': 'filesystem_free_bytes',

            # 网络指标标准化
            'node_network_transmit_bytes_total': 'node_network_transmit_bytes_total',
            'node_network_receive_bytes_total': 'node_network_receive_bytes_total',
            'pod_network_transmit_bytes': 'pod_network_transmit_bytes',
            'pod_network_receive_bytes': 'pod_network_receive_bytes',

            # 响应时间指标标准化
            'rrt': 'rrt',
            'rrt_max': 'rrt_max',
            'response_time': 'response_time',
            'response_time_max': 'response_time_max',

            # CPU指标标准化
            'pod_cpu_usage': 'pod_cpu_usage',
            'node_cpu_usage_rate': 'node_cpu_usage_rate',
            'cpu_usage': 'cpu_usage',

            # 错误率指标标准化
            'error_ratio': 'error_ratio',
            'client_error_ratio': 'client_error_ratio',
            'server_error_ratio': 'server_error_ratio',
        }

        if metric_lower in normalized_mappings:
            normalized_key = normalized_mappings[metric_lower]
            if normalized_key in fallback_map:
                threshold = fallback_map[normalized_key]
                self.logger.debug(f"🔄 标准化匹配: {metric_name} -> {normalized_key} -> {threshold}")
                return threshold

        # 第三步：模糊匹配 (按优先级排序)
        fuzzy_patterns = [
            # 高优先级：精确子串匹配
            ('memory_memtotal', 'node_memory_memtotal_bytes'),
            ('memory_memavailable', 'node_memory_memavailable_bytes'),
            ('memory_working_set', 'memory_working_set_bytes'),
            ('filesystem_size', 'filesystem_size_bytes'),
            ('filesystem_free', 'filesystem_free_bytes'),
            ('network_transmit', 'network_transmit_bytes'),
            ('network_receive', 'network_receive_bytes'),

            # 中优先级：关键词匹配
            ('error_ratio', 'error_ratio'),
            ('response_time', 'response_time'),
            ('cpu_usage', 'cpu_usage'),
            ('memory_usage', 'memory_usage'),

            # 低优先级：通用匹配
            ('rrt', 'rrt'),
            ('qps', 'qps'),
        ]

        for pattern, fallback_key in fuzzy_patterns:
            if pattern in metric_lower and fallback_key in fallback_map:
                threshold = fallback_map[fallback_key]
                self.logger.debug(f"🔍 模糊匹配: {metric_name} -> {pattern} -> {fallback_key} -> {threshold}")
                return threshold

        # 第四步：基于指标类型的智能推断
        if any(keyword in metric_lower for keyword in ['memory', 'mem']):
            if 'total' in metric_lower or 'size' in metric_lower:
                threshold = 128*1024*1024*1024  # 128GB
            else:
                threshold = 8*1024*1024*1024    # 8GB
            self.logger.debug(f"💡 内存类型推断: {metric_name} -> {threshold}")
            return threshold

        if any(keyword in metric_lower for keyword in ['filesystem', 'disk', 'storage']):
            if 'size' in metric_lower or 'total' in metric_lower:
                threshold = 500*1024*1024*1024  # 500GB
            else:
                threshold = 50*1024*1024*1024   # 50GB
            self.logger.debug(f"💡 磁盘类型推断: {metric_name} -> {threshold}")
            return threshold

        if any(keyword in metric_lower for keyword in ['network', 'bytes', 'transmit', 'receive']):
            threshold = 100*1024*1024  # 100MB
            self.logger.debug(f"💡 网络类型推断: {metric_name} -> {threshold}")
            return threshold

        if any(keyword in metric_lower for keyword in ['error', 'ratio']):
            threshold = 0.05  # 5%
            self.logger.debug(f"💡 错误率类型推断: {metric_name} -> {threshold}")
            return threshold

        if any(keyword in metric_lower for keyword in ['response', 'latency', 'rrt']):
            threshold = 8000  # 8秒
            self.logger.debug(f"💡 响应时间类型推断: {metric_name} -> {threshold}")
            return threshold

        # 最后的默认值
        threshold = 10000.0
        self.logger.warning(f"⚠️ 使用默认阈值: {metric_name} -> {threshold}")
        return threshold
    
    def is_anomaly(self, category: str, metric_name: str, value: float,
                   threshold: float, threshold_type: str, metadata: Dict[str, Any],
                   object_name: str = None) -> bool:
        """判断是否为异常"""
        try:
            component_type = self._determine_component_type(category, object_name)
            
            # 1. 优先使用智能阈值管理器的异常判断
            if (self.intelligent_manager and 
                component_type in self.tidb_components and
                threshold_type in ['intelligent', 'calculated']):
                
                return self.intelligent_manager.is_anomaly(
                    component_type, metric_name, value, threshold, threshold_type, metadata
                )
            
            # 2. 使用传统异常判断逻辑
            return self._traditional_anomaly_check(metric_name, value, threshold)
            
        except Exception as e:
            self.logger.debug(f"异常判断失败: {e}")
            return value > threshold
    
    def _traditional_anomaly_check(self, metric_name: str, value: float, threshold: float) -> bool:
        """传统异常判断逻辑"""
        # 反向逻辑指标 (值越低越异常)
        reverse_metrics = [
            'health', 'up_count', 'leader_count', 'capacity', 
            'available', 'server_is_up', 'free'
        ]
        
        for reverse_metric in reverse_metrics:
            if reverse_metric in metric_name.lower():
                return value < threshold
        
        # 正向逻辑指标 (值越高越异常)
        return value > threshold
    
    def get_threshold_explanation(self, threshold_type: str, metadata: Dict[str, Any]) -> str:
        """获取阈值解释"""
        source = metadata.get('source', 'unknown')
        strategy = metadata.get('strategy', 'unknown')
        confidence = metadata.get('confidence', 0.5)
        data_points = metadata.get('data_points', 0)
        
        explanations = {
            'intelligent': f"智能阈值 (校准数据, 置信度{confidence:.1%})",
            'calculated': f"计算阈值 (历史数据{data_points}点, 置信度{confidence:.1%})",
            'adaptive': f"自适应阈值 (历史数据, 置信度{confidence:.1%})",
            'multidimensional': f"多维度阈值 (关联分析, 置信度{confidence:.1%})",
            'dynamic': f"动态阈值 (配置文件, 置信度{confidence:.1%})",
            'fallback': f"Fallback阈值 (默认值, 置信度{confidence:.1%})",
            'error_fallback': f"错误Fallback阈值 (硬编码, 置信度{confidence:.1%})"
        }
        
        return explanations.get(threshold_type, f"未知阈值类型: {threshold_type}")

    def get_threshold_explanation(self, threshold_type: str, metadata: Dict[str, Any]) -> str:
        """
        获取阈值解释（简化版本，兼容旧接口）

        Args:
            threshold_type: 阈值类型
            metadata: 阈值元数据

        Returns:
            阈值解释字符串
        """
        return self.get_threshold_explanation_detailed(threshold_type, metadata)
