"""
全局并发限制器
提供统一的并发控制和资源管理
"""

import asyncio
import time
import psutil
from typing import Optional
from ..config.config_manager import config_manager

class GlobalConcurrencyLimiter:
    """全局并发限制器"""
    
    _instance: Optional['GlobalConcurrencyLimiter'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not getattr(self, '_initialized', False):
            self.config = config_manager.config
            self.global_semaphore = asyncio.Semaphore(self.config.global_max_concurrent)
            self.llm_semaphore = asyncio.Semaphore(self.config.llm_max_concurrent)
            self._last_check_time = 0
            self._check_interval = 10  # 每10秒检查一次系统资源
            self._initialized = True
    
    async def acquire_global(self):
        """获取全局并发许可"""
        await self._check_and_adjust_concurrency()
        await self.global_semaphore.acquire()
    
    async def acquire_llm(self):
        """获取LLM并发许可"""
        await self._check_and_adjust_concurrency()
        await self.llm_semaphore.acquire()
    
    def release_global(self):
        """释放全局并发许可"""
        self.global_semaphore.release()
    
    def release_llm(self):
        """释放LLM并发许可"""
        self.llm_semaphore.release()
    
    async def _check_and_adjust_concurrency(self):
        """检查系统资源并动态调整并发度"""
        current_time = time.time()
        if current_time - self._last_check_time < self._check_interval:
            return
        
        self._last_check_time = current_time
        
        if not self.config.enable_adaptive_concurrency:
            return
        
        try:
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory_info = psutil.virtual_memory()
            memory_percent = memory_info.percent
            
            # 如果资源使用过高，降低并发度
            if cpu_percent > self.config.cpu_threshold_percent or memory_percent > 85:
                await self._reduce_concurrency()
            elif cpu_percent < 50 and memory_percent < 60:
                await self._increase_concurrency()
                
        except Exception as e:
            # 如果无法获取系统信息，忽略错误
            pass
    
    async def _reduce_concurrency(self):
        """降低并发度"""
        # 这里可以实现动态降低并发度的逻辑
        # 由于asyncio.Semaphore不支持动态调整，这里只是记录
        pass
    
    async def _increase_concurrency(self):
        """增加并发度"""
        # 这里可以实现动态增加并发度的逻辑
        pass
    
    def get_current_usage(self) -> dict:
        """获取当前并发使用情况"""
        return {
            "global_available": self.global_semaphore._value,
            "global_max": self.config.global_max_concurrent,
            "llm_available": self.llm_semaphore._value,
            "llm_max": self.config.llm_max_concurrent,
            "global_usage_percent": (1 - self.global_semaphore._value / self.config.global_max_concurrent) * 100,
            "llm_usage_percent": (1 - self.llm_semaphore._value / self.config.llm_max_concurrent) * 100
        }


class ConcurrencyContext:
    """并发上下文管理器"""
    
    def __init__(self, limiter: GlobalConcurrencyLimiter, context_type: str = "global"):
        self.limiter = limiter
        self.context_type = context_type
    
    async def __aenter__(self):
        if self.context_type == "llm":
            await self.limiter.acquire_llm()
        else:
            await self.limiter.acquire_global()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.context_type == "llm":
            self.limiter.release_llm()
        else:
            self.limiter.release_global()


# 全局限制器实例
global_limiter = GlobalConcurrencyLimiter()


# 便捷函数
async def with_global_concurrency():
    """全局并发上下文管理器"""
    return ConcurrencyContext(global_limiter, "global")

async def with_llm_concurrency():
    """LLM并发上下文管理器"""
    return ConcurrencyContext(global_limiter, "llm")
