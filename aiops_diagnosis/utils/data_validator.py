"""
数据校验工具 - 提供DataFrame和数据质量校验
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass

@dataclass
class ValidationResult:
    """数据校验结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    data_quality_score: float
    validated_data: Optional[pd.DataFrame] = None

class DataValidator:
    """数据校验器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据校验器
        
        Args:
            config: 校验配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 获取校验配置
        validation_config = config.get('data_validation', {})
        self.required_columns = validation_config.get('required_columns', [])
        self.optional_columns = validation_config.get('optional_columns', [])
        
        # 数据质量检查配置
        quality_config = validation_config.get('quality_checks', {})
        self.min_duration = quality_config.get('min_duration', 0)
        self.max_duration = quality_config.get('max_duration', 600000)  # 10分钟
        self.valid_statuses = quality_config.get('valid_statuses', ['ok', 'error', 'failed', 'timeout'])
    
    def validate_dataframe(self, df: pd.DataFrame, data_type: str = "traces") -> ValidationResult:
        """
        验证DataFrame的完整性和质量
        
        Args:
            df: 待验证的DataFrame
            data_type: 数据类型（traces, metrics, logs）
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        quality_score = 1.0
        
        try:
            # 1. 基本检查
            if df is None:
                errors.append("DataFrame为None")
                return ValidationResult(False, errors, warnings, 0.0)
            
            if df.empty:
                errors.append("DataFrame为空")
                return ValidationResult(False, errors, warnings, 0.0)
            
            # 2. 列存在性检查
            missing_required = self._check_required_columns(df)
            if missing_required:
                errors.extend([f"缺少必需列: {col}" for col in missing_required])
                quality_score *= 0.5  # 缺少必需列严重影响质量
            
            # 3. 数据类型检查
            type_issues = self._check_data_types(df, data_type)
            if type_issues:
                warnings.extend(type_issues)
                quality_score *= 0.9
            
            # 4. 数据质量检查
            quality_issues = self._check_data_quality(df, data_type)
            if quality_issues:
                warnings.extend(quality_issues)
                quality_score *= 0.8
            
            # 5. 数据完整性检查
            completeness_score = self._check_data_completeness(df)
            quality_score *= completeness_score
            
            # 6. 数据清理
            cleaned_df = self._clean_data(df, data_type)
            
            # 判断是否通过验证
            is_valid = len(errors) == 0 and quality_score > 0.3
            
            if is_valid:
                self.logger.info(f"数据验证通过，质量分数: {quality_score:.2f}")
            else:
                self.logger.warning(f"数据验证失败，质量分数: {quality_score:.2f}")
            
            return ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings,
                data_quality_score=quality_score,
                validated_data=cleaned_df if is_valid else None
            )
            
        except Exception as e:
            self.logger.error(f"数据验证过程中发生异常: {e}")
            errors.append(f"验证异常: {e}")
            return ValidationResult(False, errors, warnings, 0.0)
    
    def _check_required_columns(self, df: pd.DataFrame) -> List[str]:
        """检查必需列是否存在"""
        missing_columns = []
        
        for col in self.required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        return missing_columns
    
    def _check_data_types(self, df: pd.DataFrame, data_type: str) -> List[str]:
        """检查数据类型"""
        issues = []
        
        try:
            if data_type == "traces":
                # 检查duration列
                if 'duration' in df.columns:
                    if not pd.api.types.is_numeric_dtype(df['duration']):
                        # 尝试转换为数值类型
                        try:
                            df['duration'] = pd.to_numeric(df['duration'], errors='coerce')
                            issues.append("duration列已转换为数值类型")
                        except:
                            issues.append("duration列无法转换为数值类型")
                
                # 检查时间戳列
                time_columns = ['startTime', 'timestamp', '@timestamp']
                for col in time_columns:
                    if col in df.columns:
                        if not pd.api.types.is_datetime64_any_dtype(df[col]):
                            try:
                                df[col] = pd.to_datetime(df[col], errors='coerce')
                                issues.append(f"{col}列已转换为时间类型")
                            except:
                                issues.append(f"{col}列无法转换为时间类型")
            
            elif data_type == "metrics":
                # 检查数值列
                numeric_columns = ['value', 'cpu_usage', 'memory_usage']
                for col in numeric_columns:
                    if col in df.columns:
                        if not pd.api.types.is_numeric_dtype(df[col]):
                            try:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                                issues.append(f"{col}列已转换为数值类型")
                            except:
                                issues.append(f"{col}列无法转换为数值类型")
        
        except Exception as e:
            issues.append(f"数据类型检查异常: {e}")
        
        return issues
    
    def _check_data_quality(self, df: pd.DataFrame, data_type: str) -> List[str]:
        """检查数据质量"""
        issues = []
        
        try:
            if data_type == "traces":
                # 检查duration范围
                if 'duration' in df.columns:
                    invalid_durations = df[
                        (df['duration'] < self.min_duration) | 
                        (df['duration'] > self.max_duration)
                    ]
                    
                    if not invalid_durations.empty:
                        issues.append(f"发现{len(invalid_durations)}条异常duration记录")
                
                # 检查status值
                if 'status' in df.columns:
                    invalid_statuses = df[~df['status'].isin(self.valid_statuses)]
                    if not invalid_statuses.empty:
                        unique_invalid = invalid_statuses['status'].unique()
                        issues.append(f"发现无效status值: {list(unique_invalid)}")
            
            # 检查重复记录
            if len(df) > 0:
                duplicate_count = df.duplicated().sum()
                if duplicate_count > 0:
                    issues.append(f"发现{duplicate_count}条重复记录")
        
        except Exception as e:
            issues.append(f"数据质量检查异常: {e}")
        
        return issues
    
    def _check_data_completeness(self, df: pd.DataFrame) -> float:
        """检查数据完整性，返回完整性分数"""
        try:
            total_cells = df.size
            if total_cells == 0:
                return 0.0
            
            # 计算非空值比例
            non_null_cells = df.count().sum()
            completeness_score = non_null_cells / total_cells
            
            # 检查关键列的完整性
            key_columns = ['duration', 'service', 'timestamp']
            key_completeness = []
            
            for col in key_columns:
                if col in df.columns:
                    col_completeness = df[col].count() / len(df)
                    key_completeness.append(col_completeness)
            
            if key_completeness:
                # 关键列完整性权重更高
                weighted_score = (completeness_score * 0.6 + 
                                np.mean(key_completeness) * 0.4)
            else:
                weighted_score = completeness_score
            
            return min(weighted_score, 1.0)
            
        except Exception as e:
            self.logger.error(f"完整性检查异常: {e}")
            return 0.5  # 异常时返回中等分数
    
    def _clean_data(self, df: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """清理数据"""
        try:
            cleaned_df = df.copy()
            
            if data_type == "traces":
                # 过滤duration缺失或异常的记录
                if 'duration' in cleaned_df.columns:
                    original_count = len(cleaned_df)
                    # 移除duration缺失的记录
                    cleaned_df = cleaned_df.dropna(subset=['duration'])
                    # 移除异常值
                    cleaned_df = cleaned_df[
                        (cleaned_df['duration'] >= self.min_duration) &
                        (cleaned_df['duration'] <= self.max_duration)
                    ]
                    filtered_count = original_count - len(cleaned_df)
                    if filtered_count > 0:
                        self.logger.info(f"过滤掉 {filtered_count} 条duration缺失或异常的记录")
                
                # 过滤掉service缺失的记录，而不是填充unknown
                if 'service' in cleaned_df.columns:
                    # 记录过滤前的数量
                    original_count = len(cleaned_df)
                    # 过滤掉service为空或无效的记录
                    cleaned_df = cleaned_df.dropna(subset=['service'])
                    cleaned_df = cleaned_df[cleaned_df['service'].str.strip() != '']
                    filtered_count = original_count - len(cleaned_df)
                    if filtered_count > 0:
                        self.logger.info(f"过滤掉 {filtered_count} 条service缺失的记录")
                
                # 过滤掉status无效的记录，而不是替换为unknown
                if 'status' in cleaned_df.columns:
                    original_count = len(cleaned_df)
                    # 只保留有效status的记录
                    cleaned_df = cleaned_df[cleaned_df['status'].isin(self.valid_statuses)]
                    filtered_count = original_count - len(cleaned_df)
                    if filtered_count > 0:
                        self.logger.info(f"过滤掉 {filtered_count} 条status无效的记录")
            
            # 移除完全重复的行
            cleaned_df.drop_duplicates(inplace=True)
            
            # 重置索引
            cleaned_df.reset_index(drop=True, inplace=True)
            
            return cleaned_df
            
        except Exception as e:
            self.logger.error(f"数据清理异常: {e}")
            return df  # 清理失败时返回原数据
    
    def safe_column_access(self, df: pd.DataFrame, column: str, 
                          default_value: Any = None) -> pd.Series:
        """
        安全访问DataFrame列
        
        Args:
            df: DataFrame
            column: 列名
            default_value: 默认值
            
        Returns:
            列数据或默认值填充的Series
        """
        if column in df.columns:
            return df[column]
        else:
            self.logger.warning(f"列 '{column}' 不存在，使用默认值 '{default_value}'")
            return pd.Series([default_value] * len(df), index=df.index)
    
    def validate_and_extract_columns(self, df: pd.DataFrame, 
                                   required_cols: List[str],
                                   optional_cols: Optional[List[str]] = None) -> Tuple[bool, Dict[str, pd.Series]]:
        """
        验证并提取所需列
        
        Args:
            df: DataFrame
            required_cols: 必需列列表
            optional_cols: 可选列列表
            
        Returns:
            (是否成功, 列数据字典)
        """
        try:
            # 检查必需列
            missing_required = [col for col in required_cols if col not in df.columns]
            if missing_required:
                self.logger.error(f"缺少必需列: {missing_required}")
                return False, {}
            
            # 提取列数据
            columns_data = {}
            
            # 提取必需列
            for col in required_cols:
                columns_data[col] = df[col]
            
            # 提取可选列
            if optional_cols:
                for col in optional_cols:
                    if col in df.columns:
                        columns_data[col] = df[col]
                    else:
                        columns_data[col] = pd.Series([None] * len(df), index=df.index)
            
            return True, columns_data
            
        except Exception as e:
            self.logger.error(f"列提取异常: {e}")
            return False, {}
