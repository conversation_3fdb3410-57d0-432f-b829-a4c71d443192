#!/usr/bin/env python3
"""
数据规范化示例脚本
展示如何在AIOps系统中正确处理和规范化各种数据单位

作者: AI Assistant
日期: 2025-07-24
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
from .unit_normalizer import UnitNormalizer


def normalize_apm_metrics(apm_data: pd.DataFrame) -> pd.DataFrame:
    """
    规范化APM指标数据
    
    Args:
        apm_data: 原始APM数据
        
    Returns:
        规范化后的APM数据
    """
    normalized_data = apm_data.copy()
    
    # 规范化时延相关字段 - 确保使用微秒
    time_fields = ['rrt', 'rrt_max']
    for field in time_fields:
        if field in normalized_data.columns:
            # 检查数据范围来判断原始单位
            values = normalized_data[field].dropna()
            if len(values) > 0:
                avg_value = values.mean()
                if avg_value < 100000:  # 如果平均值小于100ms，可能是毫秒
                    print(f"⚠️ {field} 数据可能是毫秒单位，转换为微秒")
                    normalized_data[field] = UnitNormalizer.normalize_duration_to_microseconds(
                        normalized_data[field], 'ms'
                    )
                else:
                    print(f"✅ {field} 数据已经是微秒单位")
    
    # 规范化比例字段 - 确保使用百分比
    ratio_fields = ['error_ratio', 'client_error_ratio', 'server_error_ratio', 'timeout_ratio']
    for field in ratio_fields:
        if field in normalized_data.columns:
            values = normalized_data[field].dropna()
            if len(values) > 0:
                max_value = values.max()
                if max_value <= 1.0:  # 如果最大值<=1，是小数形式
                    print(f"⚠️ {field} 数据是小数形式，转换为百分比")
                    normalized_data[field] = normalized_data[field] * 100
                else:
                    print(f"✅ {field} 数据已经是百分比形式")
    
    return normalized_data


def normalize_trace_data(trace_data: pd.DataFrame) -> pd.DataFrame:
    """
    规范化Trace数据
    
    Args:
        trace_data: 原始Trace数据
        
    Returns:
        规范化后的Trace数据
    """
    normalized_data = trace_data.copy()
    
    # 规范化duration字段 - 确保使用微秒
    if 'duration' in normalized_data.columns:
        values = normalized_data['duration'].dropna()
        if len(values) > 0:
            avg_value = values.mean()
            if avg_value < 100000:  # 如果平均值小于100ms，可能是毫秒
                print("⚠️ duration 数据可能是毫秒单位，转换为微秒")
                normalized_data['duration'] = UnitNormalizer.normalize_duration_to_microseconds(
                    normalized_data['duration'], 'ms'
                )
            else:
                print("✅ duration 数据已经是微秒单位")
    
    # 添加规范化的显示字段
    if 'duration' in normalized_data.columns:
        normalized_data['duration_display'] = normalized_data['duration'].apply(
            lambda x: UnitNormalizer.format_value_with_unit(x, 'duration') if pd.notna(x) else 'N/A'
        )
    
    return normalized_data


def normalize_infrastructure_metrics(infra_data: pd.DataFrame) -> pd.DataFrame:
    """
    规范化基础设施指标数据
    
    Args:
        infra_data: 原始基础设施数据
        
    Returns:
        规范化后的基础设施数据
    """
    normalized_data = infra_data.copy()
    
    # 规范化CPU使用率 - 确保使用百分比
    cpu_fields = [col for col in normalized_data.columns if 'cpu_usage' in col]
    for field in cpu_fields:
        values = normalized_data[field].dropna()
        if len(values) > 0:
            max_value = values.max()
            if max_value <= 1.0:  # 如果最大值<=1，是小数形式
                print(f"⚠️ {field} 数据是小数形式，转换为百分比")
                normalized_data[field] = normalized_data[field] * 100
            else:
                print(f"✅ {field} 数据已经是百分比形式")
    
    # 规范化内存字段 - 确保使用字节
    memory_fields = [col for col in normalized_data.columns if 'memory' in col and 'bytes' in col]
    for field in memory_fields:
        values = normalized_data[field].dropna()
        if len(values) > 0:
            avg_value = values.mean()
            if avg_value < 1024*1024:  # 如果平均值小于1MB，可能是KB
                print(f"⚠️ {field} 数据可能是KB单位，转换为字节")
                normalized_data[field] = UnitNormalizer.normalize_bytes(
                    normalized_data[field], 'KB'
                )
            else:
                print(f"✅ {field} 数据已经是字节单位")
    
    return normalized_data


def validate_data_consistency(data: pd.DataFrame, data_type: str) -> Dict[str, Any]:
    """
    验证数据一致性
    
    Args:
        data: 数据DataFrame
        data_type: 数据类型 ('apm', 'trace', 'infra')
        
    Returns:
        验证结果
    """
    validation_results = {
        "data_type": data_type,
        "total_records": len(data),
        "issues": [],
        "summary": {}
    }
    
    # 检查时延字段的合理性
    time_fields = ['rrt', 'rrt_max', 'duration']
    for field in time_fields:
        if field in data.columns:
            values = data[field].dropna()
            if len(values) > 0:
                min_val, max_val, mean_val = values.min(), values.max(), values.mean()
                
                # 检查是否有负值
                if min_val < 0:
                    validation_results["issues"].append(f"{field} 包含负值: {min_val}")
                
                # 检查是否有异常大的值 (>60秒)
                if max_val > 60_000_000:  # 60秒 = 60,000,000微秒
                    validation_results["issues"].append(f"{field} 包含异常大的值: {max_val}μs")
                
                # 检查平均值是否合理
                if mean_val > 10_000_000:  # 10秒
                    validation_results["issues"].append(f"{field} 平均值过大: {mean_val}μs")
                
                validation_results["summary"][field] = {
                    "min": min_val,
                    "max": max_val,
                    "mean": mean_val,
                    "unit": "μs"
                }
    
    # 检查比例字段的合理性
    ratio_fields = [col for col in data.columns if 'ratio' in col or 'usage' in col]
    for field in ratio_fields:
        if field in data.columns:
            values = data[field].dropna()
            if len(values) > 0:
                min_val, max_val = values.min(), values.max()
                
                # 检查比例是否在合理范围内
                if min_val < 0:
                    validation_results["issues"].append(f"{field} 包含负值: {min_val}")
                
                if max_val > 100:
                    validation_results["issues"].append(f"{field} 超过100%: {max_val}")
                
                validation_results["summary"][field] = {
                    "min": min_val,
                    "max": max_val,
                    "unit": "%"
                }
    
    return validation_results


def demonstrate_normalization():
    """演示数据规范化过程"""
    print("🔧 AIOps数据规范化演示")
    print("="*50)
    
    # 模拟APM数据 (混合单位)
    print("\n📊 APM数据规范化演示:")
    apm_sample = pd.DataFrame({
        'time': pd.date_range('2025-01-01', periods=5, freq='1min'),
        'object_id': ['service-a'] * 5,
        'rrt': [150, 200, 300, 180, 250],  # 毫秒
        'rrt_max': [500, 800, 1200, 600, 900],  # 毫秒
        'error_ratio': [0.01, 0.02, 0.05, 0.01, 0.03],  # 小数形式
        'request': [1000, 1200, 800, 1100, 950]
    })
    
    print("原始数据:")
    print(apm_sample.head(2))
    
    normalized_apm = normalize_apm_metrics(apm_sample)
    print("\n规范化后数据:")
    print(normalized_apm.head(2))
    
    # 验证数据一致性
    print("\n🔍 数据一致性验证:")
    validation = validate_data_consistency(normalized_apm, 'apm')
    print(f"总记录数: {validation['total_records']}")
    print(f"发现问题: {len(validation['issues'])}")
    for issue in validation['issues']:
        print(f"  - {issue}")
    
    # 显示格式化结果
    print("\n📋 格式化显示示例:")
    for field in ['rrt', 'error_ratio']:
        if field in normalized_apm.columns:
            sample_value = normalized_apm[field].iloc[0]
            formatted = UnitNormalizer.format_value_with_unit(sample_value, field)
            print(f"  {field}: {formatted}")


if __name__ == "__main__":
    demonstrate_normalization()
