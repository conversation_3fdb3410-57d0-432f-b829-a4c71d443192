#!/usr/bin/env python3
"""
时间过滤功能测试脚本
验证各个Agent的时间过滤功能是否正常工作

作者: AI Assistant
日期: 2025-07-24
"""

import asyncio
import pandas as pd
import logging
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from aiops_diagnosis.autogen_agents.simple_metric_agent import SimpleMetricAgent
from aiops_diagnosis.autogen_agents.data_loader_agent import DataLoaderAgent
from aiops_diagnosis.message_types import AnalysisRequest, TimeRange

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TimeFilteringTester:
    """时间过滤功能测试器"""
    
    def __init__(self):
        self.test_results = []
        
    async def test_simple_metric_agent_time_filtering(self):
        """测试SimpleMetricAgent的时间过滤功能"""
        logger.info("🧪 测试SimpleMetricAgent时间过滤功能...")
        
        try:
            # 创建SimpleMetricAgent实例
            agent = SimpleMetricAgent("test_metric_agent")
            
            # 设置测试时间范围 - 使用single_test.json中的时间
            start_time = "2025-06-07T09:00:00Z"
            end_time = "2025-06-07T09:05:00Z"
            
            # 创建测试请求
            request = AnalysisRequest(
                case_uuid="test_time_filtering",
                start_time=start_time,
                end_time=end_time,
                components=["frontend", "cartservice"]
            )
            
            # 测试数据加载和时间过滤
            time_range = TimeRange(start=start_time, end=end_time)
            
            # 测试APM数据时间过滤
            apm_data = await agent._load_apm_data_direct(time_range)
            apm_result = self._validate_time_filtering(apm_data, start_time, end_time, "APM")
            
            # 测试Infra数据时间过滤
            infra_data = await agent._load_infra_data_direct(time_range)
            infra_result = self._validate_time_filtering(infra_data, start_time, end_time, "Infra")
            
            # 测试TiDB数据时间过滤
            tidb_data = await agent._load_tidb_data_direct(time_range)
            tidb_result = self._validate_time_filtering(tidb_data, start_time, end_time, "TiDB")
            
            # 测试Other数据时间过滤
            other_data = await agent._load_other_data_direct(time_range)
            other_result = self._validate_time_filtering(other_data, start_time, end_time, "Other")
            
            # 汇总结果
            overall_result = {
                "agent": "SimpleMetricAgent",
                "apm_filtering": apm_result,
                "infra_filtering": infra_result,
                "tidb_filtering": tidb_result,
                "other_filtering": other_result,
                "overall_status": "PASS" if all([
                    apm_result["status"] == "PASS",
                    infra_result["status"] == "PASS", 
                    tidb_result["status"] == "PASS",
                    other_result["status"] == "PASS"
                ]) else "FAIL"
            }
            
            self.test_results.append(overall_result)
            return overall_result
            
        except Exception as e:
            logger.error(f"❌ SimpleMetricAgent时间过滤测试失败: {e}")
            error_result = {
                "agent": "SimpleMetricAgent",
                "error": str(e),
                "overall_status": "ERROR"
            }
            self.test_results.append(error_result)
            return error_result
    
    def _validate_time_filtering(self, data_dict: dict, start_time: str, end_time: str, data_type: str) -> dict:
        """验证时间过滤结果"""
        result = {
            "data_type": data_type,
            "total_objects": len(data_dict),
            "filtered_objects": 0,
            "time_violations": [],
            "status": "PASS"
        }
        
        try:
            start_dt = pd.to_datetime(start_time, utc=True)
            end_dt = pd.to_datetime(end_time, utc=True)
            
            for key, df in data_dict.items():
                if isinstance(df, pd.DataFrame) and not df.empty and 'time' in df.columns:
                    result["filtered_objects"] += 1
                    
                    # 检查时间列
                    time_col = pd.to_datetime(df['time'], utc=True)
                    
                    # 检查是否有超出时间范围的数据
                    before_start = (time_col < start_dt).sum()
                    after_end = (time_col > end_dt).sum()
                    
                    if before_start > 0 or after_end > 0:
                        violation = {
                            "object": key,
                            "before_start": int(before_start),
                            "after_end": int(after_end),
                            "total_records": len(df),
                            "time_range": f"{time_col.min()} ~ {time_col.max()}"
                        }
                        result["time_violations"].append(violation)
                        result["status"] = "FAIL"
            
            # 如果没有时间违规，状态为PASS
            if not result["time_violations"]:
                result["status"] = "PASS"
                
        except Exception as e:
            result["error"] = str(e)
            result["status"] = "ERROR"
            
        return result
    
    async def test_data_loader_agent_time_filtering(self):
        """测试DataLoaderAgent的时间过滤功能"""
        logger.info("🧪 测试DataLoaderAgent时间过滤功能...")
        
        try:
            # 创建DataLoaderAgent实例
            agent = DataLoaderAgent("test_data_loader")
            
            # 设置测试时间范围
            start_time = "2025-06-07T09:00:00Z"
            end_time = "2025-06-07T09:05:00Z"
            
            # 测试指标数据时间过滤
            metrics_data = agent.load_metrics_data(start_time, end_time)
            metrics_result = self._validate_dataframe_time_filtering(
                metrics_data, start_time, end_time, "Metrics", "time"
            )
            
            # 测试调用链数据时间过滤
            traces_data = agent.load_traces_data(start_time, end_time)
            traces_result = self._validate_dataframe_time_filtering(
                traces_data, start_time, end_time, "Traces", "startTimeMillis"
            )
            
            # 测试日志数据时间过滤
            logs_data = agent.load_logs_data(start_time, end_time)
            logs_result = self._validate_dataframe_time_filtering(
                logs_data, start_time, end_time, "Logs", "@timestamp"
            )
            
            # 汇总结果
            overall_result = {
                "agent": "DataLoaderAgent",
                "metrics_filtering": metrics_result,
                "traces_filtering": traces_result,
                "logs_filtering": logs_result,
                "overall_status": "PASS" if all([
                    metrics_result["status"] == "PASS",
                    traces_result["status"] == "PASS",
                    logs_result["status"] == "PASS"
                ]) else "FAIL"
            }
            
            self.test_results.append(overall_result)
            return overall_result
            
        except Exception as e:
            logger.error(f"❌ DataLoaderAgent时间过滤测试失败: {e}")
            error_result = {
                "agent": "DataLoaderAgent", 
                "error": str(e),
                "overall_status": "ERROR"
            }
            self.test_results.append(error_result)
            return error_result
    
    def _validate_dataframe_time_filtering(self, df: pd.DataFrame, start_time: str, end_time: str, 
                                         data_type: str, time_column: str) -> dict:
        """验证DataFrame的时间过滤结果"""
        result = {
            "data_type": data_type,
            "total_records": len(df),
            "time_column": time_column,
            "status": "PASS"
        }
        
        try:
            if df.empty or time_column not in df.columns:
                result["status"] = "SKIP"
                result["reason"] = f"数据为空或缺少{time_column}列"
                return result
            
            start_dt = pd.to_datetime(start_time, utc=True)
            end_dt = pd.to_datetime(end_time, utc=True)
            
            if time_column == "startTimeMillis":
                # 调用链数据使用毫秒时间戳
                start_millis = int(start_dt.timestamp() * 1000)
                end_millis = int(end_dt.timestamp() * 1000)
                
                before_start = (df[time_column] < start_millis).sum()
                after_end = (df[time_column] > end_millis).sum()
                
                result["time_range"] = f"{df[time_column].min()} ~ {df[time_column].max()}"
            else:
                # 其他数据使用datetime
                time_col = pd.to_datetime(df[time_column], utc=True)
                
                before_start = (time_col < start_dt).sum()
                after_end = (time_col > end_dt).sum()
                
                result["time_range"] = f"{time_col.min()} ~ {time_col.max()}"
            
            result["before_start"] = int(before_start)
            result["after_end"] = int(after_end)
            
            if before_start > 0 or after_end > 0:
                result["status"] = "FAIL"
                result["violation_details"] = f"超出范围记录: 早于开始时间{before_start}条, 晚于结束时间{after_end}条"
            
        except Exception as e:
            result["error"] = str(e)
            result["status"] = "ERROR"
            
        return result
    
    def print_test_report(self):
        """打印测试报告"""
        print("\n" + "="*60)
        print("🧪 时间过滤功能测试报告")
        print("="*60)
        
        for result in self.test_results:
            print(f"\n🤖 {result['agent']} 测试结果:")
            print(f"   总体状态: {result['overall_status']}")
            
            if result['overall_status'] == "ERROR":
                print(f"   错误信息: {result.get('error', 'Unknown error')}")
                continue
            
            # 打印详细结果
            for key, value in result.items():
                if key.endswith('_filtering') and isinstance(value, dict):
                    data_type = value['data_type']
                    status = value['status']
                    print(f"   {data_type}数据过滤: {status}")
                    
                    if status == "FAIL":
                        if 'time_violations' in value:
                            print(f"     时间违规对象数: {len(value['time_violations'])}")
                        if 'violation_details' in value:
                            print(f"     违规详情: {value['violation_details']}")
                    elif status == "SKIP":
                        print(f"     跳过原因: {value.get('reason', 'Unknown')}")
        
        # 总结
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['overall_status'] == 'PASS')
        
        print(f"\n📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n" + "="*60)


async def main():
    """主函数"""
    tester = TimeFilteringTester()
    
    # 运行所有测试
    await tester.test_simple_metric_agent_time_filtering()
    await tester.test_data_loader_agent_time_filtering()
    
    # 打印测试报告
    tester.print_test_report()


if __name__ == "__main__":
    asyncio.run(main())
