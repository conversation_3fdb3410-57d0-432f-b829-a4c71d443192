#!/usr/bin/env python3
"""
LLM配置模块
为AIOps故障诊断系统提供大模型支持
"""

import os
import asyncio
from typing import Dict, Any, List, Optional
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import UserMessage, SystemMessage, ModelFamily
import logging

logger = logging.getLogger(__name__)


class LLMConfig:
    """统一的LLM配置管理类 - 根据Agent类型差异化配置"""

    # 🔧 差异化超时配置 - 根据Agent复杂度设置不同超时时间
    REASONING_TIMEOUT = 600.0    # ReasoningAgent: 5分钟（复杂推理分析）
    METRICS_TIMEOUT = 600.0      # MetricsAgent: 2分钟（指标分析）
    TRACE_TIMEOUT = 600.0         # TraceAgent: 1分钟（结构化数据分析）
    LOG_TIMEOUT = 600.0           # LogAgent: 1.5分钟（日志模式识别）

    # 默认配置（向后兼容）
    UNIFIED_TIMEOUT = REASONING_TIMEOUT  # 默认使用最长超时
    UNIFIED_MAX_RETRIES = 3              # 减少重试次数，避免过多调用
    # UNIFIED_MODEL = "deepseek-r1:671b-0528"
    # UNIFIED_BASE_URL = "https://uni-api.cstcloud.cn/v1"
    # UNIFIED_API_KEY = "2b6c2b759437a6152d1433dd0a47ce1629a9c5e7be4d370b4e454c6975b8b286"

    # UNIFIED_MODEL = "deepseek-chat"
    # UNIFIED_BASE_URL = "https://api.deepseek.com"
    # UNIFIED_API_KEY = "***********************************"

    UNIFIED_MODEL = "Pro/deepseek-ai/DeepSeek-V3"
    UNIFIED_BASE_URL = "https://api.siliconflow.cn/v1"
    UNIFIED_API_KEY = "sk-imqurnwfzuwabslhwqagwstcsgqkjjqhdwwqdedkijttxpuy"



    def __init__(self):
        self.default_model = self.UNIFIED_MODEL
        self.base_url = self.UNIFIED_BASE_URL
        self.api_key = self.UNIFIED_API_KEY
        
    def create_client(self, model: str = None) -> OpenAIChatCompletionClient:
        """创建LLM客户端"""
        model = model or self.default_model

        client = OpenAIChatCompletionClient(
            model=model,
            base_url=self.base_url,
            api_key=self.api_key,
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
                "structured_output": False,  # 添加缺失的字段
            },
            timeout=self.UNIFIED_TIMEOUT,
            max_retries=self.UNIFIED_MAX_RETRIES,
            # 🔧 增强稳定性参数，确保输出一致性
            temperature=0.01,  # 极低温度确保输出稳定
            top_p=0.8,        # 降低输出多样性
            max_tokens=4000,  # 限制输出长度
            seed=42,          # 固定种子确保可重复性
        )

        return client


class LLMAnalysisHelper:
    """LLM分析助手"""
    
    def __init__(self):
        self.config = LLMConfig()
        self.client = self.config.create_client()
        
    async def analyze_metrics_with_llm(self, metrics_data: Dict[str, Any], anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM分析指标数据"""
        try:
            # 构建系统提示
            system_prompt = """你是一个专业的AIOps系统指标分析专家。请分析提供的指标数据和异常信息，识别潜在的性能问题和资源瓶颈。

分析要求：
1. 识别关键的性能指标异常
2. 分析可能的根因
3. 评估影响范围
4. 提供简洁的分析结论

请以JSON格式返回分析结果，包含：
- critical_issues: 关键问题列表
- root_cause_candidates: 根因候选列表
- impact_assessment: 影响评估
- confidence_score: 置信度分数(0-1)"""

            # 构建用户消息
            user_content = f"""请分析以下指标异常数据：

异常数量: {len(anomalies)}
异常详情: {str(anomalies)[:1000]}...

请提供专业的分析结论。"""

            messages = [
                SystemMessage(content=system_prompt, source="system"),
                UserMessage(content=user_content, source="user")
            ]
            
            response = await self.client.create(
                messages,
                temperature=0.01,  # 极低温度确保输出稳定
                max_tokens=2000,
                top_p=0.8,
                seed=42  # 固定种子确保可重复性
            )
            
            # 尝试解析JSON响应
            try:
                import json
                analysis_result = json.loads(response.content)
            except json.JSONDecodeError as e:
                # JSON解析失败时，不返回虚假结果
                logger.warning(f"LLM response JSON parsing failed: {e}")
                analysis_result = {
                    "critical_issues": [],
                    "root_cause_candidates": [],
                    "impact_assessment": "LLM response parsing failed",
                    "confidence_score": 0.0,
                    "error": "Failed to parse LLM response as JSON"
                }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"LLM metrics analysis failed: {e}")
            return {
                "critical_issues": [],
                "root_cause_candidates": [],
                "impact_assessment": "LLM analysis unavailable",
                "confidence_score": 0.0
            }
    
    async def analyze_traces_with_llm(self, traces_data: Dict[str, Any], anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM分析调用链数据"""
        try:
            system_prompt = """你是一个专业的分布式系统调用链分析专家。请分析提供的调用链数据和异常信息，识别服务间的依赖问题和性能瓶颈。

分析要求：
1. 识别调用链中的异常模式
2. 分析服务间依赖关系
3. 识别性能瓶颈点
4. 评估错误传播路径

请以JSON格式返回分析结果。"""

            user_content = f"""请分析以下调用链异常数据：

异常数量: {len(anomalies)}
服务依赖: {traces_data.get('service_dependency', {})}
错误传播: {traces_data.get('error_propagation', [])}

请提供专业的分析结论。"""

            messages = [
                SystemMessage(content=system_prompt, source="system"),
                UserMessage(content=user_content, source="user")
            ]
            
            response = await self.client.create(
                messages,
                temperature=0.01,  # 极低温度确保输出稳定
                max_tokens=2000,
                top_p=0.8,
                seed=42  # 固定种子确保可重复性
            )
            
            try:
                import json
                analysis_result = json.loads(response.content)
            except json.JSONDecodeError as e:
                logger.warning(f"LLM trace analysis JSON parsing failed: {e}")
                analysis_result = {
                    "service_issues": [],
                    "dependency_problems": [],
                    "performance_bottlenecks": [],
                    "confidence_score": 0.0,
                    "error": "Failed to parse LLM trace analysis response"
                }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"LLM trace analysis failed: {e}")
            return {
                "service_issues": [],
                "dependency_problems": [],
                "performance_bottlenecks": [],
                "confidence_score": 0.0
            }
    
    async def analyze_logs_with_llm(self, logs_data: Dict[str, Any], error_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM分析日志数据"""
        try:
            system_prompt = """你是一个专业的系统日志分析专家。请分析提供的错误日志和模式，识别系统故障的根本原因。

分析要求：
1. 识别关键错误模式
2. 分析错误的时间序列特征
3. 识别可能的根因
4. 评估错误的严重程度

请以JSON格式返回分析结果。"""

            # 提取关键错误信息
            error_messages = [log.get('message', '')[:100] for log in error_logs[:5]]
            error_patterns = logs_data.get('error_patterns', [])
            
            user_content = f"""请分析以下日志错误数据：

错误日志数量: {len(error_logs)}
关键错误消息: {error_messages}
错误模式: {error_patterns}
组件状态: {logs_data.get('component_status', {})}

请提供专业的分析结论。"""

            messages = [
                SystemMessage(content=system_prompt, source="system"),
                UserMessage(content=user_content, source="user")
            ]
            
            response = await self.client.create(
                messages,
                temperature=0.1,  # 确保输出稳定
                max_tokens=2000,
                top_p=0.9
            )
            
            try:
                import json
                analysis_result = json.loads(response.content)
            except:
                analysis_result = {
                    "error_analysis": ["LLM log analysis completed"],
                    "root_cause_indicators": [],
                    "severity_assessment": "medium",
                    "confidence_score": 0.7
                }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"LLM log analysis failed: {e}")
            return {
                "error_analysis": [],
                "root_cause_indicators": [],
                "severity_assessment": "unknown",
                "confidence_score": 0.0
            }
    
    async def perform_root_cause_reasoning(self, 
                                         metrics_analysis: Dict[str, Any],
                                         traces_analysis: Dict[str, Any], 
                                         logs_analysis: Dict[str, Any],
                                         evidence: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM进行根因推理"""
        try:
            system_prompt = """你是一个专业的AIOps故障诊断专家。请基于多维度的分析结果，进行综合推理，识别故障的根本原因。

你需要分析：
1. 指标分析结果（性能和资源异常）
2. 调用链分析结果（服务依赖和调用异常）
3. 日志分析结果（错误模式和事件）
4. 综合证据（时间序列和关联性）

请提供：
1. 最可能的根因组件
2. 故障类型分类
3. 推理过程说明
4. 置信度评估

输出格式要求：
- component: 根因组件名称（必须是有效的微服务名称）
- failure_type: 故障类型
- reasoning_trace: 推理步骤列表
- confidence: 置信度(0-1)
- supporting_evidence: 支持证据"""

            # 构建综合分析内容
            user_content = f"""请基于以下多维度分析结果进行根因推理：

## 指标分析结果
{str(metrics_analysis)[:500]}

## 调用链分析结果  
{str(traces_analysis)[:500]}

## 日志分析结果
{str(logs_analysis)[:500]}

## 综合证据
证据数量: {len(evidence)}
涉及组件: {list(set([e.get('component', 'unknown') for e in evidence]))}

请进行专业的根因分析和推理。"""

            messages = [
                SystemMessage(content=system_prompt, source="system"),
                UserMessage(content=user_content, source="user")
            ]
            
            response = await self.client.create(
                messages,
                temperature=0.1,  # 确保输出稳定
                max_tokens=3000,  # 根因推理需要更多token
                top_p=0.9
            )
            
            try:
                import json
                reasoning_result = json.loads(response.content)
                
                # 确保返回结果包含必需字段
                if "component" not in reasoning_result:
                    reasoning_result["component"] = "frontend"
                if "failure_type" not in reasoning_result:
                    reasoning_result["failure_type"] = "service_malfunction"
                if "confidence" not in reasoning_result:
                    reasoning_result["confidence"] = 0.7
                    
            except:
                # 解析失败时的fallback
                reasoning_result = {
                    "component": "frontend",
                    "failure_type": "service_malfunction", 
                    "reasoning_trace": ["LLM analysis completed", "Multiple factors considered"],
                    "confidence": 0.7,
                    "supporting_evidence": ["Multi-dimensional analysis performed"]
                }
            
            return reasoning_result
            
        except Exception as e:
            logger.error(f"LLM root cause reasoning failed: {e}")
            return {
                "component": "frontend",
                "failure_type": "unknown_failure",
                "reasoning_trace": ["LLM reasoning unavailable"],
                "confidence": 0.5,
                "supporting_evidence": []
            }


# 全局LLM助手实例
llm_helper = LLMAnalysisHelper()


def get_model_client(model: str = None) -> OpenAIChatCompletionClient:
    """获取LLM模型客户端 - 使用统一配置"""
    config = LLMConfig()
    return config.create_client(model)


def get_unified_llm_config() -> Dict[str, Any]:
    """获取统一的LLM配置参数"""
    return {
        'timeout_seconds': LLMConfig.UNIFIED_TIMEOUT,
        'max_retries': LLMConfig.UNIFIED_MAX_RETRIES,
        'retry_delay': 1.0,
        'model': LLMConfig.UNIFIED_MODEL,
        'base_url': LLMConfig.UNIFIED_BASE_URL
    }


def get_llm_config(agent_type: str = "default") -> Dict[str, Any]:
    """获取LLM配置参数 - 根据Agent类型提供差异化配置"""

    # 🔧 根据Agent类型选择合适的超时时间
    timeout_mapping = {
        "trace": LLMConfig.TRACE_TIMEOUT,      # TraceAgent: 60秒
        "metrics": LLMConfig.METRICS_TIMEOUT,  # MetricsAgent: 120秒
        "log": LLMConfig.LOG_TIMEOUT,          # LogAgent: 90秒
        "reasoning": LLMConfig.REASONING_TIMEOUT, # ReasoningAgent: 300秒
        "default": LLMConfig.UNIFIED_TIMEOUT   # 默认: 300秒（向后兼容）
    }

    selected_timeout = timeout_mapping.get(agent_type, LLMConfig.UNIFIED_TIMEOUT)

    return {
        'model': LLMConfig.UNIFIED_MODEL,
        'base_url': LLMConfig.UNIFIED_BASE_URL,
        'api_key': LLMConfig.UNIFIED_API_KEY,
        'timeout': selected_timeout,  # 🔧 使用差异化超时时间
        'max_retries': LLMConfig.UNIFIED_MAX_RETRIES,
        'model_info': {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "structured_output": False,
        }
    }
