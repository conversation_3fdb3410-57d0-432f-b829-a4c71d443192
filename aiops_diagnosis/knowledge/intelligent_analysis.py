#!/usr/bin/env python3
"""
智能分析模块
提供AIOps系统的智能分析能力，包括证据分析和故障传播分析
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class EvidenceType(Enum):
    """证据类型"""
    METRIC_ANOMALY = "metric_anomaly"
    LOG_ERROR = "log_error"
    TRACE_LATENCY = "trace_latency"
    RESOURCE_EXHAUSTION = "resource_exhaustion"
    DEPENDENCY_FAILURE = "dependency_failure"


class ConfidenceLevel(Enum):
    """置信度级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class AnalysisEvidence:
    """分析证据"""
    evidence_type: EvidenceType
    component: str
    description: str
    confidence: float
    timestamp: str
    data_source: str
    severity: str = "medium"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class FailurePropagation:
    """故障传播路径"""
    source_component: str
    target_component: str
    propagation_type: str
    confidence: float
    evidence: List[AnalysisEvidence]
    impact_level: str = "medium"
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = []


class IntelligentAnalyzer:
    """智能分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.IntelligentAnalyzer")
        
    def analyze_evidence_correlation(self, evidence_list: List[AnalysisEvidence]) -> Dict[str, Any]:
        """分析证据相关性"""
        try:
            if not evidence_list:
                return {
                    "correlation_score": 0.0,
                    "primary_components": [],
                    "evidence_clusters": [],
                    "confidence": 0.0
                }
            
            # 按组件分组证据
            component_evidence = {}
            for evidence in evidence_list:
                if evidence.component not in component_evidence:
                    component_evidence[evidence.component] = []
                component_evidence[evidence.component].append(evidence)
            
            # 计算组件权重
            component_scores = {}
            for component, evidences in component_evidence.items():
                score = sum(e.confidence for e in evidences) / len(evidences)
                component_scores[component] = score
            
            # 排序获取主要组件
            primary_components = sorted(component_scores.items(), 
                                     key=lambda x: x[1], reverse=True)[:3]
            
            # 计算整体相关性分数
            correlation_score = sum(score for _, score in primary_components) / len(primary_components) if primary_components else 0.0
            
            return {
                "correlation_score": correlation_score,
                "primary_components": [comp for comp, _ in primary_components],
                "evidence_clusters": self._cluster_evidence_by_type(evidence_list),
                "confidence": min(correlation_score, 1.0)
            }
            
        except Exception as e:
            self.logger.error(f"证据相关性分析失败: {e}")
            return {
                "correlation_score": 0.0,
                "primary_components": [],
                "evidence_clusters": [],
                "confidence": 0.0
            }
    
    def _cluster_evidence_by_type(self, evidence_list: List[AnalysisEvidence]) -> List[Dict[str, Any]]:
        """按类型聚类证据"""
        clusters = {}
        
        for evidence in evidence_list:
            evidence_type = evidence.evidence_type.value
            if evidence_type not in clusters:
                clusters[evidence_type] = {
                    "type": evidence_type,
                    "count": 0,
                    "components": set(),
                    "avg_confidence": 0.0,
                    "evidences": []
                }
            
            cluster = clusters[evidence_type]
            cluster["count"] += 1
            cluster["components"].add(evidence.component)
            cluster["evidences"].append(evidence)
        
        # 计算平均置信度
        for cluster in clusters.values():
            if cluster["evidences"]:
                cluster["avg_confidence"] = sum(e.confidence for e in cluster["evidences"]) / len(cluster["evidences"])
            cluster["components"] = list(cluster["components"])
            # 移除evidences以减少返回数据量
            del cluster["evidences"]
        
        return list(clusters.values())
    
    def analyze_failure_propagation(self, evidence_list: List[AnalysisEvidence], 
                                  architecture_info: Dict[str, Any] = None) -> List[FailurePropagation]:
        """分析故障传播路径"""
        try:
            propagations = []
            
            if not evidence_list:
                return propagations
            
            # 按时间排序证据
            sorted_evidence = sorted(evidence_list, 
                                   key=lambda x: x.timestamp if x.timestamp else "")
            
            # 分析时间相关的故障传播
            for i in range(len(sorted_evidence) - 1):
                current = sorted_evidence[i]
                next_evidence = sorted_evidence[i + 1]
                
                if current.component != next_evidence.component:
                    # 检查是否可能存在传播关系
                    propagation_confidence = self._calculate_propagation_confidence(
                        current, next_evidence, architecture_info
                    )
                    
                    if propagation_confidence > 0.3:  # 阈值过滤
                        propagation = FailurePropagation(
                            source_component=current.component,
                            target_component=next_evidence.component,
                            propagation_type="temporal_correlation",
                            confidence=propagation_confidence,
                            evidence=[current, next_evidence],
                            impact_level=self._determine_impact_level(propagation_confidence)
                        )
                        propagations.append(propagation)
            
            return propagations
            
        except Exception as e:
            self.logger.error(f"故障传播分析失败: {e}")
            return []
    
    def _calculate_propagation_confidence(self, source_evidence: AnalysisEvidence, 
                                        target_evidence: AnalysisEvidence,
                                        architecture_info: Dict[str, Any] = None) -> float:
        """计算传播置信度"""
        base_confidence = (source_evidence.confidence + target_evidence.confidence) / 2
        
        # 如果有架构信息，检查依赖关系
        if architecture_info and "dependencies" in architecture_info:
            dependencies = architecture_info["dependencies"]
            if (source_evidence.component in dependencies and 
                target_evidence.component in dependencies.get(source_evidence.component, [])):
                base_confidence *= 1.5  # 提升有依赖关系的置信度
        
        # 根据证据类型调整
        if (source_evidence.evidence_type == EvidenceType.DEPENDENCY_FAILURE and
            target_evidence.evidence_type == EvidenceType.METRIC_ANOMALY):
            base_confidence *= 1.2
        
        return min(base_confidence, 1.0)
    
    def _determine_impact_level(self, confidence: float) -> str:
        """确定影响级别"""
        if confidence >= 0.8:
            return "high"
        elif confidence >= 0.6:
            return "medium"
        else:
            return "low"
    
    def generate_analysis_summary(self, evidence_list: List[AnalysisEvidence],
                                propagations: List[FailurePropagation]) -> Dict[str, Any]:
        """生成分析摘要"""
        try:
            # 统计证据
            evidence_stats = {
                "total_evidence": len(evidence_list),
                "evidence_by_type": {},
                "evidence_by_component": {},
                "avg_confidence": 0.0
            }
            
            if evidence_list:
                # 按类型统计
                for evidence in evidence_list:
                    evidence_type = evidence.evidence_type.value
                    evidence_stats["evidence_by_type"][evidence_type] = \
                        evidence_stats["evidence_by_type"].get(evidence_type, 0) + 1
                    
                    # 按组件统计
                    component = evidence.component
                    evidence_stats["evidence_by_component"][component] = \
                        evidence_stats["evidence_by_component"].get(component, 0) + 1
                
                # 平均置信度
                evidence_stats["avg_confidence"] = sum(e.confidence for e in evidence_list) / len(evidence_list)
            
            # 传播统计
            propagation_stats = {
                "total_propagations": len(propagations),
                "high_confidence_propagations": len([p for p in propagations if p.confidence >= 0.7]),
                "affected_components": len(set(p.target_component for p in propagations))
            }
            
            # 生成建议
            recommendations = self._generate_recommendations(evidence_list, propagations)
            
            return {
                "evidence_stats": evidence_stats,
                "propagation_stats": propagation_stats,
                "recommendations": recommendations,
                "analysis_timestamp": self._get_current_timestamp(),
                "overall_confidence": evidence_stats["avg_confidence"]
            }
            
        except Exception as e:
            self.logger.error(f"生成分析摘要失败: {e}")
            return {
                "evidence_stats": {"total_evidence": 0},
                "propagation_stats": {"total_propagations": 0},
                "recommendations": [],
                "analysis_timestamp": self._get_current_timestamp(),
                "overall_confidence": 0.0
            }
    
    def _generate_recommendations(self, evidence_list: List[AnalysisEvidence],
                                propagations: List[FailurePropagation]) -> List[str]:
        """生成分析建议"""
        recommendations = []
        
        if not evidence_list:
            recommendations.append("需要收集更多证据进行分析")
            return recommendations
        
        # 基于证据类型的建议
        evidence_types = set(e.evidence_type for e in evidence_list)
        
        if EvidenceType.RESOURCE_EXHAUSTION in evidence_types:
            recommendations.append("检查资源使用情况，考虑扩容或优化")
        
        if EvidenceType.DEPENDENCY_FAILURE in evidence_types:
            recommendations.append("检查服务依赖关系和网络连接")
        
        if EvidenceType.LOG_ERROR in evidence_types:
            recommendations.append("分析错误日志，定位具体错误原因")
        
        # 基于传播的建议
        if propagations:
            high_confidence_propagations = [p for p in propagations if p.confidence >= 0.7]
            if high_confidence_propagations:
                recommendations.append("重点关注故障传播路径，优先修复源头组件")
        
        return recommendations
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


# 便利函数
def create_evidence(evidence_type: str, component: str, description: str, 
                   confidence: float, timestamp: str = None, 
                   data_source: str = "unknown") -> AnalysisEvidence:
    """创建分析证据"""
    if timestamp is None:
        from datetime import datetime
        timestamp = datetime.now().isoformat()
    
    return AnalysisEvidence(
        evidence_type=EvidenceType(evidence_type),
        component=component,
        description=description,
        confidence=confidence,
        timestamp=timestamp,
        data_source=data_source
    )


def analyze_system_health(evidence_list: List[AnalysisEvidence]) -> Dict[str, Any]:
    """分析系统健康状况"""
    analyzer = IntelligentAnalyzer()
    
    # 证据相关性分析
    correlation_result = analyzer.analyze_evidence_correlation(evidence_list)
    
    # 故障传播分析
    propagations = analyzer.analyze_failure_propagation(evidence_list)
    
    # 生成摘要
    summary = analyzer.generate_analysis_summary(evidence_list, propagations)
    
    return {
        "correlation_analysis": correlation_result,
        "propagation_analysis": propagations,
        "summary": summary
    }
