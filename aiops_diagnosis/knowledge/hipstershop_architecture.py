#!/usr/bin/env python3
"""
HipsterShop微服务架构知识库
基于数据信息说明文档构建的完整服务架构图谱
"""

from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class ComponentType(Enum):
    """组件类型枚举"""
    SERVICE = "service"
    POD = "pod"
    NODE = "node"
    DATABASE = "database"
    CACHE = "cache"


@dataclass
class ServiceComponent:
    """服务组件定义"""
    name: str
    component_type: ComponentType
    dependencies: List[str]  # 依赖的服务列表
    pods: List[str]  # 关联的Pod列表
    description: str
    criticality: int  # 关键程度 1-5，5最关键


class HipsterShopArchitecture:
    """HipsterShop架构知识库"""
    
    def __init__(self):
        self._initialize_architecture()
    
    def _initialize_architecture(self):
        """初始化架构信息"""
        
        # 核心微服务定义（每个服务3个Pod）
        self.services = {
            'frontend': ServiceComponent(
                name='frontend',
                component_type=ComponentType.SERVICE,
                dependencies=['productcatalogservice', 'recommendationservice', 'adservice', 
                             'currencyservice', 'shippingservice', 'cartservice', 'checkoutservice'],
                pods=['frontend-0', 'frontend-1', 'frontend-2'],
                description='用户入口，负责路由到所有下游服务',
                criticality=5
            ),
            'checkoutservice': ServiceComponent(
                name='checkoutservice',
                component_type=ComponentType.SERVICE,
                dependencies=['paymentservice', 'shippingservice', 'emailservice', 
                             'cartservice', 'currencyservice'],
                pods=['checkoutservice-0', 'checkoutservice-1', 'checkoutservice-2'],
                description='下单编排服务，协调支付、发货、通知',
                criticality=5
            ),
            'paymentservice': ServiceComponent(
                name='paymentservice',
                component_type=ComponentType.SERVICE,
                dependencies=[],
                pods=['paymentservice-0', 'paymentservice-1', 'paymentservice-2'],
                description='支付处理服务',
                criticality=4
            ),
            'cartservice': ServiceComponent(
                name='cartservice',
                component_type=ComponentType.SERVICE,
                dependencies=['redis'],
                pods=['cartservice-0', 'cartservice-1', 'cartservice-2'],
                description='购物车服务，使用Redis缓存',
                criticality=4
            ),
            'productcatalogservice': ServiceComponent(
                name='productcatalogservice',
                component_type=ComponentType.SERVICE,
                dependencies=['tidb'],
                pods=['productcatalogservice-0', 'productcatalogservice-1', 'productcatalogservice-2'],
                description='商品目录服务，从TiDB读取商品信息',
                criticality=4
            ),
            'recommendationservice': ServiceComponent(
                name='recommendationservice',
                component_type=ComponentType.SERVICE,
                dependencies=['productcatalogservice'],
                pods=['recommendationservice-0', 'recommendationservice-1', 'recommendationservice-2'],
                description='推荐服务，依赖商品目录',
                criticality=3
            ),
            'adservice': ServiceComponent(
                name='adservice',
                component_type=ComponentType.SERVICE,
                dependencies=['tidb'],
                pods=['adservice-0', 'adservice-1', 'adservice-2'],
                description='广告服务，从TiDB读取广告内容',
                criticality=2
            ),
            'currencyservice': ServiceComponent(
                name='currencyservice',
                component_type=ComponentType.SERVICE,
                dependencies=[],
                pods=['currencyservice-0', 'currencyservice-1', 'currencyservice-2'],
                description='汇率服务，提供货币转换',
                criticality=3
            ),
            'shippingservice': ServiceComponent(
                name='shippingservice',
                component_type=ComponentType.SERVICE,
                dependencies=[],
                pods=['shippingservice-0', 'shippingservice-1', 'shippingservice-2'],
                description='运费计算服务',
                criticality=3
            ),
            'emailservice': ServiceComponent(
                name='emailservice',
                component_type=ComponentType.SERVICE,
                dependencies=[],
                pods=['emailservice-0', 'emailservice-1', 'emailservice-2'],
                description='邮件通知服务',
                criticality=2
            )
        }
        
        # 基础设施组件
        self.infrastructure = {
            'redis': ServiceComponent(
                name='redis',
                component_type=ComponentType.CACHE,
                dependencies=[],
                pods=['redis-cart-0'],  # Redis通常单实例
                description='Redis缓存，支持购物车服务',
                criticality=4
            ),
            'tidb': ServiceComponent(
                name='tidb',
                component_type=ComponentType.DATABASE,
                dependencies=[],
                pods=['tidb-tidb-0'],
                description='TiDB数据库主节点',
                criticality=5
            ),
            'tikv': ServiceComponent(
                name='tikv',
                component_type=ComponentType.DATABASE,
                dependencies=[],
                pods=['tidb-tikv-0'],
                description='TiKV存储节点',
                criticality=5
            ),
            'pd': ServiceComponent(
                name='pd',
                component_type=ComponentType.DATABASE,
                dependencies=[],
                pods=['tidb-pd-0'],
                description='PD调度节点',
                criticality=5
            )
        }
        
        # 节点信息（8台虚拟机）
        self.nodes = [
            'aiops-k8s-01', 'aiops-k8s-02', 'aiops-k8s-03', 'aiops-k8s-04',
            'aiops-k8s-05', 'aiops-k8s-06', 'aiops-k8s-07', 'aiops-k8s-08'
        ]
        
        # 构建完整组件映射
        self.all_components = {**self.services, **self.infrastructure}
        
        # 构建Pod到服务的映射
        self.pod_to_service = {}
        for service_name, service in self.all_components.items():
            for pod in service.pods:
                self.pod_to_service[pod] = service_name
        
        # 构建调用链路图
        self._build_call_graph()
    
    def _build_call_graph(self):
        """构建服务调用关系图"""
        self.call_graph = {}
        self.reverse_call_graph = {}  # 反向调用图，用于影响分析
        
        for service_name, service in self.all_components.items():
            self.call_graph[service_name] = service.dependencies
            
            # 构建反向调用图
            for dep in service.dependencies:
                if dep not in self.reverse_call_graph:
                    self.reverse_call_graph[dep] = []
                self.reverse_call_graph[dep].append(service_name)
    
    def get_all_valid_components(self) -> Set[str]:
        """获取所有有效组件名称（服务+Pod+节点）"""
        components = set()
        
        # 添加服务名称
        components.update(self.all_components.keys())
        
        # 添加Pod名称
        for service in self.all_components.values():
            components.update(service.pods)
        
        # 添加节点名称
        components.update(self.nodes)
        
        return components
    
    def get_service_by_pod(self, pod_name: str) -> Optional[str]:
        """根据Pod名称获取服务名称"""
        return self.pod_to_service.get(pod_name)
    
    def get_pods_by_service(self, service_name: str) -> List[str]:
        """根据服务名称获取Pod列表"""
        if service_name in self.all_components:
            return self.all_components[service_name].pods
        return []
    
    def get_dependencies(self, component: str) -> List[str]:
        """获取组件的依赖关系"""
        if component in self.all_components:
            return self.all_components[component].dependencies
        
        # 如果是Pod，返回对应服务的依赖
        service = self.get_service_by_pod(component)
        if service:
            return self.all_components[service].dependencies
        
        return []
    
    def get_dependents(self, component: str) -> List[str]:
        """获取依赖该组件的服务列表"""
        return self.reverse_call_graph.get(component, [])
    
    def get_call_path(self, from_service: str, to_service: str) -> List[str]:
        """获取服务间的调用路径"""
        if from_service not in self.all_components:
            return []
        
        # 简单的BFS路径查找
        queue = [(from_service, [from_service])]
        visited = set()
        
        while queue:
            current, path = queue.pop(0)
            
            if current == to_service:
                return path
            
            if current in visited:
                continue
            visited.add(current)
            
            for dep in self.get_dependencies(current):
                if dep not in visited:
                    queue.append((dep, path + [dep]))
        
        return []
    
    def get_impact_scope(self, failed_component: str) -> Dict[str, List[str]]:
        """获取组件故障的影响范围"""
        impact = {
            'direct_dependents': [],
            'indirect_dependents': [],
            'affected_pods': []
        }
        
        # 直接依赖者
        direct_deps = self.get_dependents(failed_component)
        impact['direct_dependents'] = direct_deps
        
        # 间接依赖者（递归查找）
        indirect = set()
        queue = direct_deps.copy()
        visited = set(direct_deps)
        
        while queue:
            current = queue.pop(0)
            for dependent in self.get_dependents(current):
                if dependent not in visited:
                    visited.add(dependent)
                    queue.append(dependent)
                    indirect.add(dependent)
        
        impact['indirect_dependents'] = list(indirect)
        
        # 受影响的Pod
        all_affected = direct_deps + list(indirect)
        for service in all_affected:
            impact['affected_pods'].extend(self.get_pods_by_service(service))
        
        return impact
    
    def get_component_criticality(self, component: str) -> int:
        """获取组件关键程度"""
        if component in self.all_components:
            return self.all_components[component].criticality
        
        # 如果是Pod，返回对应服务的关键程度
        service = self.get_service_by_pod(component)
        if service and service in self.all_components:
            return self.all_components[service].criticality
        
        return 1  # 默认最低关键程度
    
    def is_valid_component(self, component: str) -> bool:
        """验证组件名称是否有效"""
        return component in self.get_all_valid_components()
    
    def get_component_type(self, component: str) -> Optional[ComponentType]:
        """获取组件类型"""
        if component in self.all_components:
            return self.all_components[component].component_type
        
        # 检查是否是Pod
        if component in self.pod_to_service:
            return ComponentType.POD
        
        # 检查是否是节点
        if component in self.nodes:
            return ComponentType.NODE
        
        return None
