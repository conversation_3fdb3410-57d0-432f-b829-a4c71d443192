#!/usr/bin/env python3
"""
全局组件管理器 - 单例模式实现组件信息共享
避免多个Agent重复扫描数据目录，提供统一的组件验证服务
"""

import logging
import threading
import time
from pathlib import Path
from typing import Optional, Set, Dict, List, Any
from dataclasses import dataclass

from .component_validator import ComponentValidator
from .hipstershop_architecture import ComponentType


@dataclass
class ComponentScanResult:
    """组件扫描结果"""
    valid_components: Set[str]
    scan_timestamp: float
    scan_duration: float
    component_counts: Dict[str, int]
    data_root_path: str
    success: bool
    error_message: Optional[str] = None


class GlobalComponentManager:
    """全局组件管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls, data_root_path: str = "/data/phaseone_data"):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, data_root_path: str = "/data/phaseone_data"):
        """初始化全局组件管理器"""
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            self.logger = logging.getLogger(__name__)
            self.data_root_path = data_root_path
            self._component_validator: Optional[ComponentValidator] = None
            self._scan_result: Optional[ComponentScanResult] = None
            self._cache_valid_until = 0  # 缓存有效期
            self._cache_duration = 3600  # 缓存1小时
            
            self._initialized = True
            self.logger.info("🚀 全局组件管理器初始化完成")
    
    def get_component_validator(self, force_refresh: bool = False) -> ComponentValidator:
        """获取组件验证器实例（带缓存）"""
        
        current_time = time.time()
        
        # 检查是否需要刷新缓存
        if (force_refresh or 
            self._component_validator is None or 
            current_time > self._cache_valid_until):
            
            self._refresh_component_data()
        
        return self._component_validator
    
    def _refresh_component_data(self):
        """刷新组件数据"""
        
        start_time = time.time()
        self.logger.info("🔄 开始刷新全局组件数据...")
        
        try:
            # 创建新的ComponentValidator实例
            self._component_validator = ComponentValidator()
            
            # 记录扫描结果
            scan_duration = time.time() - start_time
            
            self._scan_result = ComponentScanResult(
                valid_components=self._component_validator.valid_components.copy(),
                scan_timestamp=start_time,
                scan_duration=scan_duration,
                component_counts=self._get_component_counts(),
                data_root_path=self.data_root_path,
                success=True
            )
            
            # 更新缓存有效期
            self._cache_valid_until = time.time() + self._cache_duration
            
            self.logger.info(f"✅ 全局组件数据刷新完成")
            self.logger.info(f"   扫描耗时: {scan_duration:.2f}秒")
            self.logger.info(f"   组件总数: {len(self._component_validator.valid_components)}")
            self.logger.info(f"   缓存有效期: {self._cache_duration}秒")
            
        except Exception as e:
            self.logger.error(f"❌ 全局组件数据刷新失败: {e}")
            
            # 记录失败结果
            self._scan_result = ComponentScanResult(
                valid_components=set(),
                scan_timestamp=start_time,
                scan_duration=time.time() - start_time,
                component_counts={},
                data_root_path=self.data_root_path,
                success=False,
                error_message=str(e)
            )
            
            # 如果刷新失败但已有旧的validator，继续使用
            if self._component_validator is None:
                self.logger.warning("⚠️ 创建默认ComponentValidator作为备用")
                self._component_validator = ComponentValidator()
    
    def _get_component_counts(self) -> Dict[str, int]:
        """获取各类型组件数量统计"""
        
        if not self._component_validator:
            return {}
        
        counts = {
            "total": len(self._component_validator.valid_components),
            "services": 0,
            "pods": 0,
            "nodes": 0,
            "infrastructure": 0
        }
        
        for component in self._component_validator.valid_components:
            if component.startswith('aiops-k8s-'):
                counts["nodes"] += 1
            elif '-' in component and component.split('-')[-1].isdigit():
                counts["pods"] += 1
            elif component in ['tidb', 'tikv', 'pd', 'redis']:
                counts["infrastructure"] += 1
            elif component.endswith('service') or component in ['frontend']:
                counts["services"] += 1
        
        return counts
    
    def get_scan_result(self) -> Optional[ComponentScanResult]:
        """获取最近的扫描结果"""
        return self._scan_result
    
    def is_valid_component(self, component_name: str) -> bool:
        """检查组件是否有效"""
        validator = self.get_component_validator()
        return validator.is_valid_component(component_name)
    
    def normalize_component_name(self, component_name: str) -> Optional[str]:
        """标准化组件名称"""
        validator = self.get_component_validator()
        return validator.normalize_component_name(component_name)
    
    def get_valid_components(self) -> Set[str]:
        """获取所有有效组件"""
        validator = self.get_component_validator()
        return validator.valid_components.copy()
    
    def validate_and_suggest_components(self, candidate_components: List[str]) -> List[Any]:
        """验证并建议组件"""
        validator = self.get_component_validator()
        return validator.validate_and_suggest_components(candidate_components)
    
    def enforce_component_constraint(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """强制组件约束"""
        validator = self.get_component_validator()
        return validator.enforce_component_constraint(reasoning_result)
    
    def get_component_type(self, component_name: str) -> Optional[ComponentType]:
        """获取组件类型"""
        validator = self.get_component_validator()
        return validator.architecture.get_component_type(component_name)

    def get_component_summary(self) -> Dict[str, Any]:
        """获取组件摘要信息"""
        validator = self.get_component_validator()

        if not validator:
            return {
                "total_components": 0,
                "component_types": {},
                "services": [],
                "pods": [],
                "nodes": [],
                "error": "ComponentValidator not available"
            }

        # 获取组件统计
        component_counts = self._get_component_counts()

        # 获取各类型组件列表
        services = []
        pods = []
        nodes = []

        try:
            # 从validator的whitelist获取组件信息
            if hasattr(validator, 'whitelist') and validator.whitelist:
                for component_name in validator.whitelist:
                    component_type = validator.architecture.get_component_type(component_name)

                    if component_type == ComponentType.SERVICE:
                        services.append(component_name)
                    elif component_type == ComponentType.POD:
                        pods.append(component_name)
                    elif component_type == ComponentType.NODE:
                        nodes.append(component_name)
        except Exception as e:
            self.logger.warning(f"⚠️ 获取组件列表时出错: {e}")

        return {
            "total_components": sum(component_counts.values()),
            "component_types": component_counts,
            "services": services,
            "pods": pods,
            "nodes": nodes,
            "cache_info": {
                "last_refresh": self._scan_result.scan_timestamp if self._scan_result else None,
                "cache_duration": self._cache_duration,
                "is_cached": self._component_validator is not None
            }
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取组件管理器统计信息"""
        
        scan_result = self.get_scan_result()
        
        stats = {
            "manager_initialized": self._initialized,
            "cache_valid": time.time() < self._cache_valid_until,
            "cache_remaining_seconds": max(0, self._cache_valid_until - time.time()),
            "data_root_path": self.data_root_path
        }
        
        if scan_result:
            stats.update({
                "last_scan_timestamp": scan_result.scan_timestamp,
                "last_scan_duration": scan_result.scan_duration,
                "last_scan_success": scan_result.success,
                "component_counts": scan_result.component_counts,
                "error_message": scan_result.error_message
            })
        
        return stats
    
    @classmethod
    def reset_instance(cls):
        """重置单例实例（主要用于测试）"""
        with cls._lock:
            cls._instance = None
            cls._initialized = False


# 便捷函数
def get_global_component_manager(data_root_path: str = "/data/phaseone_data") -> GlobalComponentManager:
    """获取全局组件管理器实例"""
    return GlobalComponentManager(data_root_path)


def is_valid_component_global(component_name: str, data_root_path: str = "/data/phaseone_data") -> bool:
    """全局组件验证便捷函数"""
    manager = get_global_component_manager(data_root_path)
    return manager.is_valid_component(component_name)


def normalize_component_name_global(component_name: str, data_root_path: str = "/data/phaseone_data") -> Optional[str]:
    """全局组件名称标准化便捷函数"""
    manager = get_global_component_manager(data_root_path)
    return manager.normalize_component_name(component_name)
