{"cache_key": "pd_2025-06-18", "thresholds": {"component_type": "pd", "single_metric_thresholds": {"cpu_usage": {"threshold": 0.8, "mean": 0.6899557381922398, "std": 0.049608396817398336, "min": 0.6337387074357191, "max": 0.7667338429464906, "data_points": 5}, "memory_usage": {"threshold": **********, "mean": 171668033.18560135, "std": 10501953.064320413, "min": 159933671.27171648, "max": 184123966.9770674, "data_points": 5}, "abnormal_region_count": {"threshold": 41.857095205003475, "mean": 41.499013203613615, "std": 0.2661139863221514, "min": 41.323697011813756, "max": 41.97136900625434, "data_points": 5}, "region_health": {"threshold": 60.14794996525365, "mean": 59.94286309937456, "std": 0.15493430843348938, "min": 59.80437804030577, "max": 60.20993745656706, "data_points": 5}, "region_count": {"threshold": 209.4, "mean": 207.5236970118138, "std": 1.3941180768895103, "min": 206.6184850590688, "max": 210.0, "data_points": 5}, "store_up_count": {"threshold": 10, "mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "data_points": 5}, "store_down_count": {"threshold": 10, "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "data_points": 5}, "store_unhealth_count": {"threshold": 10, "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "data_points": 5}, "store_slow_count": {"threshold": 10, "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "data_points": 5}, "store_low_space_count": {"threshold": 10, "mean": 0.0018068102849200835, "std": 0.00404015061952707, "min": 0.0, "max": 0.009034051424600417, "data_points": 5}, "storage_size": {"threshold": **********.150938, "mean": **********.605707, "std": 16885364.548447177, "min": **********.37804, "max": **********.498262, "data_points": 5}, "storage_capacity": {"threshold": ***********.0, "mean": ***********.0, "std": 0.0, "min": ***********.0, "max": ***********.0, "data_points": 5}, "storage_used_ratio": {"threshold": 0.06000000000000001, "mean": 0.06000000000000001, "std": 0.0, "min": 0.06000000000000001, "max": 0.06000000000000001, "data_points": 5}, "leader_count": {"threshold": 209.4, "mean": 207.5236970118138, "std": 1.3941180768895103, "min": 206.6184850590688, "max": 210.0, "data_points": 5}, "learner_count": {"threshold": 10, "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "data_points": 5}, "witness_count": {"threshold": 10, "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "data_points": 5}}, "correlation_thresholds": {"pd_cluster_instability": {"primary_metrics": ["abnormal_region_count"], "secondary_metrics": ["store_down_count", "store_unhealth_count"], "rule": {"primary_metrics": ["abnormal_region_count"], "secondary_metrics": ["store_down_count", "store_unhealth_count"], "correlation_threshold": 0.5, "description": "PD集群不稳定：异常Region增加通常伴随存储节点问题"}, "enabled": true}}, "anomaly_detection_model": {"type": "statistical", "feature_stats": {"cpu_usage": {"mean": 0.6899557381922398, "std": 0.049608396817398336, "q95": 0.7528575399583044, "q05": 0.639566365531619}, "memory_usage": {"mean": 171668033.18560135, "std": 10501953.064320413, "q95": 183541438.3544128, "q05": 160952049.52139008}, "abnormal_region_count": {"mean": 41.499013203613615, "std": 0.2661139863221514, "q95": 41.857095205003475, "q05": 41.33895760945101}, "region_health": {"mean": 59.94286309937456, "std": 0.15493430843348938, "q95": 60.14794996525365, "q05": 59.82350243224462}, "region_count": {"mean": 207.5236970118138, "std": 1.3941180768895103, "q95": 209.4, "q05": 206.69478804725503}, "store_up_count": {"mean": 1.0, "std": 0.0, "q95": 1.0, "q05": 1.0}, "store_down_count": {"mean": 0.0, "std": 0.0, "q95": 0.0, "q05": 0.0}, "store_unhealth_count": {"mean": 0.0, "std": 0.0, "q95": 0.0, "q05": 0.0}, "store_slow_count": {"mean": 0.0, "std": 0.0, "q95": 0.0, "q05": 0.0}, "store_low_space_count": {"mean": 0.0018068102849200835, "std": 0.00404015061952707, "q95": 0.007227241139680332, "q05": 0.0}, "storage_size": {"mean": **********.605707, "std": 16885364.548447177, "q95": **********.150938, "q05": **********.273116}, "storage_capacity": {"mean": ***********.0, "std": 0.0, "q95": ***********.0, "q05": ***********.0}, "storage_used_ratio": {"mean": 0.06000000000000001, "std": 0.0, "q95": 0.06000000000000001, "q05": 0.06000000000000001}, "leader_count": {"mean": 207.5236970118138, "std": 1.3941180768895103, "q95": 209.4, "q05": 206.69478804725503}, "learner_count": {"mean": 0.0, "std": 0.0, "q95": 0.0, "q05": 0.0}, "witness_count": {"mean": 0.0, "std": 0.0, "q95": 0.0, "q05": 0.0}}, "feature_names": ["cpu_usage", "memory_usage", "abnormal_region_count", "region_health", "region_count", "store_up_count", "store_down_count", "store_unhealth_count", "store_slow_count", "store_low_space_count", "storage_size", "storage_capacity", "storage_used_ratio", "leader_count", "learner_count", "witness_count"]}, "data_statistics": {}, "correlation_matrix": {"cpu_usage": {"cpu_usage": 1.0, "memory_usage": 0.7622321440979978, "abnormal_region_count": -0.40393839254452557, "region_health": -0.5117844590595113, "region_count": -0.39953829682114533, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": -0.3051393911566806, "storage_size": -0.9061581506395371, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": -0.39953829682114533, "learner_count": NaN, "witness_count": NaN}, "memory_usage": {"cpu_usage": 0.7622321440979978, "memory_usage": 1.0, "abnormal_region_count": -0.25028513234297467, "region_health": -0.31271080924437017, "region_count": -0.24773104127431375, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": -0.19281924176747292, "storage_size": -0.5145393525051278, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": -0.24773104127431375, "learner_count": NaN, "witness_count": NaN}, "abnormal_region_count": {"cpu_usage": -0.40393839254452557, "memory_usage": -0.25028513234297467, "abnormal_region_count": 1.0, "region_health": 0.989352995936778, "region_count": 0.9999837457764482, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": 0.9922624688659695, "storage_size": 0.11658378426006197, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": 0.9999837457764482, "learner_count": NaN, "witness_count": NaN}, "region_health": {"cpu_usage": -0.5117844590595113, "memory_usage": -0.31271080924437017, "abnormal_region_count": 0.989352995936778, "region_health": 1.0, "region_count": 0.9885071290010464, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": 0.9636284302805861, "storage_size": 0.23307243097480534, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": 0.9885071290010464, "learner_count": NaN, "witness_count": NaN}, "region_count": {"cpu_usage": -0.39953829682114533, "memory_usage": -0.24773104127431375, "abnormal_region_count": 0.9999837457764482, "region_health": 0.9885071290010464, "region_count": 1.0, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": 0.9929542386439361, "storage_size": 0.11196963183419725, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": 1.0, "learner_count": NaN, "witness_count": NaN}, "store_up_count": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "store_down_count": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "store_unhealth_count": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "store_slow_count": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "store_low_space_count": {"cpu_usage": -0.3051393911566806, "memory_usage": -0.19281924176747292, "abnormal_region_count": 0.9922624688659695, "region_health": 0.9636284302805861, "region_count": 0.9929542386439361, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": 1.0, "storage_size": 0.015245189327056595, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": 0.9929542386439365, "learner_count": NaN, "witness_count": NaN}, "storage_size": {"cpu_usage": -0.9061581506395371, "memory_usage": -0.5145393525051278, "abnormal_region_count": 0.11658378426006197, "region_health": 0.23307243097480534, "region_count": 0.11196963183419725, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": 0.015245189327056595, "storage_size": 1.0, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": 0.11196963183418111, "learner_count": NaN, "witness_count": NaN}, "storage_capacity": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "storage_used_ratio": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "leader_count": {"cpu_usage": -0.39953829682114533, "memory_usage": -0.24773104127431375, "abnormal_region_count": 0.9999837457764482, "region_health": 0.9885071290010464, "region_count": 1.0, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": 0.9929542386439365, "storage_size": 0.11196963183418111, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": 1.0, "learner_count": NaN, "witness_count": NaN}, "learner_count": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}, "witness_count": {"cpu_usage": NaN, "memory_usage": NaN, "abnormal_region_count": NaN, "region_health": NaN, "region_count": NaN, "store_up_count": NaN, "store_down_count": NaN, "store_unhealth_count": NaN, "store_slow_count": NaN, "store_low_space_count": NaN, "storage_size": NaN, "storage_capacity": NaN, "storage_used_ratio": NaN, "leader_count": NaN, "learner_count": NaN, "witness_count": NaN}}, "strong_correlations": [{"metric1": "cpu_usage", "metric2": "memory_usage", "correlation": 0.7622321440979978}, {"metric1": "cpu_usage", "metric2": "storage_size", "correlation": -0.9061581506395371}, {"metric1": "abnormal_region_count", "metric2": "region_health", "correlation": 0.989352995936778}, {"metric1": "abnormal_region_count", "metric2": "region_count", "correlation": 0.9999837457764482}, {"metric1": "abnormal_region_count", "metric2": "store_low_space_count", "correlation": 0.9922624688659695}, {"metric1": "abnormal_region_count", "metric2": "leader_count", "correlation": 0.9999837457764482}, {"metric1": "region_health", "metric2": "region_count", "correlation": 0.9885071290010464}, {"metric1": "region_health", "metric2": "store_low_space_count", "correlation": 0.9636284302805861}, {"metric1": "region_health", "metric2": "leader_count", "correlation": 0.9885071290010464}, {"metric1": "region_count", "metric2": "store_low_space_count", "correlation": 0.9929542386439361}, {"metric1": "region_count", "metric2": "leader_count", "correlation": 1.0}, {"metric1": "store_low_space_count", "metric2": "leader_count", "correlation": 0.9929542386439365}]}, "timestamp": "2025-07-27T20:57:39.913346"}