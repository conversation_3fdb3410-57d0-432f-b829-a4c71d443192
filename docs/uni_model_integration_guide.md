# uni大模型集成指南

## 概述

uni大模型完全兼容OpenAI API规范，可以直接使用AutoGen现有的`OpenAIChatCompletionClient`来接入uni大模型服务。由于uni大模型使用非标准的模型ID（如`deepseek-r1:32b-16k`），需要额外配置`model_info`参数。

## 技术规格

- **Base URL**: `https://uni-api.cstcloud.cn/v1`
- **API兼容性**: 完全兼容OpenAI API v1规范
- **支持的功能**: 聊天补全、工具调用、JSON输出等
- **认证方式**: Bearer Token认证
- **模型ID格式**: 非标准格式（如`deepseek-r1:32b-16k`）

## 核心问题解答

### ❓ 能否仅通过配置文件实现uni大模型调用？

**✅ 答案：是的，完全可以！无需修改任何Python代码。**

关键在于正确配置`model_info`参数来处理非标准的模型ID。

## 快速开始

### 1. 环境准备

设置uni大模型的API密钥：

```bash
export UNI_API_KEY="your-bearer-token"
```

### 2. 最简配置方案（推荐）

#### 配置文件方式（无需修改代码）

```yaml
# config.yaml
uni_model:
  provider: "OpenAIChatCompletionClient"
  config:
    model: "deepseek-r1:32b-16k"  # 使用实际的uni模型名称
    base_url: "https://uni-api.cstcloud.cn/v1"
    api_key: "${UNI_API_KEY}"
    # 关键：必须指定model_info处理非标准模型ID
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "UNKNOWN"
    timeout: 30.0
    max_retries: 3
```

```python
# 从配置加载（无需修改）
from autogen_core.models import ChatCompletionClient

config = {
    "provider": "OpenAIChatCompletionClient",
    "config": {
        "model": "deepseek-r1:32b-16k",
        "base_url": "https://uni-api.cstcloud.cn/v1",
        "api_key": "your-bearer-token",
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "UNKNOWN",
        }
    }
}

client = ChatCompletionClient.load_component(config)
```

#### Python代码示例

```python
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import UserMessage, ModelFamily
import asyncio

# 创建uni大模型客户端
client = OpenAIChatCompletionClient(
    model="deepseek-r1:32b-16k",  # 使用实际的uni模型名称
    base_url="https://uni-api.cstcloud.cn/v1",
    api_key="your-bearer-token",
    # 关键：指定model_info处理非标准模型ID
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
    }
)

# 发送消息
async def chat_with_uni():
    response = await client.create([
        UserMessage(content="你好，请介绍一下你自己。", source="user")
    ])
    print(response.content)

# 运行
asyncio.run(chat_with_uni())
```

## 高级配置

### 1. 模型能力配置

如果使用的模型不在标准OpenAI模型列表中，需要手动指定模型能力：

```python
client = OpenAIChatCompletionClient(
    model="custom-model-name",
    base_url="https://uni-api.cstcloud.cn/v1",
    api_key="your-uni-api-key",
    model_capabilities={
        "vision": False,           # 是否支持视觉输入
        "function_calling": True,  # 是否支持工具调用
        "json_output": True,       # 是否支持JSON输出
    },
)
```

### 2. 性能优化配置

```python
client = OpenAIChatCompletionClient(
    model="gpt-4",
    base_url="https://uni-api.cstcloud.cn/v1",
    api_key="your-uni-api-key",
    # 超时和重试配置
    timeout=60.0,
    max_retries=3,
    # 生成参数
    temperature=0.7,
    max_tokens=2048,
    top_p=1.0,
    frequency_penalty=0.0,
    presence_penalty=0.0,
)
```

### 3. 工具调用示例

```python
from autogen_core.tools import FunctionTool
from pydantic import BaseModel

# 定义工具
class CalculatorArgs(BaseModel):
    a: float
    b: float
    operation: str

def calculator(args: CalculatorArgs) -> str:
    if args.operation == "add":
        return str(args.a + args.b)
    elif args.operation == "multiply":
        return str(args.a * args.b)
    return "不支持的操作"

tool = FunctionTool(calculator, description="执行基本数学运算")

# 使用工具
response = await client.create(
    messages=[UserMessage(content="请计算 15 + 27", source="user")],
    tools=[tool]
)
```

## 与现有OpenAI实现的对比

| 特性 | OpenAI | uni大模型 |
|------|--------|-----------|
| API兼容性 | 原生支持 | 完全兼容 |
| 配置方式 | 默认base_url | 需指定base_url |
| 功能支持 | 全功能 | 兼容全功能 |
| 切换成本 | - | 仅需修改配置 |

## 错误处理

### 常见错误及解决方案

1. **认证错误**
   ```
   Error: Invalid API key
   ```
   解决：检查UNI_API_KEY环境变量是否正确设置

2. **网络连接错误**
   ```
   Error: Connection timeout
   ```
   解决：检查网络连接，可能需要增加timeout值

3. **模型不存在错误**
   ```
   Error: Model not found
   ```
   解决：确认使用的模型名称在uni大模型服务中可用

### 错误处理代码示例

```python
import asyncio
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import UserMessage

async def safe_chat_with_uni():
    try:
        client = OpenAIChatCompletionClient(
            model="gpt-3.5-turbo",
            base_url="https://uni-api.cstcloud.cn/v1",
            api_key="your-uni-api-key",
            timeout=30.0,
            max_retries=3,
        )
        
        response = await client.create([
            UserMessage(content="Hello", source="user")
        ])
        
        return response.content
        
    except Exception as e:
        print(f"调用uni大模型时出错: {e}")
        # 可以在这里实现降级策略，比如切换到其他模型
        return None
```

## 最佳实践

1. **环境变量管理**
   - 使用环境变量存储API密钥
   - 不要在代码中硬编码敏感信息

2. **配置管理**
   - 使用配置文件管理不同环境的设置
   - 支持动态切换模型提供商

3. **错误处理**
   - 实现适当的重试机制
   - 提供降级策略

4. **性能优化**
   - 根据使用场景调整timeout和max_retries
   - 合理设置temperature和max_tokens

## 测试验证

运行提供的测试脚本验证集成：

```bash
# 设置API密钥
export UNI_API_KEY="your-actual-api-key"

# 运行测试
python examples/uni_model_config_example.py
```

## 总结

uni大模型的集成非常简单，因为：

1. **无需额外开发** - 现有的`OpenAIChatCompletionClient`已经支持
2. **配置简单** - 只需修改`base_url`参数
3. **功能完整** - 支持所有OpenAI API功能
4. **切换容易** - 可以通过配置文件轻松切换

这种设计体现了AutoGen框架的良好架构，通过标准化的接口支持多种LLM提供商。
