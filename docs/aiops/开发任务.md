✨ 本次开发计划（待确认）

功能目标
设计基于AutoGen框架的AIOps故障诊断系统，针对HipsterShop微服务系统
实现从input.json读取故障案例UUID和异常时间段
分析三类关键数据（Metric、Trace、Log）识别故障根因
生成符合评分标准的JSON输出（组件、原因、时间和推理轨迹）
技术选型
框架：AutoGen多智能体框架
数据处理：PyArrow/Pandas处理Parquet文件
分析工具：针对三类数据的专业分析库
语言：Python
智能体架构设计
基于HipsterShop系统架构设计专业智能体
针对评分标准（LA、TA、Efficiency、Explainability）优化智能体交互
数据处理流程
基于数据信息说明文档中的数据格式和字段定义设计处理流程
针对三类数据的特点设计专门的分析策略
推理策略
设计符合评分标准的推理轨迹生成方法
确保reasoning_trace满足规则要求
详细设计方案
1. 数据特性与分析策略
根据 数据信息说明文档.md，我们需要处理三类关键数据：

1.1 监控指标(Metric)数据分析策略
数据特点：

APM指标：反映业务服务请求与响应情况
关键字段：time, request, response, rrt, error_ratio等
粒度：60秒
对象类型：pod, service
Infra指标：反映系统底层资源使用情况
Pod级指标：pod_cpu_usage, pod_memory_working_set_bytes等
Node级指标：node_disk_written_bytes_total, node_network_receive_bytes_total等
TiDB指标：connection_count, failed_query_ops, slow_query等
分析策略：

设计MetricAnalysisAgent专门处理指标数据，实现：
时序异常检测：检测指标突变、持续偏离和异常模式
资源瓶颈识别：识别CPU、内存、磁盘I/O、网络等资源瓶颈
服务性能分析：分析请求延迟、错误率和吞吐量异常
组件关联分析：建立不同组件指标间的关联关系
1.2 调用链(Trace)数据分析策略
数据特点：

文件格式：trace_jaeger-span_日期_小时-59-00.parquet
关键字段：traceID, spanID, operationName, startTime, duration, tags, process
记录了微服务间的调用关系、时间和状态
分析策略：

设计TraceAnalysisAgent专门处理调用链数据，实现：
服务依赖图构建：识别服务间调用关系和依赖路径
异常调用识别：检测高延迟、错误状态码和异常调用模式
瓶颈服务定位：识别调用链中的性能瓶颈服务
根因传播分析：分析故障如何在服务间传播
1.3 日志(Log)数据分析策略
数据特点：

文件格式：log_filebeat-server_日期_小时-00-00.parquet
关键字段：k8_namespace, @timestamp, k8_pod, message, k8_node_name
记录了各组件的运行日志和错误信息
分析策略：

设计LogAnalysisAgent专门处理日志数据，实现：
错误日志提取：识别ERROR、FATAL、WARNING等级别日志
异常模式识别：识别常见错误模式如连接失败、超时、OOM等
时序关联分析：将日志事件与时间轴关联
组件状态推断：根据日志内容推断组件健康状态
2. 智能体架构设计
基于HipsterShop系统架构和数据特性，设计以下智能体：

2.1 核心智能体
OrchestratorAgent（协调者智能体）
功能：整体流程控制，任务分发和结果汇总
输入：故障案例UUID和时间范围
输出：最终的根因分析结果
关键方法：
initialize_investigation(uuid, time_range)：初始化调查
coordinate_analysis()：协调各分析智能体
synthesize_results()：综合分析结果
generate_final_output()：生成最终输出
DataLoaderAgent（数据加载智能体）
功能：根据时间范围加载相关Parquet文件，处理UTC与北京时间的转换
处理数据类型：Metric、Trace、Log三类数据
核心时间转换逻辑：
- 输入：UTC时间片段（如"2025-04-26T19:10:15Z"）
- 文件定位：转换为北京时间（UTC+8）用于文件名匹配
- 数据过滤：使用原始UTC时间进行文件内数据筛选
- 时间范围策略：不扩展时间窗口，只加载实际需要的时间范围
关键方法：
utc_to_beijing_time(utc_time)：UTC时间转北京时间（用于文件名）
get_time_range_files(start_utc, end_utc)：根据时间范围定位所有数据文件
load_metric_data(time_range)：加载指标数据（遍历所有子目录和parquet文件）
load_trace_data(time_range)：加载调用链数据（按北京小时精确定位）
load_log_data(time_range)：加载日志数据（按北京小时精确定位）
filter_data_by_utc_time(data, utc_time_range)：使用UTC时间过滤数据
filter_trace_data_by_utc_time(data, utc_time_range)：处理trace数据的毫秒时间戳
preprocess_data()：数据预处理和初步清洗
2.2 专家分析智能体
MetricAnalysisAgent（指标分析智能体）
功能：分析APM和Infra指标，检测异常
分析维度：
Pod级指标分析：CPU、内存、磁盘I/O、网络等
Service级指标分析：请求量、响应时间、错误率等
Node级指标分析：主机资源使用情况
TiDB组件指标分析：数据库性能指标
关键方法：
detect_metric_anomalies()：检测指标异常
identify_resource_bottlenecks()：识别资源瓶颈
correlate_service_metrics()：关联服务指标
generate_metric_insights()：生成指标分析结果
TraceAnalysisAgent（调用链分析智能体）
功能：分析服务调用关系，识别异常调用
分析维度：
服务依赖分析：构建服务调用图
调用延迟分析：识别高延迟调用
错误传播分析：分析错误如何在服务间传播
异常模式识别：识别异常调用模式
关键方法：
build_service_dependency_graph()：构建服务依赖图
detect_latency_anomalies()：检测延迟异常
analyze_error_propagation()：分析错误传播
generate_trace_insights()：生成调用链分析结果
LogAnalysisAgent（日志分析智能体）
功能：分析容器日志，提取错误信息
分析维度：
错误日志分析：提取ERROR级别日志
异常模式识别：识别常见错误模式
组件状态分析：推断组件健康状态
时序事件分析：构建日志事件时间线
关键方法：
extract_error_logs()：提取错误日志
identify_error_patterns()：识别错误模式
infer_component_status()：推断组件状态
generate_log_insights()：生成日志分析结果
2.3 推理与输出智能体
ReasoningAgent（推理智能体）
功能：综合各分析结果，执行根因推理
推理策略：
时序关联：分析事件时间顺序
组件关联：关联同一组件的不同异常
因果推断：推断异常的因果关系
证据收集：收集支持根因的证据
关键方法：
correlate_anomalies()：关联各类异常
build_causal_graph()：构建因果图
identify_root_cause()：识别根因
construct_reasoning_trace()：构建推理轨迹
OutputFormatterAgent（输出格式化智能体）
功能：格式化输出符合评分标准的JSON
输出优化：
组件准确性：确保component字段准确
原因描述：优化reason字段表述
推理轨迹：优化reasoning_trace结构
格式验证：验证输出符合规则要求
关键方法：
format_component_and_reason()：格式化组件和原因
optimize_reasoning_trace()：优化推理轨迹
validate_output_format()：验证输出格式
generate_final_json()：生成最终JSON输出
3. 推理策略与评分优化
根据 规则说明.md 规则说明.md中的评分标准，我们需要优化以下方面：

3.1 组件准确性优化（LA得分，40%）
设计策略：
多维证据交叉验证：结合指标、日志和调用链多维证据
组件权重计算：根据异常严重程度和数量计算组件权重
历史模式匹配：与已知故障模式比对
专家规则应用：应用微服务故障诊断领域知识
3.2 故障类型准确性优化（TA得分，40%）
设计策略：
故障特征提取：从异常数据中提取故障特征
故障类型映射：将特征映射到预定义故障类型
证据链构建：构建支持故障类型判断的证据链
语义表达优化：优化reason字段的表述方式
3.3 推理效率优化（Efficiency得分，10%）
设计策略：
推理步骤优化：控制reasoning_trace步骤数量
关键证据筛选：筛选最有价值的证据
推理路径简化：避免冗余和循环推理
直接证据优先：优先使用直接证据
3.4 可解释性优化（Explainability得分，10%）
设计策略：
证据多样性：确保使用指标、日志、调用链多类证据
观察结果精简：控制observation在20词以内
推理步骤清晰：每步action和observation逻辑清晰
证据链完整：构建完整的推理证据链
4. 关键时间处理机制
基于数据信息说明文档中的时间规范，系统需要处理复杂的时间转换逻辑：

4.1 时间格式说明
输入时间格式：
- input.json中的时间片段：UTC时间格式（如"2025-04-26T19:10:15Z"）
- 表示故障发生的实际UTC时间

文件命名时间格式：
- Log文件：log_filebeat-server_{北京日期}_{北京小时}-00-00.parquet
- Trace文件：trace_jaeger-span_{北京日期}_{北京小时}-00-00.parquet
- Metric文件：按北京日期组织（如2025-04-27目录）

文件内数据时间格式：
- Metric数据的time字段：UTC时间
- Log数据的@timestamp字段：UTC时间
- Trace数据的startTimeMillis字段：UTC时间戳

4.2 时间转换逻辑
关键转换规则：
1. UTC时间 → 北京时间（UTC+8）：用于文件路径定位
2. 跨日期边界处理：UTC 2025-04-26 23:30 → 北京时间 2025-04-27 07:30
3. 文件内数据过滤：始终使用原始UTC时间进行数据筛选
4. 时间范围策略：不扩展时间窗口，除非时间范围跨越多个小时

实现示例：
```python
def utc_to_beijing_time(utc_time_str):
    """将UTC时间转换为北京时间，用于文件名定位"""
    utc_dt = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
    beijing_dt = utc_dt + timedelta(hours=8)
    return beijing_dt.strftime('%Y-%m-%d'), beijing_dt.strftime('%H')

def get_time_range_files(start_utc, end_utc):
    """根据UTC时间范围定位所需的数据文件"""
    start_dt = datetime.fromisoformat(start_utc.replace('Z', '+00:00'))
    end_dt = datetime.fromisoformat(end_utc.replace('Z', '+00:00'))

    # 转换为北京时间并确定小时范围
    start_beijing_dt = start_dt + timedelta(hours=8)
    end_beijing_dt = end_dt + timedelta(hours=8)
    start_hour = start_beijing_dt.replace(minute=0, second=0, microsecond=0)
    end_hour = end_beijing_dt.replace(minute=0, second=0, microsecond=0)

    files = {'metrics': [], 'traces': [], 'logs': []}

    # 遍历时间范围内的每个小时
    current_hour = start_hour
    while current_hour <= end_hour:
        beijing_date = current_hour.strftime('%Y-%m-%d')
        beijing_hour = current_hour.strftime('%H')

        # 构建文件路径
        date_dir = f"/data/phaseone_data/{beijing_date}/{beijing_date}"

        # Metric文件：遍历所有子目录和parquet文件
        metric_dir = f"{date_dir}/metric-parquet"
        # 递归遍历apm/pod, apm/service, infra/*, other/*等子目录

        # Trace文件：按小时精确定位
        trace_file = f"{date_dir}/trace-parquet/trace_jaeger-span_{beijing_date}_{beijing_hour}-00-00.parquet"

        # Log文件：按小时精确定位
        log_file = f"{date_dir}/log-parquet/log_filebeat-server_{beijing_date}_{beijing_hour}-00-00.parquet"

        current_hour += timedelta(hours=1)

    return files
```

4.3 数据加载策略
分层加载策略：
1. 文件定位层：使用北京时间确定需要加载的文件列表
2. 数据过滤层：使用UTC时间过滤文件内的具体数据记录
3. 时间对齐层：确保三类数据的时间基准一致（均为UTC）

数据类型特殊处理：
- Metric数据：按日期组织，需要遍历所有子目录（apm/pod, apm/service, infra/*, other/*）
- Trace数据：按小时组织，使用startTimeMillis字段（毫秒时间戳）进行UTC过滤
- Log数据：按小时组织，使用@timestamp字段进行UTC过滤

边界处理：
- 精确时间范围：不扩展时间窗口，只加载实际需要的时间范围
- 多文件合并：当时间范围跨越多个小时文件时，需要合并加载
- 时区一致性：确保所有分析过程中的时间比较都基于UTC时间
- 去重机制：避免重复加载相同的文件（特别是Metric按日期组织的情况）

文件数量示例：
- 同一小时内（如19:10-19:40）：Metrics 114个文件，Traces 1个文件，Logs 1个文件
- 跨小时范围（如18:30-20:30）：Metrics 114个文件，Traces 3个文件，Logs 3个文件

5. DataLoaderAgent完整实现示例

基于验证的时间转换逻辑，DataLoaderAgent的核心实现如下：

```python
class DataLoaderAgent:
    def __init__(self, data_root_path: str = "/data/phaseone_data"):
        self.data_root_path = Path(data_root_path)

    def utc_to_beijing_time(self, utc_time_str: str) -> Tuple[str, str]:
        """将UTC时间转换为北京时间，用于文件名定位"""
        utc_dt = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
        beijing_dt = utc_dt + timedelta(hours=8)
        return beijing_dt.strftime('%Y-%m-%d'), beijing_dt.strftime('%H')

    def get_time_range_files(self, start_utc: str, end_utc: str) -> Dict[str, List[str]]:
        """根据UTC时间范围确定需要加载的所有数据文件"""
        start_dt = datetime.fromisoformat(start_utc.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_utc.replace('Z', '+00:00'))

        # 转换为北京时间并确定小时范围
        start_beijing_dt = start_dt + timedelta(hours=8)
        end_beijing_dt = end_dt + timedelta(hours=8)
        start_hour = start_beijing_dt.replace(minute=0, second=0, microsecond=0)
        end_hour = end_beijing_dt.replace(minute=0, second=0, microsecond=0)

        files = {'metrics': [], 'traces': [], 'logs': []}
        metric_dates = set()

        # 遍历时间范围内的每个小时
        current_hour = start_hour
        while current_hour <= end_hour:
            beijing_date = current_hour.strftime('%Y-%m-%d')
            beijing_hour = current_hour.strftime('%H')
            date_dir = self.data_root_path / beijing_date / beijing_date

            # Metric文件：遍历所有子目录和parquet文件
            if beijing_date not in metric_dates:
                metric_dir = date_dir / "metric-parquet"
                if metric_dir.exists():
                    for subdir in metric_dir.iterdir():
                        if subdir.is_dir():
                            for sub_subdir in subdir.iterdir():
                                if sub_subdir.is_dir():
                                    for parquet_file in sub_subdir.glob("*.parquet"):
                                        files['metrics'].append(str(parquet_file))
                                elif sub_subdir.suffix == '.parquet':
                                    files['metrics'].append(str(sub_subdir))
                        elif subdir.suffix == '.parquet':
                            files['metrics'].append(str(subdir))
                    metric_dates.add(beijing_date)

            # Trace和Log文件：按小时精确定位
            trace_file = date_dir / "trace-parquet" / f"trace_jaeger-span_{beijing_date}_{beijing_hour}-00-00.parquet"
            if trace_file.exists():
                files['traces'].append(str(trace_file))

            log_file = date_dir / "log-parquet" / f"log_filebeat-server_{beijing_date}_{beijing_hour}-00-00.parquet"
            if log_file.exists():
                files['logs'].append(str(log_file))

            current_hour += timedelta(hours=1)

        return files

    def filter_data_by_utc_time(self, df: pd.DataFrame, start_utc: str, end_utc: str, time_column: str) -> pd.DataFrame:
        """使用UTC时间过滤数据"""
        if df.empty or time_column not in df.columns:
            return df
        df[time_column] = pd.to_datetime(df[time_column])
        start_dt = pd.to_datetime(start_utc)
        end_dt = pd.to_datetime(end_utc)
        mask = (df[time_column] >= start_dt) & (df[time_column] <= end_dt)
        return df[mask].copy()

    def filter_trace_data_by_utc_time(self, df: pd.DataFrame, start_utc: str, end_utc: str) -> pd.DataFrame:
        """使用UTC时间过滤调用链数据（处理startTimeMillis字段）"""
        if df.empty or 'startTimeMillis' not in df.columns:
            return df
        start_dt = pd.to_datetime(start_utc)
        end_dt = pd.to_datetime(end_utc)
        start_millis = int(start_dt.timestamp() * 1000)
        end_millis = int(end_dt.timestamp() * 1000)
        mask = (df['startTimeMillis'] >= start_millis) & (df['startTimeMillis'] <= end_millis)
        return df[mask].copy()
```

验证结果：
- 同一小时内（19:10-19:40）：Metrics 114个文件，Traces 1个文件，Logs 1个文件
- 跨小时范围（18:30-20:30）：Metrics 114个文件，Traces 3个文件，Logs 3个文件

6. 系统实现框架
基于AutoGen框架，我们可以实现如下的代码结构：

aiops_diagnosis/
├── main.py                      # 主程序入口
├── config/
│   ├── agent_config.py          # 智能体配置
│   └── system_config.py         # 系统配置
├── agents/
│   ├── orchestrator_agent.py    # 协调者智能体
│   ├── data_loader_agent.py     # 数据加载智能体
│   ├── metric_analysis_agent.py # 指标分析智能体
│   ├── trace_analysis_agent.py  # 调用链分析智能体
│   ├── log_analysis_agent.py    # 日志分析智能体
│   ├── reasoning_agent.py       # 推理智能体
│   └── output_formatter_agent.py # 输出格式化智能体
├── data_processors/
│   ├── metric_processor.py      # 指标数据处理
│   ├── trace_processor.py       # 调用链数据处理
│   └── log_processor.py         # 日志数据处理
├── analysis/
│   ├── anomaly_detection.py     # 异常检测算法
│   ├── correlation_analysis.py  # 关联分析算法
│   ├── causal_inference.py      # 因果推断算法
│   └── pattern_recognition.py   # 模式识别算法
└── utils/
    ├── data_loader.py           # 数据加载工具
    ├── time_utils.py            # 时间处理工具
    ├── visualization.py         # 可视化工具
    └── output_formatter.py      # 输出格式化工具

6. 工作流程示例
以下是系统处理一个故障案例的典型工作流程：

初始化阶段
从input.json读取故障案例UUID和UTC时间范围
解析时间信息：提取start_time和end_time（UTC格式）
初始化各智能体并建立通信通道

时间转换阶段
执行UTC到北京时间的转换：
- 输入："2025-04-26T19:10:15Z" (UTC)
- 转换："2025-04-27 03:10:15" (北京时间)
- 文件定位：确定需要加载的文件路径
确定数据文件范围：
- 精确时间范围：不扩展时间窗口，只加载实际需要的时间范围
- 跨边界处理：处理跨日期、跨小时的文件定位
- 示例：19:10-19:40（同一小时）只需19:00的文件；18:30-20:30（跨小时）需要18:00、19:00、20:00的文件
数据加载阶段
DataLoaderAgent执行关键时间转换和数据加载：
1. 时间范围计算：
   - 将UTC时间转换为北京时间小时范围
   - 不扩展时间窗口，精确计算需要的文件
   - 同一小时内：只加载对应小时的文件
   - 跨小时范围：加载所有涉及小时的文件
2. 文件路径构建：
   - 基于北京时间构建文件路径
   - Log: /data/phaseone_data/{北京日期}/{北京日期}/log-parquet/log_filebeat-server_{北京日期}_{北京小时}-00-00.parquet
   - Trace: /data/phaseone_data/{北京日期}/{北京日期}/trace-parquet/trace_jaeger-span_{北京日期}_{北京小时}-00-00.parquet
   - Metric: 遍历/data/phaseone_data/{北京日期}/{北京日期}/metric-parquet/下所有子目录的parquet文件
3. 具体文件枚举：
   - Metric数据：返回具体的parquet文件路径（约114个文件，包含APM、Infra、Other三大类）
   - Trace数据：返回具体的小时文件路径
   - Log数据：返回具体的小时文件路径
4. UTC数据过滤：使用原始UTC时间精确筛选数据记录
5. 数据合并：将多个文件的数据合并为统一的时间序列
6. 时间对齐验证：确保三类数据的时间基准一致
执行数据预处理和初步清洗
并行分析阶段
MetricAnalysisAgent分析指标数据，检测异常
TraceAnalysisAgent分析调用链数据，识别异常调用
LogAnalysisAgent分析日志数据，提取错误信息
综合推理阶段
ReasoningAgent接收各分析结果
执行时序关联、组件关联和因果推断
识别根因组件和故障类型
构建推理轨迹
输出生成阶段
OutputFormatterAgent格式化最终结果
生成符合规则的JSON输出
验证输出是否满足评分标准

7. 注意事项

7.1 时间处理关键点
- UTC时间与北京时间的正确转换：文件名使用北京时间，数据内容使用UTC时间
- 不扩展时间窗口：只加载实际需要的时间范围，避免不必要的数据加载
- 跨小时边界处理：正确识别时间范围是否跨越多个小时文件
- 时区一致性：确保所有数据分析都基于UTC时间

7.2 数据加载策略
- Metric数据：按日期组织，需要遍历所有子目录获取具体parquet文件（约114个文件）
- Trace/Log数据：按小时组织，精确定位到具体的小时文件
- 去重机制：避免重复加载相同的文件
- 文件存在性检查：处理文件不存在的情况

7.3 性能和错误处理
- 数据完整性：验证加载的数据覆盖故障时间范围
- 性能优化：合理使用并行处理和数据缓存
- 错误处理：处理文件不存在、数据格式异常等情况
- 内存管理：大量parquet文件加载时的内存优化

7.4 验证结果示例
- 同一小时内（19:10-19:40）：Metrics 114个文件，Traces 1个文件，Logs 1个文件
- 跨小时范围（18:30-20:30）：Metrics 114个文件，Traces 3个文件，Logs 3个文件