#!/usr/bin/env python3
"""
分析批量测试日志，提取多维数据获取情况
"""
import re
import json
from pathlib import Path

def analyze_batch_test_logs():
    """分析批量测试的数据获取情况"""
    
    # 读取测试结果
    with open('test_threshold_comparison.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("🔍 **批量测试多维数据分析报告**")
    print("=" * 60)
    
    # 分析每个案例的根因判断
    frontend_cases = []
    other_cases = []
    
    for case in results:
        uuid = case['uuid']
        component = case['component']
        reason = case['reason']
        
        if component == 'frontend':
            frontend_cases.append({
                'uuid': uuid,
                'reason': reason,
                'reasoning_trace': case.get('reasoning_trace', [])
            })
        else:
            other_cases.append({
                'uuid': uuid,
                'component': component,
                'reason': reason
            })
    
    print(f"📊 **根因分布统计**:")
    print(f"   Frontend根因: {len(frontend_cases)} 个案例")
    print(f"   其他根因: {len(other_cases)} 个案例")
    print()
    
    # 分析Frontend案例的推理质量
    print("🚨 **Frontend案例详细分析**:")
    for i, case in enumerate(frontend_cases, 1):
        print(f"\n案例 {i}: {case['uuid']}")
        print(f"   根因描述: {case['reason']}")
        
        # 分析推理步骤中的数据来源
        trace_mentions = 0
        log_mentions = 0
        metric_mentions = 0
        
        for step in case['reasoning_trace']:
            observation = step.get('observation', '').lower()
            if any(keyword in observation for keyword in ['latency', 'ms', 'trace', 'call']):
                trace_mentions += 1
            if any(keyword in observation for keyword in ['log', 'error', 'critical']):
                log_mentions += 1
            if any(keyword in observation for keyword in ['cpu', 'memory', 'metric', 'resource']):
                metric_mentions += 1
        
        print(f"   数据来源分析:")
        print(f"     Trace数据提及: {trace_mentions} 次")
        print(f"     Log数据提及: {log_mentions} 次") 
        print(f"     Metric数据提及: {metric_mentions} 次")
        
        # 检查是否有具体数值
        has_specific_values = False
        for step in case['reasoning_trace']:
            observation = step.get('observation', '')
            if re.search(r'\d+\.?\d*\s*(ms|秒|%)', observation):
                has_specific_values = True
                break
        
        print(f"     包含具体数值: {'是' if has_specific_values else '否'}")
    
    print(f"\n📈 **其他根因案例**:")
    for case in other_cases:
        print(f"   {case['uuid']}: {case['component']} - {case['reason']}")
    
    return frontend_cases, other_cases

def analyze_data_source_evidence():
    """分析推理过程中的数据来源证据"""
    
    with open('test_threshold_comparison.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("\n🔍 **数据来源证据分析**:")
    print("=" * 60)
    
    total_steps = 0
    trace_evidence_steps = 0
    log_evidence_steps = 0
    metric_evidence_steps = 0
    
    for case in results:
        uuid = case['uuid']
        reasoning_trace = case.get('reasoning_trace', [])
        
        for step in reasoning_trace:
            total_steps += 1
            observation = step.get('observation', '').lower()
            
            # 检查具体的数据证据
            if re.search(r'\d+\.?\d*\s*ms|latency|duration|trace', observation):
                trace_evidence_steps += 1
            
            if re.search(r'log|error|critical|fatal|warn', observation):
                log_evidence_steps += 1
                
            if re.search(r'cpu|memory|metric|resource|error_ratio|rrt', observation):
                metric_evidence_steps += 1
    
    print(f"总推理步骤: {total_steps}")
    print(f"包含Trace证据的步骤: {trace_evidence_steps} ({trace_evidence_steps/total_steps*100:.1f}%)")
    print(f"包含Log证据的步骤: {log_evidence_steps} ({log_evidence_steps/total_steps*100:.1f}%)")
    print(f"包含Metric证据的步骤: {metric_evidence_steps} ({metric_evidence_steps/total_steps*100:.1f}%)")

if __name__ == "__main__":
    frontend_cases, other_cases = analyze_batch_test_logs()
    analyze_data_source_evidence()
