name: Bug Report
description: Report a bug
type: "bug"

body:
  - type: textarea
    attributes:
      label: What happened?
      description: Please provide as much information as possible, this helps us address the issue.
    validations:
      required: true
  - type: textarea
    attributes:
      label: What did you expect to happen?
    validations:
      required: true
  - type: textarea
    attributes:
      label: How can we reproduce it (as minimally and precisely as possible)?
      description: Please provide steps to reproduce. Provide code that can be run if possible.
    validations:
      required: true
  - type: input
    attributes:
      label: AutoGen version
      description: What version or commit of the library was used
    validations:
      required: true
  - type: dropdown
    attributes:
      label: Which package was this bug in
      options:
        - Core
        - AgentChat
        - Extensions
        - AutoGen Studio
        - Magentic One
        - AutoGen Bench
        - Other
    validations:
      required: true
  - type: input
    attributes:
      label: Model used
      description: If a model was used, please describe it here, indicating whether it is a local model or a cloud-hosted model
      placeholder: gpt-4, mistral-7B etc
  - type: input
    attributes:
      label: Python version
  - type: input
    attributes:
      label: Operating system
  - type: textarea
    attributes:
      label: Any additional info you think would be helpful for fixing this bug
