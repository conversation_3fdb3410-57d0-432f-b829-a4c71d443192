name: Remove awaiting-op-response label if op responded
on:
  issue_comment:
    types: [created]
jobs:
  label_issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - run: gh issue edit "$NUMBER" --remove-label "$LABELS"
        if: ${{ github.event.comment.user.login == github.event.issue.user.login && contains(github.event.issue.labels.*.name, 'awaiting-op-response') }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
          NUMBER: ${{ github.event.issue.number }}
          LABELS: awaiting-op-response
