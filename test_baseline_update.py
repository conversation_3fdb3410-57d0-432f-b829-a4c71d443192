#!/usr/bin/env python3
"""
测试新基线数据的加载和使用
"""

import sys
import os
import json
import logging

# 添加项目路径
sys.path.append('/home/<USER>/aiopsagent/autogen-0.4.4')

from aiops_diagnosis.autogen_agents.simple_metric_agent import APMServiceBaselineManager, SimpleMetricAgent

def test_baseline_loading():
    """测试基线数据加载"""
    print("🔧 测试基线数据加载...")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 测试基线管理器
    baseline_config_path = "/home/<USER>/aiopsagent/autogen-0.4.4/baseline.json"
    
    print(f"📁 基线配置文件路径: {baseline_config_path}")
    print(f"📁 文件是否存在: {os.path.exists(baseline_config_path)}")
    
    # 创建基线管理器
    baseline_manager = APMServiceBaselineManager(baseline_config_path)
    
    # 检查加载的数据
    print(f"\n📊 加载的服务数量: {len(baseline_manager.service_baselines)}")
    print(f"📊 加载的Pod数量: {len(baseline_manager.pod_baselines)}")
    
    # 显示部分服务基线数据
    print("\n🎯 服务基线数据示例:")
    for i, (service_name, baseline) in enumerate(baseline_manager.service_baselines.items()):
        if i < 5:  # 只显示前5个
            print(f"  {service_name}: rrt={baseline['rrt']:.1f}ms, error={baseline['error_ratio']:.2f}%")
    
    # 显示部分Pod基线数据
    print("\n🎯 Pod基线数据示例:")
    for i, (pod_name, baseline) in enumerate(baseline_manager.pod_baselines.items()):
        if i < 5:  # 只显示前5个
            print(f"  {pod_name}: rrt={baseline['rrt']:.1f}ms, error={baseline['error_ratio']:.2f}%")
    
    # 测试基线查找功能
    print("\n🔍 测试基线查找功能:")
    test_objects = ['adservice-0', 'cartservice-1', 'frontend-2', 'paymentservice']
    
    for obj_name in test_objects:
        baseline = baseline_manager.get_baseline_for_object(obj_name)
        if baseline:
            print(f"  ✅ {obj_name}: rrt={baseline['rrt']:.1f}ms, error={baseline['error_ratio']:.2f}%")
        else:
            print(f"  ❌ {obj_name}: 未找到基线数据")
    
    # 测试阈值计算
    print("\n🎯 测试阈值计算:")
    test_pod = 'adservice-0'
    thresholds = baseline_manager.calculate_thresholds_for_object(test_pod)
    if thresholds:
        print(f"  {test_pod} 警告阈值:")
        print(f"    rrt: {thresholds['warning']['rrt']:.1f}ms")
        print(f"    error_ratio: {thresholds['warning']['error_ratio']:.2f}%")
        print(f"  {test_pod} 严重阈值:")
        print(f"    rrt: {thresholds['critical']['rrt']:.1f}ms")
        print(f"    error_ratio: {thresholds['critical']['error_ratio']:.2f}%")
    
    # 测试异常判断
    print("\n🚨 测试异常判断:")
    test_cases = [
        ('adservice-0', 'rrt', 20000),  # 应该是警告
        ('adservice-0', 'rrt', 30000),  # 应该是严重
        ('adservice-0', 'error_ratio', 10),  # 应该是警告
        ('cartservice-1', 'rrt', 6000),  # 应该是正常
    ]
    
    for obj_name, metric, value in test_cases:
        is_anomaly, severity, deviation = baseline_manager.is_anomaly_for_object(obj_name, metric, value)
        status = f"{severity} (偏差: {deviation:.2f}x)" if is_anomaly else "正常"
        print(f"  {obj_name}.{metric}={value}: {status}")

def test_metric_agent_with_baseline():
    """测试MetricAgent使用新基线数据"""
    print("\n🔧 测试MetricAgent使用新基线数据...")
    
    baseline_config_path = "/home/<USER>/aiopsagent/autogen-0.4.4/baseline.json"
    
    # 创建MetricAgent
    agent = SimpleMetricAgent(
        enable_llm=False,  # 关闭LLM以加快测试
        baseline_config_path=baseline_config_path
    )
    
    print(f"✅ MetricAgent创建成功")
    print(f"📊 基线管理器中的服务数量: {len(agent.baseline_manager.service_baselines)}")
    print(f"📊 基线管理器中的Pod数量: {len(agent.baseline_manager.pod_baselines)}")
    
    # 测试策略管理器
    should_use_baseline = agent.strategy_manager.should_use_baseline('apm', 'adservice-0')
    print(f"🎯 APM应该使用基线检测: {should_use_baseline}")

def compare_with_old_baseline():
    """对比新旧基线数据的差异"""
    print("\n🔍 对比新旧基线数据差异...")
    
    # 加载新基线数据
    with open("/home/<USER>/aiopsagent/autogen-0.4.4/baseline.json", 'r') as f:
        new_baseline = json.load(f)
    
    # 旧基线数据（从代码中提取）
    old_service_baselines = {
        'adservice': {'rrt': 8029.88, 'error_ratio': 2.73, 'timeout': 3.0},
        'cartservice': {'rrt': 5475.04, 'error_ratio': 2.16, 'timeout': 11.0},
        'paymentservice': {'rrt': 17717.8, 'error_ratio': 0.0, 'timeout': 0.0},
    }
    
    print("📊 服务基线数据对比:")
    for service in old_service_baselines:
        if service in new_baseline['services']:
            old = old_service_baselines[service]
            new = new_baseline['services'][service]
            
            print(f"  {service}:")
            print(f"    rrt: {old['rrt']:.1f} -> {new['rrt']:.1f} (变化: {((new['rrt']-old['rrt'])/old['rrt']*100):+.1f}%)")
            print(f"    error_ratio: {old['error_ratio']:.2f} -> {new['error_ratio']:.2f}")
    
    # 检查新增的服务
    new_services = set(new_baseline['services'].keys()) - set(old_service_baselines.keys())
    if new_services:
        print(f"\n🆕 新增服务: {list(new_services)}")
    
    print(f"\n📊 新基线数据包含 {len(new_baseline['pods'])} 个Pod和 {len(new_baseline['services'])} 个服务")

if __name__ == "__main__":
    print("🚀 开始测试新基线数据...")
    
    try:
        test_baseline_loading()
        test_metric_agent_with_baseline()
        compare_with_old_baseline()
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
