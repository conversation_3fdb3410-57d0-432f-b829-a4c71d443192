#!/usr/bin/env python3
"""
脚本：将 seconde_output.json 转换为 jsonl 格式
参考 result.jsonl 的格式，将 JSON 数组转换为每行一个 JSON 对象的格式
"""

import json
import sys
from pathlib import Path

def standardize_component_name(component: str) -> str:
    """标准化组件名称，确保符合AIOps规则要求 - 使用动态组件管理器"""

    if not component or component == "unknown":
        return ""

    # 🔧 使用全局组件管理器进行动态验证和标准化
    try:
        from aiops_diagnosis.knowledge.global_component_manager import get_global_component_manager

        manager = get_global_component_manager()
        normalized = manager.normalize_component_name(component)

        if normalized:
            return normalized

        # 如果无法标准化，返回原始组件名
        return component

    except Exception as e:
        print(f"⚠️ 组件标准化失败: {e}")
        return component

    # 如果是未知组件，返回空字符串
    print(f"⚠️  警告：未知组件名称 '{component}' 已清空")
    return ""

def convert_json_to_jsonl(input_file: str, output_file: str):
    """
    将 JSON 数组文件转换为 JSONL 格式

    Args:
        input_file: 输入的 JSON 文件路径 (seconde_output.json)
        output_file: 输出的 JSONL 文件路径
    """

    try:
        # 读取输入的 JSON 文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"✅ 成功读取 {input_file}")
        print(f"📊 共找到 {len(data)} 条记录")

        # 统计组件标准化情况
        component_stats = {}
        standardized_count = 0

        # 写入 JSONL 格式文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in data:
                # 标准化组件名称
                # original_component = item.get("component", "")
                # standardized_component = standardize_component_name(original_component)

                # 统计组件使用情况
                # if original_component != standardized_component:
                #     standardized_count += 1
                #     if original_component not in component_stats:
                #         component_stats[original_component] = 0
                #     component_stats[original_component] += 1

                # 确保每个对象都有必要的字段
                jsonl_item = {
                    "component": item.get("component", ""),
                    "uuid": item.get("uuid", ""),
                    "reason": item.get("reason", ""),
                    "reasoning_trace": item.get("reasoning_trace", [])
                }

                # 写入一行 JSON
                f.write(json.dumps(jsonl_item, ensure_ascii=False) + '\n')

        print(f"✅ 成功转换为 {output_file}")
        print(f"📝 格式：每行一个 JSON 对象")

        # 显示组件标准化统计
        if standardized_count > 0:
            print(f"\n🔧 组件标准化统计：")
            print(f"   共修复了 {standardized_count} 个非标准组件名")
            for original, count in component_stats.items():
                print(f"   '{original}' → 标准化 ({count} 次)")

        # 验证输出文件
        verify_jsonl_file(output_file)

    except FileNotFoundError:
        print(f"❌ 错误：找不到输入文件 {input_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 错误：JSON 解析失败 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 错误：转换失败 - {e}")
        sys.exit(1)

def verify_jsonl_file(jsonl_file: str):
    """验证生成的 JSONL 文件格式是否正确"""
    
    try:
        line_count = 0
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:  # 跳过空行
                    json.loads(line)  # 验证每行都是有效的 JSON
                    line_count += 1
        
        print(f"✅ 验证通过：{line_count} 行有效的 JSON 对象")
        
        # 显示前几行作为示例
        print("\n📋 前3行示例：")
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 3:
                    break
                print(f"  {i+1}: {line.strip()}")
                
    except Exception as e:
        print(f"❌ 验证失败：{e}")

def main():
    """主函数"""
    
    # 设置文件路径
    base_dir = Path(__file__).parent
    input_file = base_dir / "08010150-v3.json"
    output_file = base_dir / "08010150-v3.jsonl"
    
    print("🔄 JSON 到 JSONL 转换工具")
    print("=" * 50)
    print(f"📁 输入文件：{input_file}")
    print(f"📁 输出文件：{output_file}")
    print()
    
    # 检查输入文件是否存在
    if not input_file.exists():
        print(f"❌ 错误：输入文件不存在 {input_file}")
        sys.exit(1)
    
    # 执行转换
    convert_json_to_jsonl(str(input_file), str(output_file))
    
    print("\n🎉 转换完成！")
    print(f"📊 输出文件：{output_file}")
    print(f"📏 文件大小：{output_file.stat().st_size} 字节")

if __name__ == "__main__":
    main()
