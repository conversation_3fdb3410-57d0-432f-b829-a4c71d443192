# AIOps 智能故障诊断系统依赖

# 核心 AutoGen 框架
autogen-core>=0.4.4
autogen-ext>=0.4.4

# LLM 客户端支持
openai>=1.0.0
anthropic>=0.25.0

# 数据处理
pandas>=2.0.0
pyarrow>=10.0.0
numpy>=1.24.0

# 异步处理
asyncio
aiofiles
asyncio-atexit

# 配置管理
pydantic>=2.10.0,<3.0.0
python-dotenv>=1.0.0
PyYAML>=6.0

# 日志和监控
structlog>=23.0.0
opentelemetry-api>=1.27.0
opentelemetry-sdk>=1.27.0

# 网络请求
requests>=2.31.0
httpx>=0.25.0

# 数据验证
jsonschema>=4.0.0
jsonref>=1.1.0

# 工具库
typing-extensions>=4.0.0
protobuf>=4.25.1
pillow>=11.0.0

# 开发和测试
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0

# 可选依赖 - 高级功能
# 如需使用特定功能，请取消注释相应行

# 图像处理 (如果需要处理图像数据)
# opencv-python>=4.8.0

# 机器学习 (如果需要传统ML算法)
# scikit-learn>=1.3.0

# 可视化 (如果需要生成图表)
# matplotlib>=3.7.0
# seaborn>=0.12.0

# 数据库连接 (如果需要直接连接数据库)
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0

# 消息队列 (如果需要异步消息处理)
# celery>=5.3.0
# redis>=4.6.0

# 监控和追踪 (生产环境推荐)
# prometheus-client>=0.17.0
# jaeger-client>=4.8.0
