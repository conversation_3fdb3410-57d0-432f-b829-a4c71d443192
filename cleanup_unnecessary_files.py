#!/usr/bin/env python3
"""
清理不需要的文件脚本
基于a2a_batch_diagnosis.py的依赖分析，删除不需要的文件
"""

import os
import shutil
from pathlib import Path

# 不需要的文件列表 (基于依赖分析)
unnecessary_files = [
    # 测试和演示文件
    "analyze_architecture_aware_results.py",
    "autogen_batch_diagnosis.py", 
    "basic_a2a_test.py",
    "check_syntax.py",
    "convert_to_jsonl.py",
    "debug_trace_analysis.py",
    "demo_architecture_aware_system.py",
    "demo_multi_agent_simple.py", 
    "demo_multi_agent_system.py",
    "fast_fault_detector.py",
    "file_tree_printer.py",
    "optimized_a2a_batch_diagnosis.py",
    "run_batch_diagnosis.py",
    "simple_component_test.py",
    
    # 所有test_开头的文件
    "test_a2a_aiops_system.py",
    "test_clean_logging.py",
    "test_component_validation_fix.py",
    "test_data_loading_a2a.py",
    "test_enhanced_trace_detection.py",
    "test_global_component_optimization.py",
    "test_import.py",
    "test_llm_config.py",
    "test_llm_reasoning.py",
    "test_llm_simple.py",
    "test_log_filtering.py",
    "test_metric_agent_component_fix.py",
    "test_new_reasoning_agent.py",
    "test_new_reasoning_integration.py",
    "test_no_sampling_trace_analysis.py",
    "test_optimized_metric_agent.py",
    "test_true_a2a_system.py",
    
    # 输出和结果文件
    "batch_results.json",
    "fisrt_output.json",
    "fix_component_output.jsonl",
    "fix_reason_output.json",
    "intelligent_aiops_test.log",
    "intelligent_results.json",
    "log_agent_output_example.json",
    "multi_round_test_output.json",
    "new_input_output.json",
    "new_input_output.jsonl",
    "output.json",
    "production_results.json",
    "result.jsonl",
    "seconde_output.json",
    "simple_results.json",
    "three_output.jsonl",
    
    # 所有test_开头的JSON文件
    "test_all_fixed.json",
    "test_anomaly_info_results.json",
    "test_both_fixes.json",
    "test_clean_logs.json",
    "test_clean_output.json",
    "test_concurrent_output.json",
    "test_data_missing_handling.json",
    "test_debug_traceid.json",
    "test_diversity.json",
    "test_diversity_output.json",
    "test_enhanced_trace_detection.json",
    "test_final_fixed.json",
    "test_final_fixed_results.json",
    "test_final_results.json",
    "test_final_verification.json",
    "test_fixed_components.json",
    "test_fixed_errors.json",
    "test_fixed_final_results.json",
    "test_fixed_no_bias_results.json",
    "test_fixed_results.json",
    "test_fixed_system.json",
    "test_improved_system.json",
    "test_llm_fixed.json",
    "test_llm_reasoning_output.json",
    "test_metric_agent_fixed.json",
    "test_metric_optimization.json",
    "test_no_llm_logs.json",
    "test_no_sampling.json",
    "test_no_sampling_fixed.json",
    "test_raw_data_results.json",
    "test_results.json",
    "test_sensitive_threshold_results.json",
    
    # verify_output开头的文件 (保留verify.json)
    "verify_output.json",
    "verify_output_analysis.json",
    "verify_output_debug.json",
    "verify_output_debug2.json",
    "verify_output_debug_final.json",
    "verify_output_emergency.json",
    "verify_output_enhanced.json",
    "verify_output_enhanced_fixed.json",
    "verify_output_enhanced_logic.json",
    "verify_output_enhanced_trace.json",
    "verify_output_final.json",
    "verify_output_final_fix.json",
    "verify_output_final_success.json",
    "verify_output_fixed.json",
    "verify_output_fixed_final.json",
    "verify_output_fixed_reasoning.json",
    "verify_output_optimized.json",
    "verify_output_standardized.json",
    "verify_output_test.json",
    
    # aiops_diagnosis中不需要的文件
    "aiops_diagnosis/batch_processor.py",
    "aiops_diagnosis/complete_diagnosis_results.json",
    "aiops_diagnosis/layered_batch_output.json",
    "aiops_diagnosis/knowledge/intelligent_analysis.py",
    
    # intelligent_aiops_system目录 (之前创建的无效系统)
    "intelligent_aiops_system",
]

# 不需要的目录
unnecessary_dirs = [
    "intelligent_aiops_system",
    "__pycache__",
]

def cleanup_files():
    """清理不需要的文件"""
    deleted_files = []
    deleted_dirs = []
    
    print("🧹 开始清理不需要的文件...")
    
    # 删除文件
    for file_path in unnecessary_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                deleted_files.append(file_path)
                print(f"✅ 删除文件: {file_path}")
            except Exception as e:
                print(f"❌ 删除文件失败 {file_path}: {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    # 删除目录
    for dir_path in unnecessary_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            try:
                shutil.rmtree(dir_path)
                deleted_dirs.append(dir_path)
                print(f"✅ 删除目录: {dir_path}")
            except Exception as e:
                print(f"❌ 删除目录失败 {dir_path}: {e}")
    
    # 清理空的__pycache__目录
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                dir_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(dir_path)
                    deleted_dirs.append(dir_path)
                    print(f"✅ 删除缓存目录: {dir_path}")
                except Exception as e:
                    print(f"❌ 删除缓存目录失败 {dir_path}: {e}")
    
    print(f"\n📊 清理完成:")
    print(f"   删除文件: {len(deleted_files)} 个")
    print(f"   删除目录: {len(deleted_dirs)} 个")
    
    if deleted_files:
        print(f"\n🗑️ 已删除的文件:")
        for file in deleted_files:
            print(f"   - {file}")
    
    if deleted_dirs:
        print(f"\n🗑️ 已删除的目录:")
        for dir in deleted_dirs:
            print(f"   - {dir}")

def show_remaining_core_files():
    """显示保留的核心文件"""
    core_files = [
        "a2a_batch_diagnosis.py",
        "aiops_diagnosis/optimized_autogen_system.py",
        "aiops_diagnosis/autogen_agents/",
        "aiops_diagnosis/config/",
        "aiops_diagnosis/utils/",
        "aiops_diagnosis/knowledge/",
        "verify.json",
        "requirements.txt",
        "README.md"
    ]
    
    print(f"\n✅ 保留的核心文件和目录:")
    for item in core_files:
        if os.path.exists(item):
            print(f"   ✓ {item}")
        else:
            print(f"   ❌ {item} (不存在)")

if __name__ == "__main__":
    print("🚀 AIOps系统文件清理工具")
    print("=" * 50)
    
    # 确认操作
    response = input("⚠️ 这将删除大量文件，确定要继续吗? (y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        exit(0)
    
    # 执行清理
    cleanup_files()
    
    # 显示保留的文件
    show_remaining_core_files()
    
    print("\n🎉 文件清理完成！")
    print("现在项目只保留a2a_batch_diagnosis.py运行所需的核心文件。")
